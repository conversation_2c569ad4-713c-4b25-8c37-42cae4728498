#!/usr/bin/env python3
"""
专业的YOLOv5模型剪枝工具
基于 https://github.com/midasklr/yolov5prune 项目
使用BN层稀疏训练 + 结构化剪枝的方法
"""

import argparse
import torch
import torch.nn as nn
import numpy as np
import yaml
import os
import sys
from pathlib import Path
from copy import deepcopy

# 添加yolov5路径
sys.path.append('yolov5')

from yolov5.utils.torch_utils import select_device
from yolov5.utils.general import check_dataset, check_yaml, colorstr, LOGGER
from yolov5.models.yolo import Model
from yolov5.models.common import Bottleneck

def gather_bn_weights(module_list):
    """收集所有BN层的权重"""
    size_list = [idx.weight.data.shape[0] for idx in module_list.values()]
    bn_weights = torch.zeros(sum(size_list))
    index = 0
    for i, idx in enumerate(module_list.values()):
        size = size_list[i]
        bn_weights[index:(index + size)] = idx.weight.data.abs().clone()
        index += size
    return bn_weights

def obtain_bn_mask(bn_module, thre):
    """根据阈值获取BN层的掩码"""
    if torch.cuda.is_available():
        thre = thre.cuda()
    mask = bn_module.weight.data.abs().ge(thre).float()
    return mask

def load_model(weights, device):
    """加载YOLOv5模型"""
    print(f"加载模型: {weights}")
    
    # 加载模型字典
    ckpt = torch.load(weights, map_location=device)
    
    if isinstance(ckpt, dict):
        if 'ema' in ckpt and ckpt['ema'] is not None:
            model = ckpt['ema']
            print("使用EMA模型")
        elif 'model' in ckpt and ckpt['model'] is not None:
            model = ckpt['model']
            print("使用普通模型")
        else:
            raise ValueError("无法从模型字典中找到模型")
    else:
        model = ckpt
        ckpt = {'model': model}
    
    # 转换为float32并移到设备
    model = model.float().to(device)
    
    print(f"模型类型: {type(model)}")
    print(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    return model, ckpt

def analyze_model_structure(model):
    """分析模型结构，找出可剪枝的BN层"""
    model_list = {}
    ignore_bn_list = []
    
    # 找出需要忽略的BN层（shortcut连接相关）
    for name, layer in model.named_modules():
        if isinstance(layer, Bottleneck):
            if layer.add:  # 有shortcut连接
                ignore_bn_list.append(name.rsplit(".", 2)[0] + ".cv1.bn")
                ignore_bn_list.append(name + '.cv1.bn')
                ignore_bn_list.append(name + '.cv2.bn')
    
    # 收集所有BN层
    for name, layer in model.named_modules():
        if isinstance(layer, torch.nn.BatchNorm2d):
            if name not in ignore_bn_list:
                model_list[name] = layer
    
    print(f"总BN层数: {len(list(model.named_modules()))}")
    print(f"可剪枝BN层数: {len(model_list)}")
    print(f"忽略的BN层数: {len(ignore_bn_list)}")
    
    return model_list, ignore_bn_list

def calculate_prune_threshold(model_list, percent):
    """计算剪枝阈值"""
    # 收集所有BN权重
    bn_weights = gather_bn_weights(model_list)
    sorted_bn = torch.sort(bn_weights)[0]
    
    # 计算最高阈值（避免剪掉所有通道）
    highest_thre = []
    for bnlayer in model_list.values():
        highest_thre.append(bnlayer.weight.data.abs().max().item())
    highest_thre = min(highest_thre)
    
    # 计算阈值上限对应的百分比
    percent_limit = (sorted_bn == highest_thre).nonzero()[0, 0].item() / len(bn_weights)
    
    print(f'建议的Gamma阈值应小于 {highest_thre:.4f}')
    print(f'对应的剪枝比例是 {percent_limit:.3f}')
    
    if percent >= percent_limit:
        print(f"警告: 剪枝比例 {percent} 可能过高，建议小于 {percent_limit:.3f}")
    
    # 计算实际阈值
    thre_index = int(len(sorted_bn) * percent)
    thre = sorted_bn[thre_index]
    
    print(f'小于 {thre:.4f} 的Gamma值将被设为零!')
    
    return thre

def create_pruned_model_config(original_model, cfg_path):
    """创建剪枝后的模型配置"""
    with open(cfg_path, encoding='ascii', errors='ignore') as f:
        model_yamls = yaml.safe_load(f)
    
    # 创建剪枝后的配置
    pruned_yaml = {}
    pruned_yaml["nc"] = original_model.model[-1].nc
    pruned_yaml["depth_multiple"] = model_yamls["depth_multiple"]
    pruned_yaml["width_multiple"] = model_yamls["width_multiple"]
    pruned_yaml["anchors"] = model_yamls["anchors"]
    
    # 简化的backbone和head配置（适用于YOLOv5s）
    pruned_yaml["backbone"] = [
        [-1, 1, "Conv", [64, 6, 2, 2]],  # 0-P1/2
        [-1, 1, "Conv", [128, 3, 2]],    # 1-P2/4
        [-1, 3, "C3", [128]],
        [-1, 1, "Conv", [256, 3, 2]],    # 3-P3/8
        [-1, 6, "C3", [256]],
        [-1, 1, "Conv", [512, 3, 2]],    # 5-P4/16
        [-1, 9, "C3", [512]],
        [-1, 1, "Conv", [1024, 3, 2]],   # 7-P5/32
        [-1, 3, "C3", [1024]],
        [-1, 1, "SPPF", [1024, 5]],      # 9
    ]
    
    pruned_yaml["head"] = [
        [-1, 1, "Conv", [512, 1, 1]],
        [-1, 1, "nn.Upsample", [None, 2, 'nearest']],
        [[-1, 6], 1, "Concat", [1]],     # cat backbone P4
        [-1, 3, "C3", [512, False]],     # 13
        [-1, 1, "Conv", [256, 1, 1]],
        [-1, 1, "nn.Upsample", [None, 2, 'nearest']],
        [[-1, 4], 1, "Concat", [1]],     # cat backbone P3
        [-1, 3, "C3", [256, False]],     # 17 (P3/8-small)
        [-1, 1, "Conv", [256, 3, 2]],
        [[-1, 14], 1, "Concat", [1]],    # cat head P4
        [-1, 3, "C3", [512, False]],     # 20 (P4/16-medium)
        [-1, 1, "Conv", [512, 3, 2]],
        [[-1, 10], 1, "Concat", [1]],    # cat head P5
        [-1, 3, "C3", [1024, False]],    # 23 (P5/32-large)
        [[17, 20, 23], 1, "Detect", [pruned_yaml["nc"], pruned_yaml["anchors"]]],  # Detect(P3, P4, P5)
    ]
    
    return pruned_yaml

def prune_model(model, model_list, ignore_bn_list, thre, cfg_path):
    """执行模型剪枝"""
    print("=" * 94)
    print(f"|{'layer name':<25}{'|':<10}{'origin channels':<20}{'|':<10}{'remaining channels':<20}|")
    print("=" * 94)
    
    # 创建掩码字典
    maskbndict = {}
    remain_num = 0
    
    # 为每个BN层创建掩码
    for bnname, bnlayer in model.named_modules():
        if isinstance(bnlayer, nn.BatchNorm2d):
            if bnname in ignore_bn_list:
                # 忽略的层保持所有通道
                mask = torch.ones(bnlayer.weight.data.size())
                if torch.cuda.is_available():
                    mask = mask.cuda()
            else:
                # 根据阈值创建掩码
                mask = obtain_bn_mask(bnlayer, thre)
            
            maskbndict[bnname] = mask
            remain_num += int(mask.sum())
            
            # 应用掩码
            bnlayer.weight.data.mul_(mask)
            bnlayer.bias.data.mul_(mask)
            
            print(f"|{bnname:<25}{'|':<10}{bnlayer.weight.data.size()[0]:<20}{'|':<10}{int(mask.sum()):<20}|")
            
            # 检查是否有通道被完全剪掉
            if int(mask.sum()) == 0:
                raise ValueError(f"层 {bnname} 的所有通道都被剪掉了！请降低剪枝比例。")
    
    print("=" * 94)
    print(f"总剩余通道数: {remain_num}")
    
    return maskbndict

def save_pruned_model(model, maskbndict, save_path):
    """保存剪枝后的模型"""
    # 创建保存字典
    save_dict = {
        'model': model,
        'maskbndict': maskbndict,
        'epoch': -1,
        'best_fitness': 0.0,
        'optimizer': None,
        'ema': None,
        'updates': 0
    }
    
    torch.save(save_dict, save_path)
    print(f"剪枝后的模型已保存到: {save_path}")

def main():
    parser = argparse.ArgumentParser(description='专业的YOLOv5模型剪枝工具')
    parser.add_argument('--weights', type=str, default='yolov5/best_22.pt', help='模型权重文件路径')
    parser.add_argument('--cfg', type=str, default='yolov5/models/yolov5s.yaml', help='模型配置文件路径')
    parser.add_argument('--data', type=str, default='yolov5/data/coco128.yaml', help='数据集配置文件路径')
    parser.add_argument('--percent', type=float, default=0.3, help='剪枝比例 (0.0-1.0)')
    parser.add_argument('--device', type=str, default='cpu', help='设备 (cpu 或 cuda:0 等)')
    parser.add_argument('--save-path', type=str, default='pruned_yolo_professional.pt', help='剪枝后模型保存路径')
    
    args = parser.parse_args()
    
    # 选择设备
    device = select_device(args.device)
    print(f"使用设备: {device}")
    
    # 检查文件
    args.data = check_yaml(args.data)
    args.cfg = check_yaml(args.cfg)
    
    # 加载模型
    model, ckpt = load_model(args.weights, device)
    model.eval()
    
    # 分析模型结构
    model_list, ignore_bn_list = analyze_model_structure(model)
    
    if len(model_list) == 0:
        print("错误: 没有找到可剪枝的BN层!")
        return
    
    # 计算剪枝阈值
    thre = calculate_prune_threshold(model_list, args.percent)
    
    # 执行剪枝
    maskbndict = prune_model(model, model_list, ignore_bn_list, thre, args.cfg)
    
    # 保存剪枝后的模型
    save_pruned_model(model, maskbndict, args.save_path)
    
    # 显示剪枝结果
    original_params = sum(p.numel() for p in model.parameters())
    remaining_channels = sum(int(mask.sum()) for mask in maskbndict.values())
    total_channels = sum(mask.numel() for mask in maskbndict.values())
    
    print(f"\n剪枝完成!")
    print(f"原始参数量: {original_params:,}")
    print(f"剩余通道比例: {remaining_channels/total_channels:.2%}")
    print(f"剪枝比例: {args.percent:.1%}")
    print(f"剪枝后模型保存在: {args.save_path}")
    
    print(f"\n下一步:")
    print(f"1. 使用剪枝后的模型进行微调训练")
    print(f"2. 使用 detectpruned.py 进行推理测试")

if __name__ == '__main__':
    main()
