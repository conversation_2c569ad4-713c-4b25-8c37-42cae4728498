# pip install -r requirements.txt

# base ----------------------------------------

matplotlib>=3.2.2
numpy>=1.18.5
opencv-python>=4.1.2
Pillow>=7.1.2
PyYAML>=5.3.1
requests>=2.23.0
scipy>=1.4.1
torch>=1.7.0
torchvision>=0.8.1
tqdm>=4.41.0

# plotting ------------------------------------

pandas>=1.1.4
seaborn>=0.11.0

# deep_sort -----------------------------------

easydict

# torchreid

<PERSON>on
h5py
six
tb-nightly
future
yacs
gdown
flake8
yapf
isort==4.3.21
imageio