# YOLO模型剪枝实验报告

**实验ID**: final_pruning_20250604_215658  
**实验时间**: 2025-06-04T21:56:58.698376 - 2025-06-04T21:57:26.936351  
**原始模型**: yolov5/best_22.pt  
**测试设备**: cpu  

## 📊 实验结果汇总

| 剪枝比例 | 实际比例 | FPS变化 | 时间变化 | 检测变化 | 模型路径 |
|---------|---------|---------|----------|----------|----------|
| 15.0% | 15.0% | -15.8% | +18.7% | -4.5 | pruned_yolo_15percent.pt |
| 20.0% | 20.0% | -1.2% | +1.2% | -4.5 | pruned_yolo_20percent.pt |
| 25.0% | 25.0% | -0.8% | +0.8% | -4.5 | pruned_yolo_25percent.pt |

## 📈 详细性能分析

### 1. 剪枝比例 15.0%

**剪枝信息**:
- 目标比例: 15.0%
- 实际比例: 15.0%
- 剪枝阈值: 0.703125
- 剪枝通道: 1,429 / 9,504

**推理性能对比**:
- 原始FPS: 19.38
- 剪枝后FPS: 16.32
- FPS变化: -15.8%
- 原始推理时间: 51.60ms
- 剪枝后推理时间: 61.27ms
- 时间变化: +18.7%

**检测能力对比**:
- 原始检测: 4.53 个工人/图像
- 剪枝后检测: 0.00 个工人/图像
- 检测变化: -4.53 个工人/图像

### 2. 剪枝比例 20.0%

**剪枝信息**:
- 目标比例: 20.0%
- 实际比例: 20.0%
- 剪枝阈值: 0.757324
- 剪枝通道: 1,901 / 9,504

**推理性能对比**:
- 原始FPS: 16.57
- 剪枝后FPS: 16.38
- FPS变化: -1.2%
- 原始推理时间: 60.36ms
- 剪枝后推理时间: 61.06ms
- 时间变化: +1.2%

**检测能力对比**:
- 原始检测: 4.53 个工人/图像
- 剪枝后检测: 0.00 个工人/图像
- 检测变化: -4.53 个工人/图像

### 3. 剪枝比例 25.0%

**剪枝信息**:
- 目标比例: 25.0%
- 实际比例: 25.0%
- 剪枝阈值: 0.806152
- 剪枝通道: 2,380 / 9,504

**推理性能对比**:
- 原始FPS: 16.73
- 剪枝后FPS: 16.60
- FPS变化: -0.8%
- 原始推理时间: 59.78ms
- 剪枝后推理时间: 60.25ms
- 时间变化: +0.8%

**检测能力对比**:
- 原始检测: 4.53 个工人/图像
- 剪枝后检测: 0.00 个工人/图像
- 检测变化: -4.53 个工人/图像

## 📁 生成的文件

本次实验生成的文件:
- `pruned_yolo_15percent.pt` - 15.0%剪枝模型
- `pruned_yolo_20percent.pt` - 20.0%剪枝模型
- `pruned_yolo_25percent.pt` - 25.0%剪枝模型
- `experiment_results.json` - 详细实验数据
- `performance_comparison.png` - 性能对比图表
- `experiment_report.html` - HTML格式报告
