import sys
import os
import argparse
import torch
import numpy as np
from pathlib import Path

# 添加YOLOv5路径
sys.path.insert(0, './yolov5')

from yolov5.models.yolo import Model
from yolov5.utils.torch_utils import select_device

def load_model(weights, device):
    """
    加载YOLOv5模型
    
    参数:
        weights (str): 模型权重文件路径
        device (str): 设备 ('cpu' 或 'cuda:0' 等)
    
    返回:
        model: 加载的模型
    """
    model = torch.load(weights, map_location=device)
    if isinstance(model, dict):
        model = model['ema' if model.get('ema') else 'model']  # 提取模型
    
    # 确保是PyTorch模型
    if isinstance(model, torch.nn.Module):
        return model
    else:
        # 如果是state_dict，需要先加载模型结构
        print("加载模型结构...")
        yaml_file = Path(weights).with_suffix('.yaml')
        if not yaml_file.exists():
            yaml_file = Path('yolov5/models/yolov5s.yaml')  # 默认使用yolov5s结构
        
        model_structure = Model(yaml_file, ch=3, nc=80)  # 创建模型结构
        model_structure.load_state_dict(model)  # 加载权重
        return model_structure

def analyze_model(model):
    """
    分析模型结构和参数量
    
    参数:
        model: PyTorch模型
    """
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型总参数量: {total_params:,}")
    
    # 分析每层参数
    print("\n各层参数分布:")
    for name, module in model.named_modules():
        if isinstance(module, torch.nn.Conv2d) or isinstance(module, torch.nn.Linear):
            params = sum(p.numel() for p in module.parameters())
            print(f"{name}: {params:,} 参数")

def quantize_model_static(model, save_path='quantized_model.pt'):
    """
    使用静态量化压缩模型
    
    参数:
        model: PyTorch模型
        save_path (str): 保存路径
    
    返回:
        quantized_model: 量化后的模型
    """
    print("开始静态量化...")
    
    # 准备量化配置
    model.eval()
    
    # 静态量化
    quantized_model = torch.quantization.quantize_static(
        model,
        {torch.nn.Conv2d, torch.nn.Linear},
        torch.quantization.default_qconfig
    )
    
    # 保存量化后的模型
    torch.save(quantized_model, save_path)
    print(f"量化后的模型已保存到: {save_path}")
    
    # 分析量化后的模型
    analyze_model(quantized_model)
    
    return quantized_model

def quantize_model_dynamic(model, save_path='quantized_dynamic_model.pt'):
    """
    使用动态量化压缩模型
    
    参数:
        model: PyTorch模型
        save_path (str): 保存路径
    
    返回:
        quantized_model: 量化后的模型
    """
    print("开始动态量化...")
    
    # 准备量化配置
    model.eval()
    
    # 动态量化
    quantized_model = torch.quantization.quantize_dynamic(
        model,
        {torch.nn.Linear, torch.nn.LSTM, torch.nn.Conv2d},
        dtype=torch.qint8
    )
    
    # 保存量化后的模型
    torch.save(quantized_model, save_path)
    print(f"量化后的模型已保存到: {save_path}")
    
    # 分析量化后的模型
    analyze_model(quantized_model)
    
    return quantized_model

def quantize_model_qat(model, save_path='quantized_qat_model.pt', num_calibration_batches=10):
    """
    使用量化感知训练(QAT)压缩模型
    
    参数:
        model: PyTorch模型
        save_path (str): 保存路径
        num_calibration_batches (int): 校准批次数
    
    返回:
        quantized_model: 量化后的模型
    """
    print("开始量化感知训练...")
    
    # 准备量化配置
    model.train()
    
    # 准备QAT模型
    model_qat = torch.quantization.prepare_qat(model, inplace=False)
    
    # 模拟QAT训练
    print(f"模拟{num_calibration_batches}批次的QAT训练...")
    for i in range(num_calibration_batches):
        # 这里应该有真实的训练代码
        # 为了演示，我们只是前向传播一些随机数据
        dummy_input = torch.randn(1, 3, 640, 640)
        _ = model_qat(dummy_input)
        print(f"批次 {i+1}/{num_calibration_batches} 完成")
    
    # 转换为量化模型
    model_qat.eval()
    quantized_model = torch.quantization.convert(model_qat, inplace=False)
    
    # 保存量化后的模型
    torch.save(quantized_model, save_path)
    print(f"量化后的模型已保存到: {save_path}")
    
    # 分析量化后的模型
    analyze_model(quantized_model)
    
    return quantized_model

def benchmark_model(model, img_size=640, device='cpu', iterations=100):
    """
    基准测试模型推理速度
    
    参数:
        model: PyTorch模型
        img_size (int): 图像大小
        device (str): 设备
        iterations (int): 迭代次数
    """
    print(f"基准测试模型推理速度...")
    
    # 准备输入
    dummy_input = torch.randn(1, 3, img_size, img_size).to(device)
    model = model.to(device)
    model.eval()
    
    # 预热
    with torch.no_grad():
        for _ in range(10):
            _ = model(dummy_input)
    
    # 计时
    import time
    start_time = time.time()
    with torch.no_grad():
        for _ in range(iterations):
            _ = model(dummy_input)
    end_time = time.time()
    
    # 计算FPS
    elapsed_time = end_time - start_time
    fps = iterations / elapsed_time
    
    print(f"推理速度: {fps:.2f} FPS (每帧 {1000/fps:.2f} ms)")
    print(f"{iterations}次迭代总耗时: {elapsed_time:.2f} 秒")

def export_onnx(model, img_size=640, save_path='quantized_model.onnx'):
    """
    将模型导出为ONNX格式
    
    参数:
        model: PyTorch模型
        img_size (int): 图像大小
        save_path (str): 保存路径
    """
    print(f"导出模型为ONNX格式...")
    
    # 准备输入
    dummy_input = torch.randn(1, 3, img_size, img_size)
    model.eval()
    
    # 导出
    torch.onnx.export(
        model,
        dummy_input,
        save_path,
        verbose=False,
        opset_version=12,
        input_names=['images'],
        output_names=['output'],
        dynamic_axes={
            'images': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        }
    )
    
    print(f"ONNX模型已保存到: {save_path}")

def main():
    parser = argparse.ArgumentParser(description='YOLOv5模型量化工具')
    parser.add_argument('--weights', type=str, default='yolov5/best_22.pt', help='模型权重文件路径')
    parser.add_argument('--device', type=str, default='cpu', help='设备 (cpu 或 cuda:0 等)')
    parser.add_argument('--method', type=str, default='dynamic', choices=['static', 'dynamic', 'qat'], help='量化方法')
    parser.add_argument('--save-path', type=str, default='quantized_model.pt', help='量化后模型保存路径')
    parser.add_argument('--benchmark', action='store_true', help='是否进行基准测试')
    parser.add_argument('--export-onnx', action='store_true', help='是否导出为ONNX格式')
    parser.add_argument('--img-size', type=int, default=640, help='图像大小')
    parser.add_argument('--calibration-batches', type=int, default=10, help='QAT校准批次数')
    
    args = parser.parse_args()
    
    # 选择设备
    device = select_device(args.device)
    
    # 加载模型
    print(f"加载模型: {args.weights}")
    model = load_model(args.weights, device)
    
    # 分析原始模型
    print("\n原始模型信息:")
    analyze_model(model)
    
    # 如果需要，进行基准测试
    if args.benchmark:
        print("\n原始模型基准测试:")
        benchmark_model(model, args.img_size, device)
    
    # 量化模型
    print(f"\n开始{args.method}量化模型...")
    if args.method == 'static':
        quantized_model = quantize_model_static(model, args.save_path)
    elif args.method == 'dynamic':
        quantized_model = quantize_model_dynamic(model, args.save_path)
    else:  # qat
        quantized_model = quantize_model_qat(model, args.save_path, args.calibration_batches)
    
    # 如果需要，进行基准测试
    if args.benchmark:
        print("\n量化后模型基准测试:")
        benchmark_model(quantized_model, args.img_size, device)
    
    # 如果需要，导出为ONNX格式
    if args.export_onnx:
        onnx_path = os.path.splitext(args.save_path)[0] + '.onnx'
        export_onnx(quantized_model, args.img_size, onnx_path)
    
    print("\n量化完成!")

if __name__ == '__main__':
    main()
