#!/usr/bin/env python3
"""
测试真实的tracks文件（没有video_info的情况）
"""

import os
import json
from worker_analysis import WorkerAnalyzer

def test_real_tracks():
    """测试真实的tracks文件"""
    print("=" * 60)
    print("测试真实tracks文件（无video_info）")
    print("=" * 60)
    
    # 使用现有的tracks文件
    tracks_file = "runs/track/pruned_worker_model_osnet_ibn_x1_0_MSMT17/tracks/测试基准视频_2.txt"
    output_dir = "test_real_tracks_output"
    
    if not os.path.exists(tracks_file):
        print(f"❌ Tracks文件不存在: {tracks_file}")
        return
    
    try:
        # 创建分析器（不提供video_file，模拟没有video_info的情况）
        print(f"创建WorkerAnalyzer实例...")
        print(f"Tracks文件: {tracks_file}")
        print(f"输出目录: {output_dir}")
        
        analyzer = WorkerAnalyzer(tracks_file, None, output_dir)
        
        print("注意: 没有提供video文件，将使用默认FPS (30.0)")
        
        # 运行分析
        print("\n开始分析...")
        analyzer.analyze()
        
        print("\n✅ 真实tracks测试成功！")
        
        # 检查生成的统计数据
        stats_file = os.path.join(output_dir, "worker_stats.json")
        if os.path.exists(stats_file):
            with open(stats_file, 'r', encoding='utf-8') as f:
                stats = json.load(f)
            
            print("\n工人统计结果:")
            print("-" * 50)
            for worker_id, data in stats.items():
                duration = data.get('duration', 0)
                duration_fixed = data.get('duration_fixed', 0)
                distance = data['total_distance']
                speed = data.get('avg_speed', 0)
                speed_fixed = data.get('avg_speed_fixed', 0)
                frames = data['last_frame'] - data['first_frame'] + 1
                fps_used = data.get('fps_used', 'N/A')
                
                print(f"工人 {worker_id}:")
                print(f"  帧范围: {data['first_frame']}-{data['last_frame']} ({frames}帧)")
                print(f"  原始Duration: {duration:.2f}秒")
                print(f"  修复Duration: {duration_fixed:.2f}秒")
                print(f"  移动距离: {distance:.1f}像素")
                print(f"  原始Speed: {speed:.1f}像素/秒")
                print(f"  修复Speed: {speed_fixed:.1f}像素/秒")
                print(f"  使用FPS: {fps_used}")
                print()
        
        # 检查生成的文件
        print("检查生成的文件:")
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            for file in sorted(files):
                file_path = os.path.join(output_dir, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    if file.endswith('.png'):
                        print(f"  🎯 {file} - {size:,} bytes")
                    else:
                        print(f"  📄 {file} - {size:,} bytes")
        
        # 检查HTML报告中的值
        html_file = os.path.join(output_dir, "worker_report.html")
        if os.path.exists(html_file):
            print(f"\n📊 HTML报告已生成: {html_file}")
            print("可以在浏览器中打开查看修复后的Duration和Avg Speed值")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_real_tracks()
