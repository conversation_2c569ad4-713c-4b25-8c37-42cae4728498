#!/usr/bin/env python3
"""
验证每秒和每帧图表功能是否正确整合
"""

import os
import inspect
from worker_analysis import WorkerAnalyzer

def verify_methods():
    """验证方法是否存在"""
    print("=" * 60)
    print("验证每秒和每帧图表方法")
    print("=" * 60)
    
    # 检查新方法是否存在
    methods_to_check = [
        '_generate_per_second_chart',
        '_generate_per_frame_chart',
        '_generate_worker_count_charts'
    ]
    
    print("检查方法:")
    for method_name in methods_to_check:
        if hasattr(WorkerAnalyzer, method_name):
            method = getattr(WorkerAnalyzer, method_name)
            if callable(method):
                print(f"  ✅ {method_name} - 存在且可调用")
                
                # 检查方法签名
                sig = inspect.signature(method)
                print(f"    签名: {method_name}{sig}")
            else:
                print(f"  ❌ {method_name} - 存在但不可调用")
        else:
            print(f"  ❌ {method_name} - 不存在")
    
    # 检查_analyze_activity_patterns方法
    print("\n检查_analyze_activity_patterns方法:")
    if hasattr(WorkerAnalyzer, '_analyze_activity_patterns'):
        method = getattr(WorkerAnalyzer, '_analyze_activity_patterns')
        source = inspect.getsource(method)
        
        if '_generate_worker_count_charts' in source:
            print("  ✅ 包含调用: _generate_worker_count_charts")
        else:
            print("  ❌ 缺少调用: _generate_worker_count_charts")
            
        # 显示方法内容
        print(f"\n方法内容:")
        lines = source.split('\n')
        for i, line in enumerate(lines[:10], 1):  # 只显示前10行
            print(f"    {i:2d}: {line}")
        if len(lines) > 10:
            print(f"    ... (还有 {len(lines) - 10} 行)")
    else:
        print("  ❌ _analyze_activity_patterns方法不存在")

def check_method_calls():
    """检查_generate_worker_count_charts方法的调用"""
    print("\n" + "=" * 60)
    print("检查_generate_worker_count_charts方法内容")
    print("=" * 60)
    
    if hasattr(WorkerAnalyzer, '_generate_worker_count_charts'):
        method = getattr(WorkerAnalyzer, '_generate_worker_count_charts')
        source = inspect.getsource(method)
        
        # 检查是否调用了每秒和每帧的方法
        expected_calls = [
            '_generate_per_second_chart',
            '_generate_per_frame_chart'
        ]
        
        for call in expected_calls:
            if call in source:
                print(f"  ✅ 包含调用: {call}")
            else:
                print(f"  ❌ 缺少调用: {call}")
        
        # 显示方法内容
        print(f"\n方法内容:")
        lines = source.split('\n')
        for i, line in enumerate(lines, 1):
            print(f"    {i:2d}: {line}")
    else:
        print("  ❌ _generate_worker_count_charts方法不存在")

def main():
    print("🔍 开始验证每秒和每帧图表功能")
    
    try:
        verify_methods()
        check_method_calls()
        
        print("\n🎉 验证完成！")
        
    except Exception as e:
        print(f"\n❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
