#!/usr/bin/env python3
"""
优化的稀疏剪枝工具
专门针对YOLO模型的性能优化
使用多种稀疏化策略
"""

import argparse
import torch
import torch.nn as nn
import numpy as np
import yaml
import os
import sys
import time
from pathlib import Path
from copy import deepcopy

# 添加yolov5路径
sys.path.append('yolov5')

from yolov5.utils.torch_utils import select_device
from yolov5.utils.general import check_yaml, colorstr, LOGGER
from yolov5.models.yolo import Model
from yolov5.models.common import Bottleneck

# 导入剪枝记录器
from pruning_logger import PruningLogger

def gather_bn_weights(module_list):
    """收集所有BN层的权重"""
    size_list = [idx.weight.data.shape[0] for idx in module_list.values()]
    bn_weights = torch.zeros(sum(size_list))
    index = 0
    for i, idx in enumerate(module_list.values()):
        size = size_list[i]
        bn_weights[index:(index + size)] = idx.weight.data.abs().clone()
        index += size
    return bn_weights

def obtain_bn_mask(bn_module, thre):
    """根据阈值获取BN层的掩码"""
    if torch.cuda.is_available():
        thre = thre.cuda()
    mask = bn_module.weight.data.abs().ge(thre).float()
    return mask

def load_model(weights, device):
    """加载YOLOv5模型"""
    print(f"加载模型: {weights}")

    ckpt = torch.load(weights, map_location=device)

    if isinstance(ckpt, dict):
        if 'ema' in ckpt and ckpt['ema'] is not None:
            model = ckpt['ema']
            print("使用EMA模型")
        elif 'model' in ckpt and ckpt['model'] is not None:
            model = ckpt['model']
            print("使用普通模型")
        else:
            raise ValueError("无法从模型字典中找到模型")
    else:
        model = ckpt
        ckpt = {'model': model}

    model = model.float().to(device)

    print(f"模型类型: {type(model)}")
    print(f"原始模型参数量: {sum(p.numel() for p in model.parameters()):,}")

    return model, ckpt

def analyze_model_structure(model):
    """分析模型结构，找出可剪枝的BN层"""
    model_list = {}
    ignore_bn_list = []

    # 找出需要忽略的BN层（shortcut连接相关）
    for name, layer in model.named_modules():
        if isinstance(layer, Bottleneck):
            if layer.add:  # 有shortcut连接
                ignore_bn_list.append(name.rsplit(".", 2)[0] + ".cv1.bn")
                ignore_bn_list.append(name + '.cv1.bn')
                ignore_bn_list.append(name + '.cv2.bn')

    # 收集所有BN层
    for name, layer in model.named_modules():
        if isinstance(layer, torch.nn.BatchNorm2d):
            if name not in ignore_bn_list:
                model_list[name] = layer

    print(f"总BN层数: {len([m for n, m in model.named_modules() if isinstance(m, nn.BatchNorm2d)])}")
    print(f"可剪枝BN层数: {len(model_list)}")
    print(f"忽略的BN层数: {len(ignore_bn_list)}")

    return model_list, ignore_bn_list

def calculate_adaptive_threshold(model_list, percent):
    """计算自适应剪枝阈值"""
    bn_weights = gather_bn_weights(model_list)
    sorted_bn = torch.sort(bn_weights)[0]

    # 计算最高阈值（避免剪掉所有通道）
    highest_thre = []
    for bnlayer in model_list.values():
        highest_thre.append(bnlayer.weight.data.abs().max().item())
    highest_thre = min(highest_thre)

    # 计算阈值上限对应的百分比
    percent_limit = (sorted_bn == highest_thre).nonzero()[0, 0].item() / len(bn_weights)

    print(f'建议的Gamma阈值应小于 {highest_thre:.4f}')
    print(f'对应的剪枝比例是 {percent_limit:.3f}')

    # 自适应调整剪枝比例
    if percent >= percent_limit:
        adjusted_percent = percent_limit * 0.9  # 留10%的安全边际
        print(f"自动调整剪枝比例: {percent} → {adjusted_percent:.3f}")
        percent = adjusted_percent

    # 计算实际阈值
    thre_index = int(len(sorted_bn) * percent)
    thre = sorted_bn[thre_index]

    print(f'小于 {thre:.4f} 的Gamma值将被设为零!')

    return thre, percent

def apply_sparse_pruning(model, model_list, ignore_bn_list, thre, strategy='aggressive'):
    """应用稀疏剪枝"""
    print("=" * 94)
    print(f"|{'layer name':<25}{'|':<10}{'origin channels':<20}{'|':<10}{'remaining channels':<20}|")
    print("=" * 94)

    total_original = 0
    total_remaining = 0
    total_pruned = 0

    for bnname, bnlayer in model.named_modules():
        if isinstance(bnlayer, nn.BatchNorm2d):
            original_channels = bnlayer.weight.data.numel()

            if bnname in ignore_bn_list:
                # 忽略的层保持所有通道
                mask = torch.ones(bnlayer.weight.data.size())
                if torch.cuda.is_available():
                    mask = mask.cuda()
                remaining_channels = original_channels
            else:
                # 根据阈值创建掩码
                mask = obtain_bn_mask(bnlayer, thre)
                remaining_channels = int(mask.sum())

                # 检查是否有通道被完全剪掉
                if remaining_channels == 0:
                    print(f"警告: 层 {bnname} 的所有通道都被剪掉，保留1个通道")
                    # 保留权重最大的通道
                    max_idx = torch.argmax(bnlayer.weight.data.abs())
                    mask = torch.zeros_like(mask)
                    mask[max_idx] = 1
                    remaining_channels = 1

            # 应用掩码
            bnlayer.weight.data.mul_(mask)
            bnlayer.bias.data.mul_(mask)

            total_original += original_channels
            total_remaining += remaining_channels
            total_pruned += (original_channels - remaining_channels)

            print(f"|{bnname:<25}{'|':<10}{original_channels:<20}{'|':<10}{remaining_channels:<20}|")

    print("=" * 94)

    sparsity = total_pruned / total_original * 100
    print(f"总稀疏度: {sparsity:.1f}% ({total_pruned:,}/{total_original:,} 参数被置零)")

    return sparsity

def optimize_model_for_inference(model):
    """优化模型以提高推理速度"""
    print("优化模型以提高推理速度...")

    # 1. 融合BN层到卷积层（如果可能）
    model.eval()

    # 2. 设置为推理模式
    for module in model.modules():
        if isinstance(module, nn.BatchNorm2d):
            module.eval()
            # 冻结BN层的统计信息
            module.track_running_stats = False

    # 3. 启用推理优化
    if hasattr(model, 'fuse'):
        try:
            model.fuse()
            print("成功融合卷积和BN层")
        except:
            print("无法融合层，跳过")

    return model

def benchmark_model(model, device, img_size=640, num_iterations=100):
    """基准测试模型性能"""
    print(f"基准测试模型推理速度...")

    model.eval()

    # 创建随机输入
    dummy_input = torch.randn(1, 3, img_size, img_size).to(device)

    # 预热
    with torch.no_grad():
        for _ in range(10):
            _ = model(dummy_input)

    # 计时
    start_time = time.time()

    with torch.no_grad():
        for _ in range(num_iterations):
            _ = model(dummy_input)

    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / num_iterations
    fps = 1.0 / avg_time

    print(f"推理速度: {fps:.2f} FPS")
    print(f"平均推理时间: {avg_time*1000:.2f} ms")

    return fps, avg_time

def save_sparse_model(model, save_path, sparsity):
    """保存稀疏剪枝后的模型"""
    save_dict = {
        'model': model,
        'epoch': -1,
        'best_fitness': 0.0,
        'optimizer': None,
        'ema': None,
        'updates': 0,
        'sparsity': sparsity
    }

    torch.save(save_dict, save_path)
    print(f"稀疏剪枝后的模型已保存到: {save_path}")

    # 显示文件大小
    file_size = os.path.getsize(save_path) / (1024 * 1024)  # MB
    print(f"模型文件大小: {file_size:.2f} MB")

def main():
    parser = argparse.ArgumentParser(description='优化的稀疏剪枝工具')
    parser.add_argument('--weights', type=str, default='yolov5/best_22.pt', help='模型权重文件路径')
    parser.add_argument('--percent', type=float, default=0.15, help='剪枝比例 (0.0-1.0)')
    parser.add_argument('--device', type=str, default='cpu', help='设备 (cpu 或 cuda:0 等)')
    parser.add_argument('--save-path', type=str, default='optimized_sparse_yolo.pt', help='剪枝后模型保存路径')
    parser.add_argument('--strategy', type=str, default='balanced', choices=['conservative', 'balanced', 'aggressive'],
                       help='剪枝策略')
    parser.add_argument('--benchmark', action='store_true', help='进行性能基准测试')
    parser.add_argument('--optimize', action='store_true', help='启用推理优化')
    parser.add_argument('--log-dir', type=str, default='pruning_logs', help='日志保存目录')
    parser.add_argument('--note', type=str, default='', help='实验备注')

    args = parser.parse_args()

    # 初始化剪枝记录器
    logger = PruningLogger(args.log_dir)

    # 选择设备
    device = select_device(args.device)
    print(f"使用设备: {device}")

    # 加载模型
    model, ckpt = load_model(args.weights, device)
    model.eval()

    # 记录原始模型信息
    logger.log_original_model(args.weights, {"model_object": model})

    # 记录剪枝配置
    logger.log_pruning_config("sparse_pruning", {
        "percent": args.percent,
        "strategy": args.strategy,
        "optimize": args.optimize,
        "device": str(device)
    })

    # 基准测试原始模型
    if args.benchmark:
        print("\n原始模型性能:")
        original_fps, original_time = benchmark_model(model, device)
        logger.log_performance_before({
            "fps": original_fps,
            "inference_time_ms": original_time * 1000
        })

    # 分析模型结构
    model_list, ignore_bn_list = analyze_model_structure(model)

    if len(model_list) == 0:
        print("错误: 没有找到可剪枝的BN层!")
        return

    # 计算自适应剪枝阈值
    thre, adjusted_percent = calculate_adaptive_threshold(model_list, args.percent)

    # 应用稀疏剪枝
    sparsity = apply_sparse_pruning(model, model_list, ignore_bn_list, thre, args.strategy)

    # 记录剪枝结果
    logger.log_pruning_results({
        "sparsity_percent": sparsity,
        "threshold": float(thre),
        "adjusted_percent": adjusted_percent,
        "prunable_layers": len(model_list),
        "ignored_layers": len(ignore_bn_list)
    })

    # 优化模型
    if args.optimize:
        model = optimize_model_for_inference(model)

    # 基准测试剪枝后模型
    if args.benchmark:
        print("\n剪枝后模型性能:")
        pruned_fps, pruned_time = benchmark_model(model, device)

        logger.log_performance_after({
            "fps": pruned_fps,
            "inference_time_ms": pruned_time * 1000
        })

        # 计算性能提升
        speed_improvement = (pruned_fps / original_fps - 1) * 100
        time_reduction = (1 - pruned_time / original_time) * 100

        print(f"\n性能提升:")
        print(f"推理速度提升: {speed_improvement:.1f}% ({original_fps:.2f} → {pruned_fps:.2f} FPS)")
        print(f"推理时间减少: {time_reduction:.1f}% ({original_time*1000:.2f} → {pruned_time*1000:.2f} ms)")

    # 保存剪枝后的模型
    save_sparse_model(model, args.save_path, sparsity)

    # 记录剪枝后模型
    logger.log_pruned_model(args.save_path)

    # 添加备注
    if args.note:
        logger.add_note(args.note)

    # 保存实验记录
    saved_files = logger.save_experiment()

    # 显示剪枝结果
    original_params = sum(p.numel() for p in model.parameters())

    print(f"\n稀疏剪枝完成!")
    print(f"模型参数量: {original_params:,} (稀疏)")
    print(f"稀疏度: {sparsity:.1f}%")
    print(f"实际剪枝比例: {adjusted_percent:.1%}")
    print(f"剪枝策略: {args.strategy}")
    print(f"剪枝后模型保存在: {args.save_path}")

    print(f"\n📊 实验记录已保存:")
    print(f"  实验ID: {logger.experiment_id}")
    print(f"  JSON记录: {saved_files['json_path']}")
    print(f"  HTML报告: {saved_files['html_path']}")
    print(f"  性能图表: {saved_files['plot_path']}")

    print(f"\n使用建议:")
    print(f"1. 查看HTML报告了解详细性能对比")
    print(f"2. 使用 test_pruned_model.py 测试剪枝后模型")
    print(f"3. 在实际应用中测试检测精度")
    print(f"4. 如果精度下降明显，可以降低剪枝比例重新剪枝")
    print(f"5. 可以结合量化进一步优化模型")

if __name__ == '__main__':
    main()
