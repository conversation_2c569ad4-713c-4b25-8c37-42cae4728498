import torch
import sys
import argparse
import os

def check_model(model_path):
    """
    检查模型结构和格式
    
    参数:
        model_path (str): 模型文件路径
    """
    print(f"检查模型: {model_path}")
    
    try:
        # 加载模型
        model_data = torch.load(model_path, map_location='cpu')
        
        # 检查模型类型
        print(f"模型类型: {type(model_data)}")
        
        # 如果是字典，检查键
        if isinstance(model_data, dict):
            print(f"模型是字典格式，包含以下键: {list(model_data.keys())}")
            
            # 检查是否包含模型
            if 'model' in model_data:
                print("包含'model'键")
                model = model_data['model']
                print(f"model类型: {type(model)}")
                
                # 检查模型参数
                if hasattr(model, 'parameters'):
                    params = sum(p.numel() for p in model.parameters())
                    print(f"model参数数量: {params:,}")
                else:
                    print("model对象没有parameters方法")
            
            # 检查是否包含EMA模型
            if 'ema' in model_data:
                print("包含'ema'键")
                ema = model_data['ema']
                print(f"ema类型: {type(ema)}")
                
                # 检查EMA模型参数
                if ema is not None and hasattr(ema, 'parameters'):
                    params = sum(p.numel() for p in ema.parameters())
                    print(f"ema参数数量: {params:,}")
                else:
                    print("ema对象为None或没有parameters方法")
            
            # 检查其他可能的键
            for key in model_data.keys():
                if key not in ['model', 'ema']:
                    print(f"其他键: {key}, 类型: {type(model_data[key])}")
        
        # 如果是模型对象，检查参数
        elif hasattr(model_data, 'parameters'):
            params = sum(p.numel() for p in model_data.parameters())
            print(f"模型参数数量: {params:,}")
            
            # 检查模型结构
            if hasattr(model_data, 'named_modules'):
                print("\n模型结构:")
                for name, module in model_data.named_modules():
                    if isinstance(module, torch.nn.Conv2d) or isinstance(module, torch.nn.Linear):
                        params = sum(p.numel() for p in module.parameters())
                        print(f"{name}: {params:,} 参数")
        
        else:
            print(f"未知模型格式: {type(model_data)}")
        
        # 检查文件大小
        file_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
        print(f"\n模型文件大小: {file_size:.2f} MB")
        
        return True
    
    except Exception as e:
        print(f"检查模型时出错: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='检查YOLOv5模型结构')
    parser.add_argument('--model', type=str, required=True, help='模型文件路径')
    
    args = parser.parse_args()
    
    check_model(args.model)

if __name__ == '__main__':
    main()
