#!/usr/bin/env python3
"""
新的YOLO模型剪枝实验
支持GPU环境和工人检测任务验证
"""

import argparse
import torch
import torch.nn as nn
import numpy as np
import time
import os
import sys
from pathlib import Path
import cv2

# 添加yolov5路径
sys.path.append('yolov5')

from yolov5.utils.torch_utils import select_device
from yolov5.utils.general import check_dataset, check_yaml, colorstr, LOGGER
from yolov5.models.yolo import Model
from yolov5.models.common import Bottleneck

from pruning_logger import PruningLogger

class NewPruningExperiment:
    def __init__(self, weights_path, device='auto'):
        """
        初始化新的剪枝实验
        
        Args:
            weights_path: 模型权重路径
            device: 计算设备 ('auto', 'cpu', 'cuda:0' 等)
        """
        self.weights_path = weights_path
        self.device = select_device(device)
        self.logger = PruningLogger()
        
        print(f"🚀 新剪枝实验初始化")
        print(f"设备: {self.device}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"GPU型号: {torch.cuda.get_device_name(0)}")
            print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        
        # 加载原始模型
        self.original_model, self.original_ckpt = self.load_model(weights_path)
        
        # 记录原始模型信息
        self.logger.log_original_model(
            weights_path, 
            {"model_object": self.original_model}
        )
        
        print(f"✅ 原始模型加载完成: {weights_path}")
        
    def load_model(self, weights_path):
        """加载YOLO模型"""
        print(f"📥 加载模型: {weights_path}")
        
        ckpt = torch.load(weights_path, map_location=self.device, weights_only=False)
        
        if isinstance(ckpt, dict):
            if 'ema' in ckpt and ckpt['ema'] is not None:
                model = ckpt['ema']
                print("使用EMA模型")
            elif 'model' in ckpt and ckpt['model'] is not None:
                model = ckpt['model']
                print("使用普通模型")
            else:
                raise ValueError("无法从模型字典中找到模型")
        else:
            model = ckpt
            ckpt = {'model': model}
        
        model = model.float().to(self.device)
        model.eval()
        
        total_params = sum(p.numel() for p in model.parameters())
        print(f"模型参数量: {total_params:,}")
        
        return model, ckpt
    
    def benchmark_model(self, model, num_runs=50, input_size=(640, 640)):
        """GPU优化的性能测试"""
        print(f"🔥 开始性能测试 ({num_runs} 次运行, 输入尺寸: {input_size})")
        
        # 创建测试输入
        test_input = torch.randn(1, 3, input_size[0], input_size[1]).to(self.device)
        
        # GPU预热
        if torch.cuda.is_available():
            for _ in range(20):
                with torch.no_grad():
                    _ = model(test_input)
            torch.cuda.synchronize()
        else:
            for _ in range(10):
                with torch.no_grad():
                    _ = model(test_input)
        
        # 正式测试
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(num_runs):
                _ = model(test_input)
        
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        end_time = time.time()
        
        # 计算性能指标
        total_time = end_time - start_time
        avg_time_ms = (total_time / num_runs) * 1000
        fps = num_runs / total_time
        
        # GPU内存使用情况
        gpu_memory = {}
        if torch.cuda.is_available():
            gpu_memory = {
                "allocated_mb": torch.cuda.memory_allocated() / 1024**2,
                "cached_mb": torch.cuda.memory_reserved() / 1024**2,
                "max_allocated_mb": torch.cuda.max_memory_allocated() / 1024**2
            }
        
        performance = {
            "fps": fps,
            "inference_time_ms": avg_time_ms,
            "total_time_s": total_time,
            "num_runs": num_runs,
            "device": str(self.device),
            "input_size": input_size
        }
        
        if gpu_memory:
            performance["gpu_memory"] = gpu_memory
        
        print(f"⚡ FPS: {fps:.2f}")
        print(f"⏱️ 平均推理时间: {avg_time_ms:.2f} ms")
        if gpu_memory:
            print(f"💾 GPU内存使用: {gpu_memory['allocated_mb']:.1f} MB")
        
        return performance
    
    def analyze_bn_layers(self, model):
        """分析BN层结构"""
        bn_layers = {}
        ignore_bn_list = []
        
        # 找出需要忽略的BN层（shortcut连接相关）
        for name, layer in model.named_modules():
            if isinstance(layer, Bottleneck):
                if layer.add:  # 有shortcut连接
                    ignore_bn_list.append(name.rsplit(".", 2)[0] + ".cv1.bn")
                    ignore_bn_list.append(name + '.cv1.bn')
                    ignore_bn_list.append(name + '.cv2.bn')
        
        # 收集所有BN层
        for name, layer in model.named_modules():
            if isinstance(layer, torch.nn.BatchNorm2d):
                if name not in ignore_bn_list:
                    bn_layers[name] = layer
        
        print(f"📊 BN层分析:")
        print(f"  总BN层数: {len([m for n, m in model.named_modules() if isinstance(m, torch.nn.BatchNorm2d)])}")
        print(f"  可剪枝BN层: {len(bn_layers)}")
        print(f"  忽略的BN层: {len(ignore_bn_list)}")
        
        return bn_layers, ignore_bn_list
    
    def sparse_prune(self, percent=0.2):
        """改进的稀疏剪枝"""
        print(f"\n🔪 开始稀疏剪枝 (剪枝比例: {percent:.1%})")
        
        # 记录剪枝配置
        self.logger.log_pruning_config("sparse_pruning_v2", {
            "percent": percent,
            "method": "BN_weight_based_improved",
            "device": str(self.device)
        })
        
        # 测试原始模型性能
        print("📈 测试原始模型性能...")
        original_performance = self.benchmark_model(self.original_model)
        self.logger.log_performance_before(original_performance)
        
        # 分析BN层
        bn_layers, ignore_bn_list = self.analyze_bn_layers(self.original_model)
        
        if len(bn_layers) == 0:
            raise ValueError("没有找到可剪枝的BN层!")
        
        # 收集所有BN权重并计算统计信息
        all_weights = []
        layer_weights = {}
        
        for name, layer in bn_layers.items():
            weights = layer.weight.data.abs().cpu().numpy()
            all_weights.extend(weights)
            layer_weights[name] = weights
        
        all_weights = np.array(all_weights)
        
        print(f"📊 权重统计:")
        print(f"  权重总数: {len(all_weights):,}")
        print(f"  权重范围: [{all_weights.min():.6f}, {all_weights.max():.6f}]")
        print(f"  权重均值: {all_weights.mean():.6f}")
        print(f"  权重标准差: {all_weights.std():.6f}")
        
        # 计算阈值
        sorted_weights = np.sort(all_weights)
        threshold_index = int(len(sorted_weights) * percent)
        threshold = sorted_weights[threshold_index]
        
        print(f"🎯 剪枝阈值: {threshold:.6f}")
        print(f"📍 阈值位置: {threshold_index:,} / {len(sorted_weights):,}")
        
        # 应用剪枝
        pruned_channels = 0
        total_channels = 0
        layer_stats = {}
        
        for name, layer in bn_layers.items():
            original_channels = layer.weight.data.size(0)
            mask = layer.weight.data.abs() > threshold
            
            # 应用掩码
            layer.weight.data *= mask.float()
            layer.bias.data *= mask.float()
            
            remaining_channels = mask.sum().item()
            pruned_in_layer = original_channels - remaining_channels
            
            pruned_channels += pruned_in_layer
            total_channels += original_channels
            
            layer_stats[name] = {
                "original": original_channels,
                "remaining": remaining_channels,
                "pruned": pruned_in_layer,
                "prune_ratio": pruned_in_layer / original_channels
            }
            
            if remaining_channels == 0:
                raise ValueError(f"层 {name} 的所有通道都被剪掉了！请降低剪枝比例。")
        
        actual_prune_ratio = pruned_channels / total_channels
        print(f"✂️ 实际剪枝比例: {actual_prune_ratio:.1%}")
        print(f"📉 剪枝通道数: {pruned_channels:,} / {total_channels:,}")
        
        # 测试剪枝后性能
        print("📈 测试剪枝后模型性能...")
        pruned_performance = self.benchmark_model(self.original_model)
        self.logger.log_performance_after(pruned_performance)
        
        # 记录剪枝结果
        pruning_results = {
            "target_prune_ratio": percent,
            "actual_prune_ratio": actual_prune_ratio,
            "pruned_channels": int(pruned_channels),
            "total_channels": int(total_channels),
            "threshold": float(threshold),
            "prunable_bn_layers": len(bn_layers),
            "weight_stats": {
                "min": float(all_weights.min()),
                "max": float(all_weights.max()),
                "mean": float(all_weights.mean()),
                "std": float(all_weights.std())
            },
            "layer_stats": {k: {kk: float(vv) if isinstance(vv, (int, float, np.number)) else vv 
                               for kk, vv in v.items()} for k, v in layer_stats.items()}
        }
        self.logger.log_pruning_results(pruning_results)
        
        # 保存剪枝后模型
        save_path = f"new_sparse_pruned_yolo_{int(percent*100)}percent.pt"
        self.save_pruned_model(save_path)
        self.logger.log_pruned_model(save_path)
        
        return save_path, pruning_results
    
    def save_pruned_model(self, save_path):
        """保存剪枝后的模型"""
        save_dict = {
            'model': self.original_model,
            'epoch': -1,
            'best_fitness': 0.0,
            'optimizer': None,
            'ema': None,
            'updates': 0,
            'pruning_info': {
                'device': str(self.device),
                'timestamp': time.time()
            }
        }
        
        torch.save(save_dict, save_path)
        print(f"💾 剪枝后模型已保存: {save_path}")
    
    def run_experiment(self, prune_ratios=[0.1, 0.2, 0.3]):
        """运行完整的剪枝实验"""
        print("=" * 80)
        print("🔬 开始新的综合剪枝实验")
        print("=" * 80)
        
        results = []
        
        for i, ratio in enumerate(prune_ratios):
            print(f"\n{'='*25} 实验 {i+1}/{len(prune_ratios)}: 剪枝比例 {ratio:.1%} {'='*25}")
            
            # 重新加载原始模型（避免累积剪枝效果）
            self.original_model, self.original_ckpt = self.load_model(self.weights_path)
            
            try:
                save_path, pruning_results = self.sparse_prune(ratio)
                results.append({
                    "ratio": ratio,
                    "save_path": save_path,
                    "results": pruning_results
                })
                
                # 添加实验备注
                self.logger.add_note(f"新稀疏剪枝 {ratio:.1%} 完成，设备: {self.device}，模型: {save_path}")
                
                print(f"✅ 剪枝比例 {ratio:.1%} 完成")
                
            except Exception as e:
                print(f"❌ 剪枝比例 {ratio:.1%} 失败: {e}")
                self.logger.add_note(f"剪枝比例 {ratio:.1%} 失败: {str(e)}")
        
        # 保存实验记录
        saved_files = self.logger.save_experiment()
        
        print(f"\n{'='*80}")
        print("🎉 实验完成!")
        print(f"🆔 实验ID: {self.logger.experiment_id}")
        print(f"✅ 成功剪枝: {len(results)}/{len(prune_ratios)} 个比例")
        print(f"📊 实验记录: {saved_files['html_path']}")
        print(f"📈 性能图表: {saved_files['plot_path']}")
        print(f"{'='*80}")
        
        return results

def main():
    parser = argparse.ArgumentParser(description='新的YOLO模型剪枝实验')
    parser.add_argument('--weights', type=str, default='yolov5/best_22.pt', 
                       help='模型权重文件路径')
    parser.add_argument('--device', type=str, default='auto', 
                       help='设备 (auto, cpu, cuda:0 等)')
    parser.add_argument('--ratios', type=float, nargs='+', default=[0.1, 0.2, 0.3], 
                       help='剪枝比例列表')
    
    args = parser.parse_args()
    
    # 检查模型文件
    if not os.path.exists(args.weights):
        print(f"❌ 错误: 模型文件不存在 {args.weights}")
        return
    
    # 创建剪枝器并运行实验
    try:
        pruner = NewPruningExperiment(args.weights, args.device)
        results = pruner.run_experiment(args.ratios)
        
        # 显示结果摘要
        print("\n📋 实验结果摘要:")
        print("-" * 60)
        for result in results:
            ratio = result["ratio"]
            actual_ratio = result["results"]["actual_prune_ratio"]
            save_path = result["save_path"]
            print(f"🎯 剪枝比例 {ratio:.1%} -> 实际 {actual_ratio:.1%}")
            print(f"   📁 模型: {save_path}")
        
        print(f"\n💡 下一步: 使用生成的剪枝模型进行工人检测任务验证")
        
    except Exception as e:
        print(f"❌ 实验失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
