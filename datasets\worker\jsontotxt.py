import os
import json

# 设置路径
json_folder = r"data/data/label"  # 原始 JSON 文件夹路径
txt_folder = r"labels/train2017"    # 输出 TXT 文件夹路径

# 创建输出目录（如果不存在）
os.makedirs(txt_folder, exist_ok=True)

# 遍历 JSON 文件
for filename in os.listdir(json_folder):
    if filename.endswith(".json"):
        json_path = os.path.join(json_folder, filename)
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        image_width = data.get("imageWidth")
        image_height = data.get("imageHeight")

        txt_lines = []

        for shape in data["shapes"]:
            if shape["shape_type"] != "rectangle":
                continue  # 仅处理矩形标注

            # 提取标注框坐标
            (x1, y1), (x2, y2) = shape["points"]
            x_center = ((x1 + x2) / 2) / image_width
            y_center = ((y1 + y2) / 2) / image_height
            width = abs(x2 - x1) / image_width
            height = abs(y2 - y1) / image_height

            txt_line = f"0 {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}"
            txt_lines.append(txt_line)

        # 写入 TXT 文件
        txt_filename = os.path.splitext(filename)[0] + ".txt"
        txt_path = os.path.join(txt_folder, txt_filename)
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write("\n".join(txt_lines))

print("转换完成。")

