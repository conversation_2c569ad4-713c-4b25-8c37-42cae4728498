#!/usr/bin/env python3
"""
有效的YOLO模型剪枝工具
使用结构化剪枝真正减少模型大小和参数数量
"""

import torch
import torch.nn as nn
import numpy as np
import argparse
import os
import copy
import time
from pathlib import Path

# 添加yolov5路径
import sys
sys.path.append('yolov5')

from yolov5.utils.torch_utils import select_device
from yolov5.models.yolo import Model

def load_model(weights, device):
    """加载YOLOv5模型"""
    print(f"加载模型: {weights}")

    # 加载模型字典
    model_dict = torch.load(weights, map_location=device)

    if isinstance(model_dict, dict):
        print("模型是字典格式")
        if 'ema' in model_dict and model_dict['ema'] is not None:
            model = model_dict['ema']
            print("使用EMA模型")
        elif 'model' in model_dict and model_dict['model'] is not None:
            model = model_dict['model']
            print("使用普通模型")
        else:
            # 尝试从state_dict重建模型
            print("尝试从state_dict重建模型")
            from yolov5.models.yolo import Model
            model = Model('yolov5/models/yolov5s.yaml', ch=3, nc=1)  # 假设是单类别
            if 'model' in model_dict:
                model.load_state_dict(model_dict['model'])
            else:
                model.load_state_dict(model_dict)
    else:
        model = model_dict
        model_dict = {'model': model}

    # 转换为float32
    if hasattr(model, 'float'):
        model = model.float()

    print(f"模型类型: {type(model)}")
    return model, model_dict

def analyze_model(model):
    """分析模型结构和参数"""
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型总参数量: {total_params:,}")

    # 分析各层参数
    print("\n主要层参数分布:")
    for name, module in model.named_modules():
        if isinstance(module, nn.Conv2d):
            params = sum(p.numel() for p in module.parameters())
            print(f"{name}: {params:,} 参数 (输入:{module.in_channels}, 输出:{module.out_channels})")

def get_conv_layers(model):
    """获取所有卷积层（除了检测头）"""
    conv_layers = []
    for name, module in model.named_modules():
        if isinstance(module, nn.Conv2d) and not name.startswith('model.24'):
            conv_layers.append((name, module))
    return conv_layers

def calculate_channel_importance(conv_layer):
    """计算通道重要性（基于L1范数）"""
    weight = conv_layer.weight.data
    # 计算每个输出通道的L1范数
    channel_importance = torch.sum(torch.abs(weight), dim=(1, 2, 3))
    return channel_importance

def prune_conv_layer(conv_layer, keep_ratio=0.8):
    """剪枝单个卷积层"""
    # 计算通道重要性
    importance = calculate_channel_importance(conv_layer)

    # 确定要保留的通道数
    num_channels = conv_layer.out_channels
    num_keep = max(1, int(num_channels * keep_ratio))

    # 选择最重要的通道
    _, indices = torch.topk(importance, num_keep)
    indices = torch.sort(indices)[0]  # 保持原始顺序

    # 创建新的卷积层
    new_conv = nn.Conv2d(
        in_channels=conv_layer.in_channels,
        out_channels=num_keep,
        kernel_size=conv_layer.kernel_size,
        stride=conv_layer.stride,
        padding=conv_layer.padding,
        dilation=conv_layer.dilation,
        groups=conv_layer.groups,
        bias=conv_layer.bias is not None
    )

    # 复制权重
    new_conv.weight.data = conv_layer.weight.data[indices]
    if conv_layer.bias is not None:
        new_conv.bias.data = conv_layer.bias.data[indices]

    return new_conv, indices

def prune_bn_layer(bn_layer, indices):
    """根据通道索引剪枝BatchNorm层"""
    if bn_layer is None:
        return None

    num_keep = len(indices)
    new_bn = nn.BatchNorm2d(num_keep)

    # 复制参数
    new_bn.weight.data = bn_layer.weight.data[indices]
    new_bn.bias.data = bn_layer.bias.data[indices]
    new_bn.running_mean.data = bn_layer.running_mean.data[indices]
    new_bn.running_var.data = bn_layer.running_var.data[indices]
    new_bn.num_batches_tracked = bn_layer.num_batches_tracked

    return new_bn

def structured_prune_model(model, prune_ratio=0.2):
    """结构化剪枝模型"""
    print(f"开始结构化剪枝，剪枝比例: {prune_ratio*100:.1f}%")

    # 创建模型副本
    pruned_model = copy.deepcopy(model)

    # 获取所有卷积层
    conv_layers = get_conv_layers(pruned_model)

    # 存储每层的通道映射
    channel_mapping = {}

    # 逐层剪枝
    for name, conv in conv_layers:
        keep_ratio = 1.0 - prune_ratio

        # 剪枝卷积层
        new_conv, indices = prune_conv_layer(conv, keep_ratio)
        channel_mapping[name] = indices

        # 替换卷积层
        parent_name = '.'.join(name.split('.')[:-1])
        layer_name = name.split('.')[-1]

        if parent_name:
            parent_module = pruned_model
            for part in parent_name.split('.'):
                parent_module = getattr(parent_module, part)
            setattr(parent_module, layer_name, new_conv)
        else:
            setattr(pruned_model, layer_name, new_conv)

        # 查找并剪枝对应的BatchNorm层
        bn_name = name.replace('.conv', '.bn')
        try:
            bn_parent_name = '.'.join(bn_name.split('.')[:-1])
            bn_layer_name = bn_name.split('.')[-1]

            if bn_parent_name:
                bn_parent_module = pruned_model
                for part in bn_parent_name.split('.'):
                    bn_parent_module = getattr(bn_parent_module, part)

                if hasattr(bn_parent_module, bn_layer_name):
                    old_bn = getattr(bn_parent_module, bn_layer_name)
                    new_bn = prune_bn_layer(old_bn, indices)
                    if new_bn is not None:
                        setattr(bn_parent_module, bn_layer_name, new_bn)
        except:
            pass  # 如果没有对应的BN层，跳过

    return pruned_model

def benchmark_model(model, img_size=640, device='cpu', num_iterations=50):
    """基准测试模型性能"""
    print("基准测试模型推理速度...")

    model.eval()
    model = model.to(device)

    # 创建随机输入
    dummy_input = torch.randn(1, 3, img_size, img_size).to(device)

    # 预热
    print("开始预热...")
    with torch.no_grad():
        for _ in range(5):
            _ = model(dummy_input)

    # 计时
    print(f"开始计时 {num_iterations} 次迭代...")
    start_time = time.time()

    with torch.no_grad():
        for _ in range(num_iterations):
            _ = model(dummy_input)

    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / num_iterations
    fps = 1.0 / avg_time

    print(f"推理速度: {fps:.2f} FPS (每帧 {avg_time*1000:.2f} ms)")
    print(f"{num_iterations}次迭代总耗时: {total_time:.2f} 秒")

    return fps, avg_time

def save_model(model, model_dict, save_path):
    """保存剪枝后的模型"""
    # 更新模型字典
    if 'ema' in model_dict:
        model_dict['ema'] = model
    else:
        model_dict['model'] = model

    # 保存模型
    torch.save(model_dict, save_path)
    print(f"剪枝后的模型已保存到: {save_path}")

def main():
    parser = argparse.ArgumentParser(description='有效的YOLO模型剪枝工具')
    parser.add_argument('--weights', type=str, default='yolov5/best_22.pt', help='模型权重文件路径')
    parser.add_argument('--device', type=str, default='cpu', help='设备 (cpu 或 cuda:0 等)')
    parser.add_argument('--prune-ratio', type=float, default=0.2, help='剪枝比例 (0.0-1.0)')
    parser.add_argument('--save-path', type=str, default='effective_pruned_model.pt', help='剪枝后模型保存路径')
    parser.add_argument('--benchmark', action='store_true', help='是否进行基准测试')
    parser.add_argument('--img-size', type=int, default=640, help='图像大小')

    args = parser.parse_args()

    # 选择设备
    device = select_device(args.device)

    # 加载模型
    model, model_dict = load_model(args.weights, device)

    # 分析原始模型
    print("\n原始模型信息:")
    analyze_model(model)

    # 如果需要，进行基准测试
    if args.benchmark:
        print("\n原始模型基准测试:")
        benchmark_model(model, args.img_size, device)

    # 剪枝模型
    print("\n开始结构化剪枝...")
    pruned_model = structured_prune_model(model, args.prune_ratio)

    # 分析剪枝后的模型
    print("\n剪枝后模型信息:")
    analyze_model(pruned_model)

    # 保存剪枝后的模型
    save_model(pruned_model, model_dict, args.save_path)

    # 如果需要，进行基准测试
    if args.benchmark:
        print("\n剪枝后模型基准测试:")
        benchmark_model(pruned_model, args.img_size, device)

    print("\n结构化剪枝完成!")

if __name__ == '__main__':
    main()
