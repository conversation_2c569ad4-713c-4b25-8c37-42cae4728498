{"experiment_id": "pruning_exp_20250604_212622", "start_time": "2025-06-04T21:26:22.664349", "original_model": {"path": "yolov5/best_22.pt", "file_size_mb": 13.783973693847656, "parameters": {"total": 7022326, "trainable": 0, "non_trainable": 7022326}, "layers": 25}, "pruning_method": "sparse_pruning", "pruning_params": {"percent": 0.3, "method": "BN_weight_based"}, "pruned_model": {"path": "sparse_pruned_yolo_30percent.pt", "file_size_mb": 27.220909118652344, "saved_path": "pruning_logs\\experiments\\pruning_exp_20250604_212622\\sparse_pruned_yolo_30percent.pt"}, "performance_before": {"fps": 13.511158299740426, "inference_time_ms": 74.01289939880371, "total_time_s": 7.401289939880371, "num_runs": 100}, "performance_after": {"fps": 12.929523298921428, "inference_time_ms": 77.3423719406128, "total_time_s": 7.734237194061279, "num_runs": 100}, "pruning_results": {"target_prune_ratio": 0.3, "actual_prune_ratio": 0.3002946127946128, "pruned_channels": 2854, "total_channels": 9504, "threshold": 0.84814453125, "prunable_bn_layers": 57}, "notes": "稀疏剪枝 10.0% 完成，模型保存至 sparse_pruned_yolo_10percent.pt\n稀疏剪枝 20.0% 完成，模型保存至 sparse_pruned_yolo_20percent.pt\n稀疏剪枝 30.0% 完成，模型保存至 sparse_pruned_yolo_30percent.pt", "end_time": "2025-06-04T21:27:10.242347", "improvements": {"fps_improvement_percent": -4.30484928024396, "time_reduction_percent": -4.498503056702163, "size_reduction_percent": -97.48230606970859}}