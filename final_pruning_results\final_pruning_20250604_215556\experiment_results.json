{"experiment_id": "final_pruning_20250604_215556", "start_time": "2025-06-04T21:55:56.445566", "original_model_path": "yolov5/best_22.pt", "device": "cpu", "results": [{"prune_ratio": 0.1, "prune_info": {"target_ratio": 0.1, "actual_ratio": 0.10048400673400673, "threshold": "0.62939453", "pruned_channels": 955, "total_channels": 9504, "prunable_bn_layers": 57, "ignored_bn_layers": 0}, "original_model": {"info": {"total_params": 7022326, "trainable_params": 0, "model_layers": 270, "file_size_mb": 13.783973693847656}, "inference": {"fps": 16.55405410738911, "inference_time_ms": 60.40816307067871, "total_time_s": 3.0204081535339355, "num_runs": 50}, "detection": {"total_images": 15, "total_detections": 68, "avg_detections_per_image": 4.533333333333333, "avg_processing_time_ms": 78.99155616760254, "detection_details": [{"image": "test_frame_001.jpg", "detections": 5, "processing_time_ms": 73.67730140686035}, {"image": "test_frame_002.jpg", "detections": 5, "processing_time_ms": 72.80135154724121}, {"image": "test_frame_003.jpg", "detections": 5, "processing_time_ms": 78.32002639770508}, {"image": "test_frame_004.jpg", "detections": 5, "processing_time_ms": 79.52523231506348}, {"image": "test_frame_005.jpg", "detections": 5, "processing_time_ms": 68.54653358459473}, {"image": "test_frame_006.jpg", "detections": 4, "processing_time_ms": 91.15934371948242}, {"image": "test_frame_007.jpg", "detections": 4, "processing_time_ms": 86.58981323242188}, {"image": "test_frame_008.jpg", "detections": 4, "processing_time_ms": 79.67305183410645}, {"image": "test_frame_009.jpg", "detections": 4, "processing_time_ms": 86.29775047302246}, {"image": "test_frame_010.jpg", "detections": 4, "processing_time_ms": 79.83636856079102}, {"image": "test_frame_011.jpg", "detections": 4, "processing_time_ms": 86.92812919616699}, {"image": "test_frame_012.jpg", "detections": 4, "processing_time_ms": 72.21341133117676}, {"image": "test_frame_013.jpg", "detections": 4, "processing_time_ms": 79.10656929016113}, {"image": "test_frame_014.jpg", "detections": 5, "processing_time_ms": 74.35917854309082}, {"image": "test_frame_015.jpg", "detections": 6, "processing_time_ms": 75.83928108215332}], "detection_rate": 100}}, "pruned_model": {"info": {"total_params": 7022326, "trainable_params": 0, "model_layers": 270, "file_size_mb": 27.21855354309082}, "inference": {"fps": 14.011198124492866, "inference_time_ms": 71.37148380279541, "total_time_s": 3.5685741901397705, "num_runs": 50}, "detection": {"total_images": 15, "total_detections": 0, "avg_detections_per_image": 0.0, "avg_processing_time_ms": 74.67452685038249, "detection_details": [{"image": "test_frame_001.jpg", "detections": 0, "processing_time_ms": 90.08979797363281}, {"image": "test_frame_002.jpg", "detections": 0, "processing_time_ms": 72.29375839233398}, {"image": "test_frame_003.jpg", "detections": 0, "processing_time_ms": 68.15671920776367}, {"image": "test_frame_004.jpg", "detections": 0, "processing_time_ms": 77.68034934997559}, {"image": "test_frame_005.jpg", "detections": 0, "processing_time_ms": 70.7395076751709}, {"image": "test_frame_006.jpg", "detections": 0, "processing_time_ms": 65.03510475158691}, {"image": "test_frame_007.jpg", "detections": 0, "processing_time_ms": 79.48470115661621}, {"image": "test_frame_008.jpg", "detections": 0, "processing_time_ms": 64.44358825683594}, {"image": "test_frame_009.jpg", "detections": 0, "processing_time_ms": 63.98272514343262}, {"image": "test_frame_010.jpg", "detections": 0, "processing_time_ms": 90.00444412231445}, {"image": "test_frame_011.jpg", "detections": 0, "processing_time_ms": 81.30788803100586}, {"image": "test_frame_012.jpg", "detections": 0, "processing_time_ms": 81.50434494018555}, {"image": "test_frame_013.jpg", "detections": 0, "processing_time_ms": 65.29688835144043}, {"image": "test_frame_014.jpg", "detections": 0, "processing_time_ms": 65.02413749694824}, {"image": "test_frame_015.jpg", "detections": 0, "processing_time_ms": 85.07394790649414}], "detection_rate": 0}, "model_path": "final_pruning_results\\final_pruning_20250604_215556\\pruned_yolo_10percent.pt"}, "performance_changes": {"fps_change_percent": -15.36092588800473, "time_change_percent": 18.14874045961206, "detection_change": -4.533333333333333}}, {"prune_ratio": 0.15, "prune_info": {"target_ratio": 0.15, "actual_ratio": 0.1503577441077441, "threshold": "0.703125", "pruned_channels": 1429, "total_channels": 9504, "prunable_bn_layers": 57, "ignored_bn_layers": 0}, "original_model": {"info": {"total_params": 7022326, "trainable_params": 0, "model_layers": 270, "file_size_mb": 13.783973693847656}, "inference": {"fps": 15.517129499151206, "inference_time_ms": 64.44490909576416, "total_time_s": 3.222245454788208, "num_runs": 50}, "detection": {"total_images": 15, "total_detections": 68, "avg_detections_per_image": 4.533333333333333, "avg_processing_time_ms": 62.45826085408529, "detection_details": [{"image": "test_frame_001.jpg", "detections": 5, "processing_time_ms": 67.44766235351562}, {"image": "test_frame_002.jpg", "detections": 5, "processing_time_ms": 78.45544815063477}, {"image": "test_frame_003.jpg", "detections": 5, "processing_time_ms": 78.68790626525879}, {"image": "test_frame_004.jpg", "detections": 5, "processing_time_ms": 61.66696548461914}, {"image": "test_frame_005.jpg", "detections": 5, "processing_time_ms": 67.00301170349121}, {"image": "test_frame_006.jpg", "detections": 4, "processing_time_ms": 76.77364349365234}, {"image": "test_frame_007.jpg", "detections": 4, "processing_time_ms": 59.71789360046387}, {"image": "test_frame_008.jpg", "detections": 4, "processing_time_ms": 60.84942817687988}, {"image": "test_frame_009.jpg", "detections": 4, "processing_time_ms": 59.30328369140625}, {"image": "test_frame_010.jpg", "detections": 4, "processing_time_ms": 53.8790225982666}, {"image": "test_frame_011.jpg", "detections": 4, "processing_time_ms": 59.0822696685791}, {"image": "test_frame_012.jpg", "detections": 4, "processing_time_ms": 62.519073486328125}, {"image": "test_frame_013.jpg", "detections": 4, "processing_time_ms": 51.31411552429199}, {"image": "test_frame_014.jpg", "detections": 5, "processing_time_ms": 48.093318939208984}, {"image": "test_frame_015.jpg", "detections": 6, "processing_time_ms": 52.08086967468262}], "detection_rate": 100}}, "pruned_model": {"info": {"total_params": 7022326, "trainable_params": 0, "model_layers": 270, "file_size_mb": 27.21855354309082}, "inference": {"fps": 19.60190912682675, "inference_time_ms": 51.0154390335083, "total_time_s": 2.550771951675415, "num_runs": 50}, "detection": {"total_images": 15, "total_detections": 0, "avg_detections_per_image": 0.0, "avg_processing_time_ms": 51.523780822753906, "detection_details": [{"image": "test_frame_001.jpg", "detections": 0, "processing_time_ms": 50.508737564086914}, {"image": "test_frame_002.jpg", "detections": 0, "processing_time_ms": 53.73263359069824}, {"image": "test_frame_003.jpg", "detections": 0, "processing_time_ms": 50.39238929748535}, {"image": "test_frame_004.jpg", "detections": 0, "processing_time_ms": 54.28934097290039}, {"image": "test_frame_005.jpg", "detections": 0, "processing_time_ms": 47.34206199645996}, {"image": "test_frame_006.jpg", "detections": 0, "processing_time_ms": 47.52969741821289}, {"image": "test_frame_007.jpg", "detections": 0, "processing_time_ms": 52.33454704284668}, {"image": "test_frame_008.jpg", "detections": 0, "processing_time_ms": 54.29267883300781}, {"image": "test_frame_009.jpg", "detections": 0, "processing_time_ms": 48.54249954223633}, {"image": "test_frame_010.jpg", "detections": 0, "processing_time_ms": 50.75716972351074}, {"image": "test_frame_011.jpg", "detections": 0, "processing_time_ms": 54.311513900756836}, {"image": "test_frame_012.jpg", "detections": 0, "processing_time_ms": 54.54301834106445}, {"image": "test_frame_013.jpg", "detections": 0, "processing_time_ms": 49.227237701416016}, {"image": "test_frame_014.jpg", "detections": 0, "processing_time_ms": 52.51359939575195}, {"image": "test_frame_015.jpg", "detections": 0, "processing_time_ms": 52.53958702087402}], "detection_rate": 0}, "model_path": "final_pruning_results\\final_pruning_20250604_215556\\pruned_yolo_15percent.pt"}, "performance_changes": {"fps_change_percent": 26.324325178177986, "time_change_percent": -20.83868260609984, "detection_change": -4.533333333333333}}, {"prune_ratio": 0.2, "prune_info": {"target_ratio": 0.2, "actual_ratio": 0.20002104377104377, "threshold": "0.7573242", "pruned_channels": 1901, "total_channels": 9504, "prunable_bn_layers": 57, "ignored_bn_layers": 0}, "original_model": {"info": {"total_params": 7022326, "trainable_params": 0, "model_layers": 270, "file_size_mb": 13.783973693847656}, "inference": {"fps": 16.43065407205068, "inference_time_ms": 60.861849784851074, "total_time_s": 3.0430924892425537, "num_runs": 50}, "detection": {"total_images": 15, "total_detections": 68, "avg_detections_per_image": 4.533333333333333, "avg_processing_time_ms": 60.83273887634277, "detection_details": [{"image": "test_frame_001.jpg", "detections": 5, "processing_time_ms": 63.166141510009766}, {"image": "test_frame_002.jpg", "detections": 5, "processing_time_ms": 70.04714012145996}, {"image": "test_frame_003.jpg", "detections": 5, "processing_time_ms": 60.32752990722656}, {"image": "test_frame_004.jpg", "detections": 5, "processing_time_ms": 57.50727653503418}, {"image": "test_frame_005.jpg", "detections": 5, "processing_time_ms": 61.54513359069824}, {"image": "test_frame_006.jpg", "detections": 4, "processing_time_ms": 59.82327461242676}, {"image": "test_frame_007.jpg", "detections": 4, "processing_time_ms": 55.89580535888672}, {"image": "test_frame_008.jpg", "detections": 4, "processing_time_ms": 60.91928482055664}, {"image": "test_frame_009.jpg", "detections": 4, "processing_time_ms": 65.15693664550781}, {"image": "test_frame_010.jpg", "detections": 4, "processing_time_ms": 54.566383361816406}, {"image": "test_frame_011.jpg", "detections": 4, "processing_time_ms": 57.32369422912598}, {"image": "test_frame_012.jpg", "detections": 4, "processing_time_ms": 63.92550468444824}, {"image": "test_frame_013.jpg", "detections": 4, "processing_time_ms": 58.54392051696777}, {"image": "test_frame_014.jpg", "detections": 5, "processing_time_ms": 56.20837211608887}, {"image": "test_frame_015.jpg", "detections": 6, "processing_time_ms": 67.5346851348877}], "detection_rate": 100}}, "pruned_model": {"info": {"total_params": 7022326, "trainable_params": 0, "model_layers": 270, "file_size_mb": 27.21855354309082}, "inference": {"fps": 16.73047773739282, "inference_time_ms": 59.771156311035156, "total_time_s": 2.988557815551758, "num_runs": 50}, "detection": {"total_images": 15, "total_detections": 0, "avg_detections_per_image": 0.0, "avg_processing_time_ms": 62.59015401204427, "detection_details": [{"image": "test_frame_001.jpg", "detections": 0, "processing_time_ms": 60.526371002197266}, {"image": "test_frame_002.jpg", "detections": 0, "processing_time_ms": 61.180830001831055}, {"image": "test_frame_003.jpg", "detections": 0, "processing_time_ms": 57.83987045288086}, {"image": "test_frame_004.jpg", "detections": 0, "processing_time_ms": 72.81684875488281}, {"image": "test_frame_005.jpg", "detections": 0, "processing_time_ms": 62.81471252441406}, {"image": "test_frame_006.jpg", "detections": 0, "processing_time_ms": 62.27254867553711}, {"image": "test_frame_007.jpg", "detections": 0, "processing_time_ms": 59.78679656982422}, {"image": "test_frame_008.jpg", "detections": 0, "processing_time_ms": 65.01293182373047}, {"image": "test_frame_009.jpg", "detections": 0, "processing_time_ms": 60.68277359008789}, {"image": "test_frame_010.jpg", "detections": 0, "processing_time_ms": 61.92421913146973}, {"image": "test_frame_011.jpg", "detections": 0, "processing_time_ms": 64.78571891784668}, {"image": "test_frame_012.jpg", "detections": 0, "processing_time_ms": 63.51733207702637}, {"image": "test_frame_013.jpg", "detections": 0, "processing_time_ms": 62.537193298339844}, {"image": "test_frame_014.jpg", "detections": 0, "processing_time_ms": 62.07895278930664}, {"image": "test_frame_015.jpg", "detections": 0, "processing_time_ms": 61.07521057128906}], "detection_rate": 0}, "model_path": "final_pruning_results\\final_pruning_20250604_215556\\pruned_yolo_20percent.pt"}, "performance_changes": {"fps_change_percent": 1.8247822881996916, "time_change_percent": -1.7920807166912613, "detection_change": -4.533333333333333}}, {"prune_ratio": 0.25, "prune_info": {"target_ratio": 0.25, "actual_ratio": 0.25042087542087543, "threshold": "0.80615234", "pruned_channels": 2380, "total_channels": 9504, "prunable_bn_layers": 57, "ignored_bn_layers": 0}, "original_model": {"info": {"total_params": 7022326, "trainable_params": 0, "model_layers": 270, "file_size_mb": 13.783973693847656}, "inference": {"fps": 16.291064567599523, "inference_time_ms": 61.38334274291992, "total_time_s": 3.069167137145996, "num_runs": 50}, "detection": {"total_images": 15, "total_detections": 68, "avg_detections_per_image": 4.533333333333333, "avg_processing_time_ms": 59.515889485677086, "detection_details": [{"image": "test_frame_001.jpg", "detections": 5, "processing_time_ms": 58.500051498413086}, {"image": "test_frame_002.jpg", "detections": 5, "processing_time_ms": 59.07011032104492}, {"image": "test_frame_003.jpg", "detections": 5, "processing_time_ms": 69.20456886291504}, {"image": "test_frame_004.jpg", "detections": 5, "processing_time_ms": 54.55279350280762}, {"image": "test_frame_005.jpg", "detections": 5, "processing_time_ms": 55.817604064941406}, {"image": "test_frame_006.jpg", "detections": 4, "processing_time_ms": 57.50584602355957}, {"image": "test_frame_007.jpg", "detections": 4, "processing_time_ms": 63.387155532836914}, {"image": "test_frame_008.jpg", "detections": 4, "processing_time_ms": 57.462453842163086}, {"image": "test_frame_009.jpg", "detections": 4, "processing_time_ms": 54.915428161621094}, {"image": "test_frame_010.jpg", "detections": 4, "processing_time_ms": 66.59865379333496}, {"image": "test_frame_011.jpg", "detections": 4, "processing_time_ms": 56.658267974853516}, {"image": "test_frame_012.jpg", "detections": 4, "processing_time_ms": 57.28626251220703}, {"image": "test_frame_013.jpg", "detections": 4, "processing_time_ms": 64.2538070678711}, {"image": "test_frame_014.jpg", "detections": 5, "processing_time_ms": 59.267520904541016}, {"image": "test_frame_015.jpg", "detections": 6, "processing_time_ms": 58.2578182220459}], "detection_rate": 100}}, "pruned_model": {"info": {"total_params": 7022326, "trainable_params": 0, "model_layers": 270, "file_size_mb": 27.21855354309082}, "inference": {"fps": 16.893442887062992, "inference_time_ms": 59.19456481933594, "total_time_s": 2.959728240966797, "num_runs": 50}, "detection": {"total_images": 15, "total_detections": 0, "avg_detections_per_image": 0.0, "avg_processing_time_ms": 58.57241948445638, "detection_details": [{"image": "test_frame_001.jpg", "detections": 0, "processing_time_ms": 58.97402763366699}, {"image": "test_frame_002.jpg", "detections": 0, "processing_time_ms": 53.61795425415039}, {"image": "test_frame_003.jpg", "detections": 0, "processing_time_ms": 55.581092834472656}, {"image": "test_frame_004.jpg", "detections": 0, "processing_time_ms": 60.95457077026367}, {"image": "test_frame_005.jpg", "detections": 0, "processing_time_ms": 54.29387092590332}, {"image": "test_frame_006.jpg", "detections": 0, "processing_time_ms": 58.33625793457031}, {"image": "test_frame_007.jpg", "detections": 0, "processing_time_ms": 64.21995162963867}, {"image": "test_frame_008.jpg", "detections": 0, "processing_time_ms": 58.18915367126465}, {"image": "test_frame_009.jpg", "detections": 0, "processing_time_ms": 57.31678009033203}, {"image": "test_frame_010.jpg", "detections": 0, "processing_time_ms": 58.75849723815918}, {"image": "test_frame_011.jpg", "detections": 0, "processing_time_ms": 63.2169246673584}, {"image": "test_frame_012.jpg", "detections": 0, "processing_time_ms": 57.58476257324219}, {"image": "test_frame_013.jpg", "detections": 0, "processing_time_ms": 58.691978454589844}, {"image": "test_frame_014.jpg", "detections": 0, "processing_time_ms": 62.31212615966797}, {"image": "test_frame_015.jpg", "detections": 0, "processing_time_ms": 56.53834342956543}], "detection_rate": 0}, "model_path": "final_pruning_results\\final_pruning_20250604_215556\\pruned_yolo_25percent.pt"}, "performance_changes": {"fps_change_percent": 3.6975994844530216, "time_change_percent": -3.56575224772431, "detection_change": -4.533333333333333}}, {"prune_ratio": 0.3, "prune_info": {"target_ratio": 0.3, "actual_ratio": 0.3002946127946128, "threshold": "0.84814453", "pruned_channels": 2854, "total_channels": 9504, "prunable_bn_layers": 57, "ignored_bn_layers": 0}, "original_model": {"info": {"total_params": 7022326, "trainable_params": 0, "model_layers": 270, "file_size_mb": 13.783973693847656}, "inference": {"fps": 16.772472744707798, "inference_time_ms": 59.621500968933105, "total_time_s": 2.9810750484466553, "num_runs": 50}, "detection": {"total_images": 15, "total_detections": 68, "avg_detections_per_image": 4.533333333333333, "avg_processing_time_ms": 63.08856010437012, "detection_details": [{"image": "test_frame_001.jpg", "detections": 5, "processing_time_ms": 70.74785232543945}, {"image": "test_frame_002.jpg", "detections": 5, "processing_time_ms": 57.459115982055664}, {"image": "test_frame_003.jpg", "detections": 5, "processing_time_ms": 60.038089752197266}, {"image": "test_frame_004.jpg", "detections": 5, "processing_time_ms": 77.81004905700684}, {"image": "test_frame_005.jpg", "detections": 5, "processing_time_ms": 70.79195976257324}, {"image": "test_frame_006.jpg", "detections": 4, "processing_time_ms": 64.6202564239502}, {"image": "test_frame_007.jpg", "detections": 4, "processing_time_ms": 67.04854965209961}, {"image": "test_frame_008.jpg", "detections": 4, "processing_time_ms": 58.61926078796387}, {"image": "test_frame_009.jpg", "detections": 4, "processing_time_ms": 62.29972839355469}, {"image": "test_frame_010.jpg", "detections": 4, "processing_time_ms": 60.4710578918457}, {"image": "test_frame_011.jpg", "detections": 4, "processing_time_ms": 61.7070198059082}, {"image": "test_frame_012.jpg", "detections": 4, "processing_time_ms": 57.036638259887695}, {"image": "test_frame_013.jpg", "detections": 4, "processing_time_ms": 58.155059814453125}, {"image": "test_frame_014.jpg", "detections": 5, "processing_time_ms": 63.18354606628418}, {"image": "test_frame_015.jpg", "detections": 6, "processing_time_ms": 56.34021759033203}], "detection_rate": 100}}, "pruned_model": {"info": {"total_params": 7022326, "trainable_params": 0, "model_layers": 270, "file_size_mb": 27.21855354309082}, "inference": {"fps": 16.156644456550136, "inference_time_ms": 61.89404010772705, "total_time_s": 3.0947020053863525, "num_runs": 50}, "detection": {"total_images": 15, "total_detections": 0, "avg_detections_per_image": 0.0, "avg_processing_time_ms": 60.49931844075521, "detection_details": [{"image": "test_frame_001.jpg", "detections": 0, "processing_time_ms": 59.41438674926758}, {"image": "test_frame_002.jpg", "detections": 0, "processing_time_ms": 64.04876708984375}, {"image": "test_frame_003.jpg", "detections": 0, "processing_time_ms": 58.37559700012207}, {"image": "test_frame_004.jpg", "detections": 0, "processing_time_ms": 58.90226364135742}, {"image": "test_frame_005.jpg", "detections": 0, "processing_time_ms": 65.77396392822266}, {"image": "test_frame_006.jpg", "detections": 0, "processing_time_ms": 57.93166160583496}, {"image": "test_frame_007.jpg", "detections": 0, "processing_time_ms": 58.83169174194336}, {"image": "test_frame_008.jpg", "detections": 0, "processing_time_ms": 63.40789794921875}, {"image": "test_frame_009.jpg", "detections": 0, "processing_time_ms": 59.72886085510254}, {"image": "test_frame_010.jpg", "detections": 0, "processing_time_ms": 58.313608169555664}, {"image": "test_frame_011.jpg", "detections": 0, "processing_time_ms": 61.69462203979492}, {"image": "test_frame_012.jpg", "detections": 0, "processing_time_ms": 62.27231025695801}, {"image": "test_frame_013.jpg", "detections": 0, "processing_time_ms": 55.87267875671387}, {"image": "test_frame_014.jpg", "detections": 0, "processing_time_ms": 57.170867919921875}, {"image": "test_frame_015.jpg", "detections": 0, "processing_time_ms": 65.7505989074707}], "detection_rate": 0}, "model_path": "final_pruning_results\\final_pruning_20250604_215556\\pruned_yolo_30percent.pt"}, "performance_changes": {"fps_change_percent": -3.6716606879088447, "time_change_percent": 3.811610076670317, "detection_change": -4.533333333333333}}], "end_time": "2025-06-04T21:56:45.175254"}