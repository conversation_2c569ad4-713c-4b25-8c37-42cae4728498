#!/usr/bin/env python3
"""
性能统计功能测试脚本
"""

import time
import numpy as np
from performance_tracker import PerformanceTracker, TimingContext

def simulate_yolo_detection(duration_ms=20):
    """模拟YOLO检测"""
    time.sleep(duration_ms / 1000.0)

def simulate_deepsort_tracking(duration_ms=15):
    """模拟DeepSORT跟踪"""
    time.sleep(duration_ms / 1000.0)

def simulate_other_operations(duration_ms=5):
    """模拟其他操作"""
    time.sleep(duration_ms / 1000.0)

def test_performance_tracker():
    """Test performance tracker"""
    print("Starting performance tracker test...")

    # Create performance tracker
    tracker = PerformanceTracker(save_dir="test_performance", window_size=10)

    # 模拟处理50帧
    for frame_id in range(1, 51):
        # 开始帧计时
        tracker.start_frame(frame_id)

        # 模拟YOLO检测
        tracker.start_yolo()
        yolo_time = np.random.normal(20, 5)  # 平均20ms，标准差5ms
        simulate_yolo_detection(max(10, yolo_time))
        tracker.end_yolo()

        # 模拟DeepSORT跟踪
        tracker.start_deepsort()
        deepsort_time = np.random.normal(15, 3)  # 平均15ms，标准差3ms
        simulate_deepsort_tracking(max(5, deepsort_time))
        tracker.end_deepsort()

        # 模拟其他操作
        with TimingContext(tracker, "worker_monitor"):
            other_time = np.random.normal(5, 1)  # 平均5ms，标准差1ms
            simulate_other_operations(max(1, other_time))

        with TimingContext(tracker, "video_save"):
            save_time = np.random.normal(3, 0.5)  # 平均3ms，标准差0.5ms
            simulate_other_operations(max(1, save_time))

        # 结束帧计时
        frame_stats = tracker.end_frame()

        # Output statistics every 10 frames
        if frame_id % 10 == 0:
            current_stats = tracker.get_current_stats()
            if current_stats:
                print(f"Frame {frame_id}: Real-time FPS = {current_stats['recent_fps']:.1f}")

    # Print final statistics
    tracker.print_summary()

    # Save Excel report
    excel_path = tracker.save_to_excel("test_performance_report.xlsx")
    print(f"Excel report saved: {excel_path}")

    # Generate charts
    try:
        tracker.plot_performance(save_plots=True)
        print("Performance charts generated")
    except Exception as e:
        print(f"Error generating charts: {e}")

    print("Test completed!")

if __name__ == "__main__":
    test_performance_tracker()
