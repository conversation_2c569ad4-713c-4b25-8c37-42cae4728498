#!/usr/bin/env python3
"""
验证剪枝模型在工人检测任务上的效果
"""

import argparse
import torch
import cv2
import numpy as np
import time
import os
import sys
from pathlib import Path
import json

# 添加yolov5路径
sys.path.append('yolov5')

from yolov5.utils.torch_utils import select_device
from yolov5.utils.general import non_max_suppression, scale_coords, xyxy2xywh

class PrunedModelValidator:
    def __init__(self, device='cpu'):
        """
        初始化剪枝模型验证器
        
        Args:
            device: 计算设备
        """
        self.device = select_device(device)
        print(f"🔍 剪枝模型验证器初始化")
        print(f"设备: {self.device}")
        
    def load_model(self, model_path):
        """加载模型"""
        print(f"📥 加载模型: {model_path}")
        
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        ckpt = torch.load(model_path, map_location=self.device, weights_only=False)
        
        if isinstance(ckpt, dict):
            if 'ema' in ckpt and ckpt['ema'] is not None:
                model = ckpt['ema']
            elif 'model' in ckpt and ckpt['model'] is not None:
                model = ckpt['model']
            else:
                raise ValueError("无法从模型字典中找到模型")
        else:
            model = ckpt
        
        model = model.float().to(self.device)
        model.eval()
        
        # 获取模型信息
        total_params = sum(p.numel() for p in model.parameters())
        model_size_mb = os.path.getsize(model_path) / (1024 * 1024)
        
        print(f"✅ 模型加载成功")
        print(f"   参数量: {total_params:,}")
        print(f"   文件大小: {model_size_mb:.1f} MB")
        
        return model, {
            "total_params": total_params,
            "file_size_mb": model_size_mb,
            "path": model_path
        }
    
    def preprocess_image(self, image_path, img_size=640):
        """预处理图像"""
        # 读取图像
        img0 = cv2.imread(image_path)
        if img0 is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # 调整大小
        img = cv2.resize(img0, (img_size, img_size))
        
        # 转换为RGB并归一化
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        img = img.transpose(2, 0, 1)  # HWC to CHW
        img = np.ascontiguousarray(img)
        img = torch.from_numpy(img).to(self.device)
        img = img.float() / 255.0  # 0-255 to 0.0-1.0
        
        if img.ndimension() == 3:
            img = img.unsqueeze(0)
        
        return img, img0
    
    def detect_workers(self, model, img, conf_thres=0.4, iou_thres=0.45):
        """检测工人"""
        with torch.no_grad():
            # 推理
            pred = model(img)[0]
            
            # NMS
            pred = non_max_suppression(pred, conf_thres, iou_thres, classes=[0])  # 只检测person类
        
        return pred
    
    def benchmark_detection(self, model, test_images, num_runs=10):
        """检测性能基准测试"""
        print(f"🔥 开始检测性能测试 ({num_runs} 次运行)")
        
        total_detections = 0
        total_time = 0
        detection_results = []
        
        for image_path in test_images[:num_runs]:  # 限制测试图像数量
            if not os.path.exists(image_path):
                print(f"⚠️ 图像不存在: {image_path}")
                continue
            
            try:
                # 预处理
                img, img0 = self.preprocess_image(image_path)
                
                # 计时检测
                start_time = time.time()
                detections = self.detect_workers(model, img)
                end_time = time.time()
                
                inference_time = end_time - start_time
                total_time += inference_time
                
                # 统计检测结果
                num_detections = len(detections[0]) if len(detections) > 0 and detections[0] is not None else 0
                total_detections += num_detections
                
                detection_results.append({
                    "image": os.path.basename(image_path),
                    "detections": num_detections,
                    "inference_time_ms": inference_time * 1000
                })
                
                print(f"   📷 {os.path.basename(image_path)}: {num_detections} 个工人, {inference_time*1000:.1f}ms")
                
            except Exception as e:
                print(f"❌ 处理图像失败 {image_path}: {e}")
                continue
        
        # 计算平均性能
        avg_time_ms = (total_time / len(detection_results)) * 1000 if detection_results else 0
        avg_fps = len(detection_results) / total_time if total_time > 0 else 0
        avg_detections = total_detections / len(detection_results) if detection_results else 0
        
        return {
            "total_images": len(detection_results),
            "total_detections": total_detections,
            "avg_detections_per_image": avg_detections,
            "avg_inference_time_ms": avg_time_ms,
            "avg_fps": avg_fps,
            "total_time_s": total_time,
            "detection_results": detection_results
        }
    
    def validate_model(self, model_path, test_images):
        """验证单个模型"""
        print(f"\n{'='*60}")
        print(f"🧪 验证模型: {os.path.basename(model_path)}")
        print(f"{'='*60}")
        
        # 加载模型
        model, model_info = self.load_model(model_path)
        
        # 性能测试
        performance = self.benchmark_detection(model, test_images)
        
        # 合并结果
        result = {
            "model_path": model_path,
            "model_info": model_info,
            "performance": performance
        }
        
        print(f"📊 验证结果:")
        print(f"   平均检测数量: {performance['avg_detections_per_image']:.1f} 个工人/图像")
        print(f"   平均推理时间: {performance['avg_inference_time_ms']:.1f} ms")
        print(f"   平均FPS: {performance['avg_fps']:.1f}")
        
        return result
    
    def compare_models(self, model_paths, test_images):
        """比较多个模型"""
        print(f"🔬 开始模型对比验证")
        print(f"测试图像数量: {len(test_images)}")
        print(f"模型数量: {len(model_paths)}")
        
        results = []
        
        for model_path in model_paths:
            try:
                result = self.validate_model(model_path, test_images)
                results.append(result)
            except Exception as e:
                print(f"❌ 模型验证失败 {model_path}: {e}")
                continue
        
        # 生成对比报告
        self.generate_comparison_report(results)
        
        return results
    
    def generate_comparison_report(self, results):
        """生成对比报告"""
        print(f"\n{'='*80}")
        print(f"📊 模型对比报告")
        print(f"{'='*80}")
        
        if not results:
            print("❌ 没有有效的验证结果")
            return
        
        # 表头
        print(f"{'模型':<30} {'参数量':<12} {'大小(MB)':<10} {'检测数':<8} {'时间(ms)':<10} {'FPS':<8}")
        print("-" * 80)
        
        # 数据行
        for result in results:
            model_name = os.path.basename(result["model_path"])
            if len(model_name) > 28:
                model_name = model_name[:25] + "..."
            
            params = result["model_info"]["total_params"]
            size_mb = result["model_info"]["file_size_mb"]
            avg_detections = result["performance"]["avg_detections_per_image"]
            avg_time = result["performance"]["avg_inference_time_ms"]
            avg_fps = result["performance"]["avg_fps"]
            
            print(f"{model_name:<30} {params:<12,} {size_mb:<10.1f} {avg_detections:<8.1f} {avg_time:<10.1f} {avg_fps:<8.1f}")
        
        # 保存详细报告
        report_path = "pruned_models_validation_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 详细报告已保存: {report_path}")

def find_test_images(image_dir="image"):
    """查找测试图像"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    test_images = []
    
    if os.path.exists(image_dir):
        for file in os.listdir(image_dir):
            if any(file.lower().endswith(ext) for ext in image_extensions):
                test_images.append(os.path.join(image_dir, file))
    
    return test_images

def find_pruned_models():
    """查找剪枝模型"""
    model_paths = []
    
    # 查找最新实验的剪枝模型
    experiments_dir = Path("pruning_logs/experiments")
    if experiments_dir.exists():
        # 按时间排序，获取最新的实验
        exp_dirs = sorted([d for d in experiments_dir.iterdir() if d.is_dir()], 
                         key=lambda x: x.stat().st_mtime, reverse=True)
        
        for exp_dir in exp_dirs[:2]:  # 检查最新的2个实验
            for model_file in exp_dir.glob("*.pt"):
                if "pruned" in model_file.name:
                    model_paths.append(str(model_file))
    
    # 添加原始模型作为对比
    original_model = "yolov5/best_22.pt"
    if os.path.exists(original_model):
        model_paths.insert(0, original_model)  # 放在第一位
    
    return model_paths

def main():
    parser = argparse.ArgumentParser(description='验证剪枝模型在工人检测任务上的效果')
    parser.add_argument('--models', type=str, nargs='+', default=None,
                       help='模型路径列表 (默认自动查找)')
    parser.add_argument('--images', type=str, default='image',
                       help='测试图像目录')
    parser.add_argument('--device', type=str, default='cpu',
                       help='设备 (cpu, cuda:0 等)')
    
    args = parser.parse_args()
    
    # 查找测试图像
    test_images = find_test_images(args.images)
    if not test_images:
        print(f"❌ 在 {args.images} 目录中没有找到测试图像")
        return
    
    print(f"📷 找到 {len(test_images)} 张测试图像")
    
    # 查找模型
    if args.models:
        model_paths = args.models
    else:
        model_paths = find_pruned_models()
    
    if not model_paths:
        print("❌ 没有找到模型文件")
        return
    
    print(f"🤖 找到 {len(model_paths)} 个模型:")
    for model_path in model_paths:
        print(f"   - {model_path}")
    
    # 创建验证器并运行对比
    validator = PrunedModelValidator(args.device)
    results = validator.compare_models(model_paths, test_images)
    
    print(f"\n🎉 验证完成! 共验证了 {len(results)} 个模型")

if __name__ == '__main__':
    main()
