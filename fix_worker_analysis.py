#!/usr/bin/env python3
"""
修复工人分析中的时间计算和图表生成问题
"""

import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import os
import cv2
from pathlib import Path

# 设置matplotlib中文字体支持
matplotlib.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False

def load_worker_stats(stats_file):
    """加载工人统计数据"""
    with open(stats_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def get_video_info(video_file):
    """获取视频信息"""
    if not os.path.exists(video_file):
        print(f"视频文件不存在: {video_file}")
        return None
    
    try:
        cap = cv2.VideoCapture(video_file)
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        cap.release()
        
        video_info = {
            'fps': fps,
            'frame_count': frame_count,
            'width': width,
            'height': height,
            'duration': duration
        }
        
        print(f"视频信息: {width}x{height}, {fps:.2f} FPS, {frame_count} 帧, {duration:.2f} 秒")
        return video_info
    except Exception as e:
        print(f"获取视频信息失败: {e}")
        return None

def fix_worker_stats(worker_stats, video_info):
    """修复工人统计数据中的时间计算"""
    print("修复工人统计数据...")
    
    if not video_info:
        # 使用默认FPS
        fps = 30.0
        print(f"使用默认FPS: {fps}")
    else:
        fps = video_info['fps']
        print(f"使用视频FPS: {fps}")
    
    fixed_stats = {}
    
    for worker_id, stats in worker_stats.items():
        first_frame = stats['first_frame']
        last_frame = stats['last_frame']
        
        # 修复duration计算
        frame_duration = last_frame - first_frame + 1
        duration_seconds = frame_duration / fps
        
        # 修复avg_speed计算
        total_distance = stats['total_distance']
        avg_speed = total_distance / duration_seconds if duration_seconds > 0 else 0
        
        # 创建修复后的统计数据
        fixed_stats[worker_id] = {
            **stats,
            'duration': duration_seconds,
            'avg_speed': avg_speed,
            'frame_duration': frame_duration,
            'fps_used': fps
        }
        
        print(f"工人 {worker_id}: {frame_duration} 帧 = {duration_seconds:.2f} 秒, 平均速度: {avg_speed:.2f}")
    
    return fixed_stats

def generate_fixed_worker_count_chart(worker_stats, video_info, output_path):
    """生成修复后的工人数量时间图表"""
    print("生成修复后的工人数量时间图表...")
    
    if not video_info:
        fps = 30.0
        # 从工人统计数据中获取帧范围
        all_first_frames = [stats['first_frame'] for stats in worker_stats.values()]
        all_last_frames = [stats['last_frame'] for stats in worker_stats.values()]
        min_frame = min(all_first_frames) if all_first_frames else 1
        max_frame = max(all_last_frames) if all_last_frames else 100
        total_frames = max_frame - min_frame + 1
    else:
        fps = video_info['fps']
        min_frame = 1
        max_frame = video_info['frame_count']
        total_frames = max_frame
    
    print(f"帧范围: {min_frame} - {max_frame} (总计 {total_frames} 帧)")
    print(f"FPS: {fps}")
    
    # 每分钟为一个时间段
    time_interval_seconds = 60
    time_interval_frames = int(fps * time_interval_seconds)
    num_intervals = max(1, int(np.ceil(total_frames / time_interval_frames)))
    
    print(f"时间间隔: {time_interval_frames} 帧 ({time_interval_seconds} 秒)")
    print(f"时间段数量: {num_intervals}")
    
    # 计算每个时间段的工人数量
    worker_counts = np.zeros(num_intervals)
    time_labels = []
    
    for interval in range(num_intervals):
        start_frame = min_frame + interval * time_interval_frames
        end_frame = min(min_frame + (interval + 1) * time_interval_frames, max_frame + 1)
        
        # 统计该时间段内活跃的工人数量
        active_workers = 0
        for worker_id, stats in worker_stats.items():
            worker_first = stats['first_frame']
            worker_last = stats['last_frame']
            
            # 检查工人是否在该时间段内活跃
            if not (worker_last < start_frame or worker_first >= end_frame):
                active_workers += 1
        
        worker_counts[interval] = active_workers
        
        # 生成时间标签（分钟）
        time_minutes = interval * time_interval_seconds / 60
        time_labels.append(time_minutes)
        
        print(f"时间段 {interval}: 帧 {start_frame}-{end_frame}, 时间 {time_minutes:.1f}分钟, 工人数量: {active_workers}")
    
    # 创建图表
    plt.figure(figsize=(12, 6))
    
    # 绘制线图
    if len(time_labels) > 1:
        plt.plot(time_labels, worker_counts, marker='o', linestyle='-', 
                linewidth=3, markersize=10, color='#2E86AB', markerfacecolor='#A23B72')
    else:
        plt.scatter(time_labels, worker_counts, s=200, color='#2E86AB')
    
    # 设置图表属性
    plt.title('Worker Count Over Time (Fixed)', fontsize=16, fontweight='bold')
    plt.xlabel('Time (minutes)', fontsize=12)
    plt.ylabel('Worker Count', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 设置Y轴范围
    max_count = max(worker_counts) if len(worker_counts) > 0 and max(worker_counts) > 0 else 1
    plt.ylim(-0.5, max_count + 0.5)
    
    # 设置X轴范围
    if len(time_labels) > 1:
        plt.xlim(-0.5, max(time_labels) + 0.5)
    
    # 添加数值标签
    for i, (x, y) in enumerate(zip(time_labels, worker_counts)):
        plt.annotate(f'{int(y)}', (x, y), textcoords="offset points", 
                    xytext=(0,15), ha='center', fontsize=12, fontweight='bold')
    
    # 美化图表
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"修复后的工人数量时间图表已保存到: {output_path}")
    
    # 显示统计信息
    print(f"\n图表统计信息:")
    print(f"  总时间段: {num_intervals}")
    print(f"  最大工人数: {int(max(worker_counts))}")
    print(f"  最小工人数: {int(min(worker_counts))}")
    print(f"  平均工人数: {np.mean(worker_counts):.2f}")
    
    return worker_counts, time_labels

def generate_worker_timeline_chart(worker_stats, video_info, output_path):
    """生成工人时间线图表"""
    print("生成工人时间线图表...")
    
    if not video_info:
        fps = 30.0
    else:
        fps = video_info['fps']
    
    # 准备数据
    worker_data = []
    for worker_id, stats in worker_stats.items():
        start_time = stats['first_frame'] / fps / 60  # 转换为分钟
        end_time = stats['last_frame'] / fps / 60
        duration = end_time - start_time
        
        worker_data.append({
            'worker_id': int(worker_id),
            'start_time': start_time,
            'end_time': end_time,
            'duration': duration
        })
    
    # 按工人ID排序
    worker_data.sort(key=lambda x: x['worker_id'])
    
    # 创建时间线图表
    fig, ax = plt.subplots(figsize=(14, 8))
    
    colors = plt.cm.Set3(np.linspace(0, 1, len(worker_data)))
    
    for i, data in enumerate(worker_data):
        worker_id = data['worker_id']
        start_time = data['start_time']
        duration = data['duration']
        
        # 绘制时间线条
        ax.barh(i, duration, left=start_time, height=0.6, 
               color=colors[i], alpha=0.8, 
               label=f'Worker {worker_id}')
        
        # 添加工人ID标签
        ax.text(start_time + duration/2, i, f'ID {worker_id}', 
               ha='center', va='center', fontweight='bold', fontsize=10)
    
    # 设置图表属性
    ax.set_xlabel('Time (minutes)', fontsize=12)
    ax.set_ylabel('Workers', fontsize=12)
    ax.set_title('Worker Activity Timeline', fontsize=16, fontweight='bold')
    
    # 设置Y轴标签
    ax.set_yticks(range(len(worker_data)))
    ax.set_yticklabels([f'Worker {data["worker_id"]}' for data in worker_data])
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='x')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"工人时间线图表已保存到: {output_path}")

def main():
    # 设置路径
    analysis_dir = "runs/track/yolov5/best_22_osnet_x0_2518/analysis"
    video_file = "image/测试基准视频.mp4"
    
    # 检查文件是否存在
    stats_file = os.path.join(analysis_dir, "worker_stats.json")
    if not os.path.exists(stats_file):
        print(f"统计文件不存在: {stats_file}")
        return
    
    print("=" * 60)
    print("修复工人分析数据和图表")
    print("=" * 60)
    
    # 加载数据
    worker_stats = load_worker_stats(stats_file)
    video_info = get_video_info(video_file)
    
    # 修复统计数据
    fixed_stats = fix_worker_stats(worker_stats, video_info)
    
    # 保存修复后的统计数据
    fixed_stats_file = os.path.join(analysis_dir, "worker_stats_fixed.json")
    with open(fixed_stats_file, 'w', encoding='utf-8') as f:
        json.dump(fixed_stats, f, indent=2, ensure_ascii=False)
    print(f"修复后的统计数据已保存到: {fixed_stats_file}")
    
    # 生成修复后的工人数量图表
    fixed_chart_path = os.path.join(analysis_dir, "worker_count_over_time_fixed.png")
    worker_counts, time_labels = generate_fixed_worker_count_chart(fixed_stats, video_info, fixed_chart_path)
    
    # 生成工人时间线图表
    timeline_chart_path = os.path.join(analysis_dir, "worker_timeline.png")
    generate_worker_timeline_chart(fixed_stats, video_info, timeline_chart_path)
    
    print("=" * 60)
    print("修复完成！")
    print("=" * 60)
    print(f"修复后的文件:")
    print(f"  统计数据: {fixed_stats_file}")
    print(f"  工人数量图表: {fixed_chart_path}")
    print(f"  工人时间线图表: {timeline_chart_path}")

if __name__ == '__main__':
    main()
