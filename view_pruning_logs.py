#!/usr/bin/env python3
"""
查看模型剪枝记录工具
提供多种方式查看和分析剪枝实验记录
"""

import argparse
import json
import os
import webbrowser
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

from pruning_logger import list_all_experiments, load_experiment_log

def list_experiments(log_dir="pruning_logs"):
    """列出所有剪枝实验"""
    print("🔍 所有剪枝实验记录:")
    print("=" * 80)
    
    experiments = list_all_experiments(log_dir)
    
    if not experiments:
        print("没有找到剪枝实验记录")
        return
    
    # 创建表格显示
    data = []
    for exp in experiments:
        improvements = exp.get("improvements", {})
        
        data.append({
            "实验ID": exp["experiment_id"],
            "开始时间": exp["start_time"][:19].replace("T", " "),
            "剪枝方法": exp.get("pruning_method", "N/A"),
            "FPS提升(%)": f"{improvements.get('fps_improvement_percent', 0):.1f}",
            "时间减少(%)": f"{improvements.get('time_reduction_percent', 0):.1f}",
            "大小减少(%)": f"{improvements.get('size_reduction_percent', 0):.1f}"
        })
    
    df = pd.DataFrame(data)
    print(df.to_string(index=False))
    
    print(f"\n总共 {len(experiments)} 个实验记录")
    print(f"日志目录: {Path(log_dir).absolute()}")

def show_experiment_detail(experiment_id, log_dir="pruning_logs"):
    """显示特定实验的详细信息"""
    try:
        exp_log = load_experiment_log(experiment_id, log_dir)
    except FileNotFoundError:
        print(f"❌ 实验记录不存在: {experiment_id}")
        return
    
    print(f"📊 实验详细信息: {experiment_id}")
    print("=" * 80)
    
    # 基本信息
    print("🔧 基本信息:")
    print(f"  实验ID: {exp_log['experiment_id']}")
    print(f"  开始时间: {exp_log['start_time']}")
    print(f"  结束时间: {exp_log.get('end_time', 'N/A')}")
    print(f"  剪枝方法: {exp_log.get('pruning_method', 'N/A')}")
    
    # 剪枝参数
    print("\n⚙️ 剪枝参数:")
    params = exp_log.get("pruning_params", {})
    for key, value in params.items():
        print(f"  {key}: {value}")
    
    # 性能对比
    print("\n📈 性能对比:")
    before = exp_log.get("performance_before", {})
    after = exp_log.get("performance_after", {})
    
    if before and after:
        print(f"  推理速度: {before.get('fps', 'N/A'):.2f} → {after.get('fps', 'N/A'):.2f} FPS")
        print(f"  推理时间: {before.get('inference_time_ms', 'N/A'):.2f} → {after.get('inference_time_ms', 'N/A'):.2f} ms")
    
    # 性能提升
    improvements = exp_log.get("improvements", {})
    if improvements:
        print("\n🚀 性能提升:")
        if "fps_improvement_percent" in improvements:
            print(f"  FPS提升: {improvements['fps_improvement_percent']:+.1f}%")
        if "time_reduction_percent" in improvements:
            print(f"  时间减少: {improvements['time_reduction_percent']:+.1f}%")
        if "size_reduction_percent" in improvements:
            print(f"  大小减少: {improvements['size_reduction_percent']:+.1f}%")
    
    # 剪枝结果
    results = exp_log.get("pruning_results", {})
    if results:
        print("\n🎯 剪枝结果:")
        print(f"  稀疏度: {results.get('sparsity_percent', 'N/A'):.1f}%")
        print(f"  阈值: {results.get('threshold', 'N/A'):.4f}")
        print(f"  可剪枝层数: {results.get('prunable_layers', 'N/A')}")
        print(f"  忽略层数: {results.get('ignored_layers', 'N/A')}")
    
    # 模型信息
    original_model = exp_log.get("original_model", {})
    pruned_model = exp_log.get("pruned_model", {})
    
    if original_model or pruned_model:
        print("\n📁 模型信息:")
        if original_model:
            print(f"  原始模型: {original_model.get('path', 'N/A')}")
            print(f"  原始大小: {original_model.get('file_size_mb', 'N/A'):.2f} MB")
        if pruned_model:
            print(f"  剪枝模型: {pruned_model.get('path', 'N/A')}")
            print(f"  剪枝大小: {pruned_model.get('file_size_mb', 'N/A'):.2f} MB")
    
    # 备注
    notes = exp_log.get("notes", "")
    if notes:
        print(f"\n📝 备注:")
        print(f"  {notes}")

def open_experiment_report(experiment_id, log_dir="pruning_logs"):
    """打开实验的HTML报告"""
    report_path = Path(log_dir) / "experiments" / experiment_id / "report.html"
    
    if not report_path.exists():
        print(f"❌ HTML报告不存在: {report_path}")
        return
    
    print(f"🌐 打开HTML报告: {report_path}")
    webbrowser.open(f"file://{report_path.absolute()}")

def compare_experiments(experiment_ids, log_dir="pruning_logs"):
    """比较多个实验"""
    print(f"📊 实验对比: {', '.join(experiment_ids)}")
    print("=" * 80)
    
    experiments_data = []
    
    for exp_id in experiment_ids:
        try:
            exp_log = load_experiment_log(exp_id, log_dir)
            experiments_data.append(exp_log)
        except FileNotFoundError:
            print(f"❌ 实验记录不存在: {exp_id}")
            continue
    
    if len(experiments_data) < 2:
        print("需要至少2个有效的实验记录进行比较")
        return
    
    # 创建对比表格
    comparison_data = []
    
    for exp_log in experiments_data:
        improvements = exp_log.get("improvements", {})
        params = exp_log.get("pruning_params", {})
        results = exp_log.get("pruning_results", {})
        
        comparison_data.append({
            "实验ID": exp_log["experiment_id"][:15] + "...",
            "剪枝比例": f"{params.get('percent', 0):.1%}",
            "策略": params.get('strategy', 'N/A'),
            "稀疏度": f"{results.get('sparsity_percent', 0):.1f}%",
            "FPS提升": f"{improvements.get('fps_improvement_percent', 0):+.1f}%",
            "时间减少": f"{improvements.get('time_reduction_percent', 0):+.1f}%",
            "大小减少": f"{improvements.get('size_reduction_percent', 0):+.1f}%"
        })
    
    df = pd.DataFrame(comparison_data)
    print(df.to_string(index=False))

def generate_summary_report(log_dir="pruning_logs"):
    """生成剪枝实验总结报告"""
    experiments = list_all_experiments(log_dir)
    
    if not experiments:
        print("没有找到剪枝实验记录")
        return
    
    print("📈 剪枝实验总结报告")
    print("=" * 80)
    
    # 统计信息
    total_experiments = len(experiments)
    successful_experiments = len([exp for exp in experiments if exp.get("improvements")])
    
    print(f"总实验数: {total_experiments}")
    print(f"成功实验数: {successful_experiments}")
    
    # 性能提升统计
    fps_improvements = []
    time_reductions = []
    size_reductions = []
    
    for exp in experiments:
        improvements = exp.get("improvements", {})
        if "fps_improvement_percent" in improvements:
            fps_improvements.append(improvements["fps_improvement_percent"])
        if "time_reduction_percent" in improvements:
            time_reductions.append(improvements["time_reduction_percent"])
        if "size_reduction_percent" in improvements:
            size_reductions.append(improvements["size_reduction_percent"])
    
    if fps_improvements:
        print(f"\n🚀 FPS提升统计:")
        print(f"  平均提升: {sum(fps_improvements)/len(fps_improvements):.1f}%")
        print(f"  最大提升: {max(fps_improvements):.1f}%")
        print(f"  最小提升: {min(fps_improvements):.1f}%")
    
    if time_reductions:
        print(f"\n⏱️ 时间减少统计:")
        print(f"  平均减少: {sum(time_reductions)/len(time_reductions):.1f}%")
        print(f"  最大减少: {max(time_reductions):.1f}%")
        print(f"  最小减少: {min(time_reductions):.1f}%")
    
    if size_reductions:
        print(f"\n💾 大小减少统计:")
        print(f"  平均减少: {sum(size_reductions)/len(size_reductions):.1f}%")
        print(f"  最大减少: {max(size_reductions):.1f}%")
        print(f"  最小减少: {min(size_reductions):.1f}%")
    
    # 最佳实验
    if fps_improvements:
        best_fps_idx = fps_improvements.index(max(fps_improvements))
        best_exp = [exp for exp in experiments if exp.get("improvements", {}).get("fps_improvement_percent") == max(fps_improvements)][0]
        print(f"\n🏆 最佳FPS提升实验:")
        print(f"  实验ID: {best_exp['experiment_id']}")
        print(f"  FPS提升: {max(fps_improvements):.1f}%")

def clean_old_logs(log_dir="pruning_logs", days=30):
    """清理旧的日志记录"""
    print(f"🧹 清理 {days} 天前的日志记录...")
    
    experiments_dir = Path(log_dir) / "experiments"
    if not experiments_dir.exists():
        print("没有找到实验目录")
        return
    
    current_time = datetime.now()
    cleaned_count = 0
    
    for exp_dir in experiments_dir.iterdir():
        if exp_dir.is_dir():
            # 从目录名提取时间戳
            try:
                timestamp_str = exp_dir.name.split("_")[-2] + "_" + exp_dir.name.split("_")[-1]
                exp_time = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                
                if (current_time - exp_time).days > days:
                    import shutil
                    shutil.rmtree(exp_dir)
                    cleaned_count += 1
                    print(f"  删除: {exp_dir.name}")
            except:
                continue
    
    print(f"清理完成，删除了 {cleaned_count} 个旧实验记录")

def main():
    parser = argparse.ArgumentParser(description='查看模型剪枝记录工具')
    parser.add_argument('--log-dir', type=str, default='pruning_logs', help='日志目录')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 列出所有实验
    list_parser = subparsers.add_parser('list', help='列出所有实验')
    
    # 显示实验详情
    detail_parser = subparsers.add_parser('detail', help='显示实验详情')
    detail_parser.add_argument('experiment_id', help='实验ID')
    
    # 打开HTML报告
    report_parser = subparsers.add_parser('report', help='打开HTML报告')
    report_parser.add_argument('experiment_id', help='实验ID')
    
    # 比较实验
    compare_parser = subparsers.add_parser('compare', help='比较多个实验')
    compare_parser.add_argument('experiment_ids', nargs='+', help='实验ID列表')
    
    # 总结报告
    summary_parser = subparsers.add_parser('summary', help='生成总结报告')
    
    # 清理日志
    clean_parser = subparsers.add_parser('clean', help='清理旧日志')
    clean_parser.add_argument('--days', type=int, default=30, help='保留天数')
    
    args = parser.parse_args()
    
    if args.command == 'list':
        list_experiments(args.log_dir)
    elif args.command == 'detail':
        show_experiment_detail(args.experiment_id, args.log_dir)
    elif args.command == 'report':
        open_experiment_report(args.experiment_id, args.log_dir)
    elif args.command == 'compare':
        compare_experiments(args.experiment_ids, args.log_dir)
    elif args.command == 'summary':
        generate_summary_report(args.log_dir)
    elif args.command == 'clean':
        clean_old_logs(args.log_dir, args.days)
    else:
        # 默认显示所有实验列表
        list_experiments(args.log_dir)

if __name__ == '__main__':
    main()
