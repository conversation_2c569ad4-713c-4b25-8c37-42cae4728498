{
  "experiment_id": "pruning_exp_20250604_212450",
  "start_time": "2025-06-04T21:24:50.441614",
  "original_model": {
    "path": "yolov5/best_22.pt",
    "file_size_mb": 13.783973693847656,
    "parameters": {
      "total": 7022326,
      "trainable": 0,
      "non_trainable": 7022326
    },
    "layers": 25
  },
  "pruning_method": "sparse_pruning",
  "pruning_params": {
    "percent": 0.3,
    "method": "BN_weight_based"
  },
  "pruned_model": {
    "path": "sparse_pruned_yolo_30percent.pt",
    "file_size_mb": 27.220909118652344,
    "saved_path": "pruning_logs\\experiments\\pruning_exp_20250604_212450\\sparse_pruned_yolo_30percent.pt"
  },
  "performance_before": {
    "fps": 15.221716134626284,
    "inference_time_ms": 65.6956148147583,
    "total_time_s": 6.56956148147583,
    "num_runs": 100
  },
  "performance_after": {
    "fps": 16.418752598762364,
    "inference_time_ms": 60.90596675872803,
    "total_time_s": 6.090596675872803,
    "num_runs": 100
  },
  "pruning_results": {
    "target_prune_ratio": 0.3,
    "actual_prune_ratio": 0.3002946127946128,
    "pruned_channels": 2854,
    "total_channels": 9504,
    "threshold": 