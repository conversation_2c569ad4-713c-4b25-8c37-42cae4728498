# 🛠️ 建筑工地工人行为分析系统 - 技术使用手册

## 📋 目录
- [快速开始](#快速开始)
- [核心脚本详解](#核心脚本详解)
- [模型剪枝工具](#模型剪枝工具)
- [参数配置指南](#参数配置指南)
- [故障排除](#故障排除)
- [最佳实践](#最佳实践)

## 🚀 快速开始

### 环境准备
```bash
# 1. 创建conda环境
conda create -n yolo python=3.8
conda activate yolo

# 2. 安装依赖
pip install torch torchvision torchaudio
pip install opencv-python matplotlib seaborn
pip install torch-pruning

# 3. 验证安装
python -c "import torch; print(torch.__version__)"
```

### 基础使用流程
```bash
# 1. 运行检测和跟踪
python track.py --source 测试基准视频.mp4 --weights yolov5/best_22.pt

# 2. 生成行为分析报告
python worker_analysis.py --tracks-file runs/track/exp/tracks.txt --video-file 测试基准视频.mp4

# 3. 查看结果
# 打开 runs/track/exp/analysis/worker_report.html
```

## 📝 核心脚本详解

### 1. track.py - 主要跟踪脚本

#### 功能说明
- 使用YOLOv5进行工人检测
- 使用DeepSORT进行多目标跟踪
- 生成跟踪结果文件

#### 关键参数
```bash
python track.py \
    --source 测试基准视频.mp4 \          # 输入视频文件
    --weights yolov5/best_22.pt \        # 模型权重文件
    --conf-thres 0.6 \                   # 置信度阈值 (推荐0.6)
    --iou-thres 0.45 \                   # IoU阈值 (推荐0.45)
    --max-det 1000 \                     # 最大检测数量
    --device cpu \                       # 设备选择 (cpu/cuda)
    --save-txt \                         # 保存跟踪结果
    --save-vid                           # 保存输出视频
```

#### 输出文件
- `runs/track/exp/tracks.txt` - 跟踪数据
- `runs/track/exp/测试基准视频.mp4` - 标注视频

### 2. worker_analysis.py - 工人行为分析

#### 功能说明
- 分析工人活动时间和移动距离
- 生成统计图表和热力图
- 创建详细的HTML报告

#### 使用方法
```bash
python worker_analysis.py \
    --tracks-file runs/track/exp/tracks.txt \    # 跟踪数据文件
    --video-file 测试基准视频.mp4 \              # 原始视频文件
    --output-dir runs/track/exp/analysis \       # 输出目录
    --min-track-length 30                        # 最小跟踪长度
```

#### 输出文件
- `worker_stats.json` - 详细统计数据
- `worker_report.html` - HTML分析报告
- `worker_count_over_time.png` - 工人数量变化图
- `worker_heatmap.png` - 活动热力图

### 3. regenerate_report.py - 报告重新生成

#### 功能说明
- 基于现有跟踪数据重新生成报告
- 无需重新运行检测和跟踪

#### 使用方法
```bash
python regenerate_report.py \
    --tracks-file runs/track/exp/tracks.txt \
    --video-file 测试基准视频.mp4
```

## 🔧 模型剪枝工具

### 1. optimized_sparse_prune.py - 优化稀疏剪枝 (推荐)

#### 功能特点
- 自适应阈值计算
- 层融合优化
- 性能基准测试

#### 使用方法
```bash
python optimized_sparse_prune.py \
    --weights yolov5/best_22.pt \               # 原始模型
    --percent 0.15 \                            # 剪枝比例 (推荐0.15)
    --save-path optimized_sparse_yolo.pt \      # 输出模型
    --strategy balanced \                       # 剪枝策略
    --benchmark \                               # 性能测试
    --optimize                                  # 启用优化
```

#### 剪枝策略
- `conservative` - 保守剪枝，安全性高
- `balanced` - 平衡剪枝，推荐使用
- `aggressive` - 激进剪枝，可能影响精度

### 2. structural_yolo_prune.py - 结构化剪枝

#### 功能说明
- 真正减少模型参数数量
- 物理移除通道和层

#### 使用方法
```bash
python structural_yolo_prune.py \
    --weights yolov5/best_22.pt \
    --percent 0.1 \                             # 较低的剪枝比例
    --save-path structural_pruned_yolo.pt
```

### 3. test_pruned_model.py - 模型性能测试

#### 功能说明
- 比较原始模型和剪枝后模型性能
- 基准测试推理速度
- 单张图像测试

#### 使用方法
```bash
# 性能比较
python test_pruned_model.py \
    --pruned-weights optimized_sparse_yolo.pt \
    --original-weights yolov5/best_22.pt \
    --compare

# 基准测试
python test_pruned_model.py \
    --pruned-weights optimized_sparse_yolo.pt \
    --benchmark

# 单张图像测试
python test_pruned_model.py \
    --pruned-weights optimized_sparse_yolo.pt \
    --source test_image.jpg \
    --save-path result.jpg
```

## ⚙️ 参数配置指南

### 检测参数优化

#### conf-thres (置信度阈值)
- **默认值**: 0.25
- **推荐值**: 0.6
- **作用**: 解决工人离开画面重返时的ID跳动问题
- **调优建议**: 
  - 提高值 → 减少误检，但可能增加漏检
  - 降低值 → 增加检测数量，但可能增加误检

#### iou-thres (IoU阈值)
- **默认值**: 0.45
- **推荐值**: 0.45
- **作用**: 解决工人重叠时的识别框合并问题
- **调优建议**:
  - 提高值 → 允许更多重叠框，可能导致重复检测
  - 降低值 → 更严格的NMS，减少重复检测

### 剪枝参数选择

#### 剪枝比例建议
| 应用场景 | 推荐比例 | 预期效果 |
|----------|----------|----------|
| 实时应用 | 15% | 最佳性能提升 |
| 精度优先 | 10% | 保持高精度 |
| 速度优先 | 20% | 适中性能提升 |

#### 剪枝策略选择
- **conservative**: 精度要求高的场景
- **balanced**: 一般应用场景 (推荐)
- **aggressive**: 速度要求极高的场景

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 内存不足错误
```bash
# 错误信息: CUDA out of memory
# 解决方案:
python track.py --device cpu  # 使用CPU
# 或
python track.py --imgsz 416   # 减小输入图像尺寸
```

#### 2. 模型加载失败
```bash
# 错误信息: FileNotFoundError
# 解决方案: 检查模型路径
ls yolov5/best_22.pt  # 确认文件存在
```

#### 3. 跟踪结果为空
```bash
# 可能原因: 置信度阈值过高
# 解决方案: 降低置信度阈值
python track.py --conf-thres 0.3
```

#### 4. HTML报告显示异常
```bash
# 可能原因: 浏览器兼容性
# 解决方案: 使用Chrome或Firefox浏览器
```

#### 5. 剪枝后精度下降
```bash
# 解决方案: 降低剪枝比例
python optimized_sparse_prune.py --percent 0.1
```

### 性能调优建议

#### 提升检测精度
1. 调整置信度阈值: `--conf-thres 0.5`
2. 增加输入图像尺寸: `--imgsz 640`
3. 使用更大的模型: `yolov5m.pt` 或 `yolov5l.pt`

#### 提升推理速度
1. 使用剪枝后的模型
2. 减小输入图像尺寸: `--imgsz 416`
3. 启用GPU加速: `--device 0`

#### 提升跟踪稳定性
1. 优化检测参数: `--conf-thres 0.6 --iou-thres 0.45`
2. 调整跟踪器参数 (在deep_sort配置中)
3. 使用更好的特征提取模型

## 🎯 最佳实践

### 1. 生产环境部署
```bash
# 使用优化后的模型
python track.py \
    --weights optimized_sparse_yolo.pt \
    --conf-thres 0.6 \
    --iou-thres 0.45 \
    --device 0 \
    --save-txt
```

### 2. 批量处理
```bash
# 处理多个视频文件
for video in *.mp4; do
    python track.py --source "$video" --weights optimized_sparse_yolo.pt
    python worker_analysis.py --tracks-file runs/track/exp/tracks.txt --video-file "$video"
done
```

### 3. 实时监控
```bash
# 摄像头实时监控
python track.py --source 0 --weights optimized_sparse_yolo.pt --view-img
```

### 4. 模型选择建议
- **精度优先**: 使用原始模型 `best_22.pt`
- **速度优先**: 使用15%剪枝模型 `optimized_sparse_yolo.pt`
- **平衡选择**: 使用10%剪枝模型

### 5. 监控指标
定期检查以下指标：
- 检测精度 (mAP)
- 跟踪稳定性 (ID切换率)
- 推理速度 (FPS)
- 资源占用 (CPU/内存)

---

**📞 技术支持**: 如遇问题请查看项目Issues或联系开发团队  
**📚 更多文档**: 参考 `PROJECT_README.md` 和 `项目总结报告.md`
