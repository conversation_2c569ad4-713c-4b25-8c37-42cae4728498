#!/usr/bin/env python3
"""
验证工人分析功能整合是否成功
"""

import os
import inspect
from worker_analysis import WorkerAnalyzer

def verify_integration():
    """验证整合是否成功"""
    print("=" * 60)
    print("验证工人分析功能整合")
    print("=" * 60)
    
    # 检查WorkerAnalyzer类是否包含新的方法
    methods_to_check = [
        '_generate_worker_count_charts',
        '_generate_fixed_single_chart', 
        '_generate_worker_activity_heatmap',
        '_generate_worker_statistics_chart',
        '_generate_worker_timeline_chart',
        '_fix_worker_stats_timing'
    ]
    
    print("检查新增的方法:")
    for method_name in methods_to_check:
        if hasattr(WorkerAnalyzer, method_name):
            method = getattr(WorkerAnalyzer, method_name)
            if callable(method):
                print(f"  ✅ {method_name} - 存在且可调用")
            else:
                print(f"  ❌ {method_name} - 存在但不可调用")
        else:
            print(f"  ❌ {method_name} - 不存在")
    
    # 检查_analyze_activity_patterns方法是否已更新
    print("\n检查_analyze_activity_patterns方法:")
    if hasattr(WorkerAnalyzer, '_analyze_activity_patterns'):
        method = getattr(WorkerAnalyzer, '_analyze_activity_patterns')
        source = inspect.getsource(method)
        
        # 检查是否包含新的方法调用
        new_calls = [
            '_generate_worker_count_charts',
            '_generate_worker_activity_heatmap', 
            '_generate_worker_statistics_chart',
            '_generate_worker_timeline_chart'
        ]
        
        for call in new_calls:
            if call in source:
                print(f"  ✅ 包含调用: {call}")
            else:
                print(f"  ❌ 缺少调用: {call}")
    else:
        print("  ❌ _analyze_activity_patterns方法不存在")
    
    # 检查现有的图表文件
    print("\n检查现有的图表文件:")
    analysis_dir = "runs/track/yolov5/best_22_osnet_x0_2518/analysis"
    
    expected_files = [
        "worker_count_over_time.png",
        "worker_count_multi_scale.png", 
        "worker_activity_heatmap.png",
        "worker_statistics_summary.png",
        "worker_timeline.png"
    ]
    
    if os.path.exists(analysis_dir):
        for file_name in expected_files:
            file_path = os.path.join(analysis_dir, file_name)
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"  ✅ {file_name} - {size} bytes")
            else:
                print(f"  ❌ {file_name} - 不存在")
    else:
        print(f"  ❌ 分析目录不存在: {analysis_dir}")
    
    print("\n" + "=" * 60)
    print("整合验证完成！")
    print("=" * 60)

def check_method_signatures():
    """检查方法签名"""
    print("\n检查方法签名:")
    
    # 检查_analyze_activity_patterns方法
    if hasattr(WorkerAnalyzer, '_analyze_activity_patterns'):
        method = getattr(WorkerAnalyzer, '_analyze_activity_patterns')
        sig = inspect.signature(method)
        print(f"  _analyze_activity_patterns{sig}")
    
    # 检查新方法的签名
    new_methods = [
        '_generate_worker_count_charts',
        '_generate_worker_activity_heatmap',
        '_generate_worker_statistics_chart', 
        '_generate_worker_timeline_chart'
    ]
    
    for method_name in new_methods:
        if hasattr(WorkerAnalyzer, method_name):
            method = getattr(WorkerAnalyzer, method_name)
            sig = inspect.signature(method)
            print(f"  {method_name}{sig}")

def main():
    print("🔍 开始验证工人分析功能整合")
    
    try:
        verify_integration()
        check_method_signatures()
        
        print("\n🎉 验证完成！")
        
    except Exception as e:
        print(f"\n❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
