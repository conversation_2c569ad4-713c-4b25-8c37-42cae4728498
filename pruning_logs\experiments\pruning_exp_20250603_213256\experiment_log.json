{"experiment_id": "pruning_exp_20250603_213256", "start_time": "2025-06-03T21:32:56.151017", "original_model": {"path": "yolov5/best_22.pt", "file_size_mb": 13.783973693847656, "parameters": {"total": 7022326, "trainable": 0, "non_trainable": 7022326}, "layers": 25}, "pruning_method": "sparse_pruning", "pruning_params": {"percent": 0.15, "strategy": "balanced", "optimize": true, "device": "cpu"}, "pruned_model": {"path": "test_pruned_with_log.pt", "file_size_mb": 27.037166595458984, "saved_path": "pruning_logs\\experiments\\pruning_exp_20250603_213256\\test_pruned_with_log.pt"}, "performance_before": {"fps": 14.024874257590715, "inference_time_ms": 71.30188703536987}, "performance_after": {"fps": 10.58523955204817, "inference_time_ms": 94.47117328643799}, "pruning_results": {"sparsity_percent": 14.972643097643099, "threshold": 0.703125, "adjusted_percent": 0.15, "prunable_layers": 57, "ignored_layers": 0}, "notes": "测试剪枝记录系统", "end_time": "2025-06-03T21:33:14.313585", "improvements": {"fps_improvement_percent": -24.52524452175322, "time_reduction_percent": -32.49463263093557, "size_reduction_percent": -96.14929044392157}}