import cv2
import numpy as np
import time
from collections import defaultdict, deque
import math

class WorkerMonitor:
    """
    工人监控类，用于分析工人行为和安全状况
    """
    def __init__(self, frame_width, frame_height, danger_zones=None, restricted_areas=None,
                 stationary_threshold=150, stationary_time=20, crowd_threshold=3,
                 crowd_distance=100):
        """
        初始化工人监控器

        参数:
            frame_width (int): 视频帧宽度
            frame_height (int): 视频帧高度
            danger_zones (list): 危险区域列表，每个区域为[(x1,y1), (x2,y2), ...]
            restricted_areas (list): 禁止区域列表，每个区域为[(x1,y1), (x2,y2), ...]
            stationary_threshold (int): 判定为静止的位移阈值
            stationary_time (int): 静止多长时间判定为异常（秒）
            crowd_threshold (int): 判定为人群聚集的人数阈值
            crowd_distance (int): 判定为人群聚集的距离阈值
        """
        self.frame_width = frame_width
        self.frame_height = frame_height

        # 危险区域和禁止区域
        self.danger_zones = danger_zones if danger_zones else []
        self.restricted_areas = restricted_areas if restricted_areas else []

        # 工人轨迹和状态记录
        self.worker_tracks = defaultdict(lambda: deque(maxlen=30))  # 存储最近30帧的位置
        self.worker_stationary = defaultdict(float)  # 记录工人静止开始时间（视频时间）
        self.worker_entry_time = {}  # 记录工人首次出现时间
        self.worker_total_time = defaultdict(float)  # 记录工人总工作时间
        self.worker_in_danger = defaultdict(bool)  # 记录工人是否在危险区域
        self.worker_in_restricted = defaultdict(bool)  # 记录工人是否在禁止区域

        # 阈值设置
        self.stationary_threshold = stationary_threshold
        self.stationary_time = stationary_time
        self.crowd_threshold = crowd_threshold
        self.crowd_distance = crowd_distance

        # 热力图数据
        self.heatmap_data = np.zeros((frame_height, frame_width), dtype=np.uint8)
        self.heatmap_alpha = 0.3

        # 统计数据
        self.total_workers_seen = set()  # 记录所有出现过的工人ID
        self.current_worker_count = 0  # 当前画面中的工人数量
        self.alerts = []  # 存储警报信息

    def update(self, frame, detections, video_time):
        """
        更新工人监控状态

        参数:
            frame (np.array): 当前视频帧
            detections (list): 检测结果列表，每个元素为 [x1, y1, x2, y2, track_id, class_id]
            video_time (float): 当前视频时间（秒）

        返回:
            frame (np.array): 添加了监控信息的视频帧
            alerts (list): 警报信息列表
        """
        self.alerts = []  # 清空上一帧的警报
        self.current_worker_count = len(detections)

        # 记录当前帧已经报警的工人，避免重复报警
        alerted_workers = set()

        # 更新工人位置和状态
        current_workers = set()
        for det in detections:
            x1, y1, x2, y2, track_id, class_id = det
            track_id = int(track_id)
            current_workers.add(track_id)
            self.total_workers_seen.add(track_id)

            # 计算工人中心点
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2

            # 更新工人轨迹
            self.worker_tracks[track_id].append((center_x, center_y, video_time))

            # 记录工人首次出现时间
            if track_id not in self.worker_entry_time:
                self.worker_entry_time[track_id] = video_time

            # 更新热力图
            # 确保坐标在热力图范围内
            if 0 <= int(center_x) < self.frame_width and 0 <= int(center_y) < self.frame_height:
                try:
                    cv2.circle(self.heatmap_data, (int(center_x), int(center_y)), 5, 255, -1)
                except Exception as e:
                    print(f"热力图更新错误: {e}, 坐标: ({int(center_x)}, {int(center_y)}), 热力图尺寸: {self.heatmap_data.shape}")

            # 检查工人是否在危险区域
            for zone in self.danger_zones:
                if self._point_in_polygon((center_x, center_y), zone):
                    self.worker_in_danger[track_id] = True
                    # 只有当该工人在当前帧还没有报警时才添加警报
                    if track_id not in alerted_workers:
                        self.alerts.append(f"工人 {track_id} 处于危险区域")
                        alerted_workers.add(track_id)
                    break

            # 检查工人是否在禁止区域
            for area in self.restricted_areas:
                if self._point_in_polygon((center_x, center_y), area):
                    self.worker_in_restricted[track_id] = True
                    # 只有当该工人在当前帧还没有报警时才添加警报
                    if track_id not in alerted_workers:
                        self.alerts.append(f"工人 {track_id} 进入禁止区域")
                        alerted_workers.add(track_id)
                    break

            # 检查工人是否静止不动
            self._check_stationary(track_id, video_time, alerted_workers)

        # 检查人群聚集情况
        self._check_crowding(detections)

        # 更新工人工作时间
        for worker_id in current_workers:
            self.worker_total_time[worker_id] = video_time - self.worker_entry_time[worker_id]

        # 绘制监控信息
        frame = self._draw_monitoring_info(frame, detections, video_time)

        return frame, self.alerts

    def _check_stationary(self, worker_id, video_time, alerted_workers):
        """
        检查工人是否静止不动
        使用视频时间计算静止时间
        """
        if len(self.worker_tracks[worker_id]) < 2:
            return

        # 计算最近两个位置的距离
        pos1 = self.worker_tracks[worker_id][-1]  # 最新位置 (x, y, video_time)
        pos2 = self.worker_tracks[worker_id][-2]  # 前一个位置
        distance = math.sqrt((pos1[0] - pos2[0])**2 + (pos1[1] - pos2[1])**2)

        # 如果距离小于阈值，认为工人静止
        if distance < self.stationary_threshold:
            # 如果是第一次检测到静止，记录开始静止的时间
            if self.worker_stationary[worker_id] == 0:
                # 记录静止开始时间（视频时间）
                self.worker_stationary[worker_id] = video_time

            # 计算静止持续时间（基于视频时间）
            stationary_duration = video_time - self.worker_stationary[worker_id]

            # 如果静止时间超过阈值且该工人在当前帧还没有报警，发出警报
            if stationary_duration > self.stationary_time and worker_id not in alerted_workers:
                # 格式化时间为分:秒
                minutes = int(stationary_duration) // 60
                seconds = int(stationary_duration) % 60
                time_str = f"{minutes}分{seconds}秒" if minutes > 0 else f"{seconds}秒"

                self.alerts.append(f"工人 {worker_id} 静止不动 {time_str}")
                alerted_workers.add(worker_id)
        else:
            # 如果工人移动，重置静止时间
            self.worker_stationary[worker_id] = 0

    def _check_crowding(self, detections):
        """检查人群聚集情况"""
        if len(detections) < self.crowd_threshold:
            return

        # 计算工人之间的距离
        for i in range(len(detections)):
            crowd_count = 0
            x1_i, y1_i, x2_i, y2_i, id_i, _ = detections[i]
            center_i = ((x1_i + x2_i) / 2, (y1_i + y2_i) / 2)

            for j in range(len(detections)):
                if i == j:
                    continue

                x1_j, y1_j, x2_j, y2_j, id_j, _ = detections[j]
                center_j = ((x1_j + x2_j) / 2, (y1_j + y2_j) / 2)

                distance = math.sqrt((center_i[0] - center_j[0])**2 + (center_i[1] - center_j[1])**2)
                if distance < self.crowd_distance:
                    crowd_count += 1

            if crowd_count >= self.crowd_threshold:
                self.alerts.append(f"检测到人群聚集，中心工人ID: {int(id_i)}")
                break

    def _point_in_polygon(self, point, polygon):
        """判断点是否在多边形内部"""
        x, y = point
        n = len(polygon)
        inside = False

        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside

    def _draw_monitoring_info(self, frame, detections, video_time):
        """在视频帧上绘制监控信息"""
        # 绘制危险区域
        for zone in self.danger_zones:
            pts = np.array(zone, np.int32)
            pts = pts.reshape((-1, 1, 2))
            cv2.polylines(frame, [pts], True, (0, 0, 255), 2)

        # 绘制禁止区域
        for area in self.restricted_areas:
            pts = np.array(area, np.int32)
            pts = pts.reshape((-1, 1, 2))
            cv2.polylines(frame, [pts], True, (0, 255, 255), 2)

        # 绘制热力图
        # 确保热力图与原始帧尺寸一致
        if self.heatmap_data.shape[:2] != frame.shape[:2]:
            # 调整热力图尺寸以匹配帧尺寸
            resized_heatmap = cv2.resize(self.heatmap_data, (frame.shape[1], frame.shape[0]))
        else:
            resized_heatmap = self.heatmap_data

        # 应用颜色映射
        heatmap = cv2.applyColorMap(resized_heatmap, cv2.COLORMAP_JET)

        # 确保热力图和帧具有相同的通道数
        if len(frame.shape) == 3 and frame.shape[2] == 3:
            if len(heatmap.shape) != 3 or heatmap.shape[2] != 3:
                # 如果热力图不是3通道，转换为3通道
                heatmap = cv2.cvtColor(heatmap, cv2.COLOR_GRAY2BGR)

        # 叠加热力图
        try:
            frame = cv2.addWeighted(frame, 1 - self.heatmap_alpha, heatmap, self.heatmap_alpha, 0)
        except cv2.error as e:
            # 如果仍然出错，跳过热力图叠加
            print(f"热力图叠加错误: {e}")
            print(f"帧尺寸: {frame.shape}, 热力图尺寸: {heatmap.shape}")

        # 绘制统计信息
        cv2.putText(frame, f"当前工人数: {self.current_worker_count}", (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(frame, f"总工人数: {len(self.total_workers_seen)}", (10, 60),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        # 绘制警报信息
        for i, alert in enumerate(self.alerts):
            cv2.putText(frame, alert, (10, 90 + i * 30),
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)

        # 在每个工人上方显示静止时间
        for det in detections:
            x1, y1, x2, y2, track_id, _ = det
            track_id = int(track_id)

            # 如果工人有静止时间记录且正在静止
            if self.worker_stationary[track_id] > 0:
                # 计算静止持续时间（基于视频时间）
                stationary_duration = video_time - self.worker_stationary[track_id]

                # 只有当静止时间大于0时才显示
                if stationary_duration > 0:
                    # 格式化时间为分:秒
                    minutes = int(stationary_duration) // 60
                    seconds = int(stationary_duration) % 60

                    # 根据静止时间长短选择颜色
                    if stationary_duration > self.stationary_time:
                        color = (0, 0, 255)  # 红色，超过阈值
                    else:
                        color = (0, 255, 255)  # 黄色，未超过阈值

                    # 显示静止时间
                    time_text = f"{minutes:02d}:{seconds:02d}"
                    cv2.putText(frame, f"静止: {time_text}", (int(x1), int(y1) - 10),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

        return frame

    def get_worker_statistics(self):
        """获取工人统计信息"""
        stats = {
            "total_workers": len(self.total_workers_seen),
            "current_workers": self.current_worker_count,
            "worker_times": {worker_id: round(time, 2) for worker_id, time in self.worker_total_time.items()},
            "danger_zone_workers": [worker_id for worker_id, in_danger in self.worker_in_danger.items() if in_danger],
            "restricted_area_workers": [worker_id for worker_id, in_restricted in self.worker_in_restricted.items() if in_restricted]
        }
        return stats

    def reset_heatmap(self):
        """重置热力图数据"""
        self.heatmap_data = np.zeros((self.frame_height, self.frame_width), dtype=np.uint8)
