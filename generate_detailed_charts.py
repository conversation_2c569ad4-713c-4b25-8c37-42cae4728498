#!/usr/bin/env python3
"""
生成更详细的工人分析图表
包含多种时间粒度的工人数量变化图表
"""

import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import os
import cv2
from pathlib import Path

# 设置matplotlib中文字体支持
matplotlib.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif']
matplotlib.rcParams['axes.unicode_minus'] = False

def load_worker_stats(stats_file):
    """加载工人统计数据"""
    with open(stats_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def generate_detailed_worker_count_charts(worker_stats, video_info, output_dir):
    """生成详细的工人数量图表（多种时间粒度）"""
    print("生成详细的工人数量图表...")
    
    if not video_info:
        fps = 30.0
        all_first_frames = [stats['first_frame'] for stats in worker_stats.values()]
        all_last_frames = [stats['last_frame'] for stats in worker_stats.values()]
        min_frame = min(all_first_frames) if all_first_frames else 1
        max_frame = max(all_last_frames) if all_last_frames else 100
        total_frames = max_frame - min_frame + 1
    else:
        fps = video_info['fps']
        min_frame = 1
        max_frame = video_info['frame_count']
        total_frames = max_frame
    
    print(f"视频信息: FPS={fps:.2f}, 总帧数={total_frames}, 总时长={total_frames/fps:.2f}秒")
    
    # 创建多个时间粒度的图表
    time_intervals = [5, 10, 15, 30]  # 5秒, 10秒, 15秒, 30秒
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Worker Count Analysis - Multiple Time Scales', fontsize=16, fontweight='bold')
    
    for idx, interval_seconds in enumerate(time_intervals):
        row = idx // 2
        col = idx % 2
        ax = axes[row, col]
        
        # 计算该时间间隔的工人数量
        interval_frames = int(fps * interval_seconds)
        num_intervals = max(1, int(np.ceil(total_frames / interval_frames)))
        
        worker_counts = []
        time_labels = []
        
        for interval in range(num_intervals):
            start_frame = min_frame + interval * interval_frames
            end_frame = min(min_frame + (interval + 1) * interval_frames, max_frame + 1)
            
            # 统计该时间段内活跃的工人数量
            active_workers = 0
            for worker_id, stats in worker_stats.items():
                worker_first = stats['first_frame']
                worker_last = stats['last_frame']
                
                # 检查工人是否在该时间段内活跃
                if not (worker_last < start_frame or worker_first >= end_frame):
                    active_workers += 1
            
            worker_counts.append(active_workers)
            time_labels.append(interval * interval_seconds)
        
        # 绘制图表
        if len(time_labels) > 1:
            ax.plot(time_labels, worker_counts, marker='o', linestyle='-', 
                   linewidth=2, markersize=6, color='#2E86AB', markerfacecolor='#A23B72')
        else:
            ax.scatter(time_labels, worker_counts, s=100, color='#2E86AB')
        
        ax.set_title(f'Worker Count ({interval_seconds}s intervals)', fontweight='bold')
        ax.set_xlabel('Time (seconds)')
        ax.set_ylabel('Worker Count')
        ax.grid(True, alpha=0.3)
        
        # 设置Y轴范围
        max_count = max(worker_counts) if worker_counts else 1
        ax.set_ylim(-0.5, max_count + 0.5)
        
        # 添加数值标签
        for x, y in zip(time_labels, worker_counts):
            ax.annotate(f'{int(y)}', (x, y), textcoords="offset points", 
                       xytext=(0,8), ha='center', fontsize=9)
        
        print(f"{interval_seconds}秒间隔: {num_intervals}个时间段, 最大工人数: {max(worker_counts)}")
    
    plt.tight_layout()
    
    # 保存多粒度图表
    multi_scale_path = os.path.join(output_dir, "worker_count_multi_scale.png")
    plt.savefig(multi_scale_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"多粒度工人数量图表已保存到: {multi_scale_path}")

def generate_worker_activity_heatmap(worker_stats, video_info, output_dir):
    """生成工人活动热力图（时间 vs 工人ID）"""
    print("生成工人活动热力图...")
    
    if not video_info:
        fps = 30.0
        all_first_frames = [stats['first_frame'] for stats in worker_stats.values()]
        all_last_frames = [stats['last_frame'] for stats in worker_stats.values()]
        min_frame = min(all_first_frames) if all_first_frames else 1
        max_frame = max(all_last_frames) if all_last_frames else 100
        total_frames = max_frame - min_frame + 1
    else:
        fps = video_info['fps']
        min_frame = 1
        max_frame = video_info['frame_count']
        total_frames = max_frame
    
    # 创建时间段（每5秒一个段）
    interval_seconds = 5
    interval_frames = int(fps * interval_seconds)
    num_intervals = max(1, int(np.ceil(total_frames / interval_frames)))
    
    # 获取所有工人ID并排序
    worker_ids = sorted([int(wid) for wid in worker_stats.keys()])
    
    # 创建活动矩阵
    activity_matrix = np.zeros((len(worker_ids), num_intervals))
    
    for i, worker_id in enumerate(worker_ids):
        worker_id_str = str(worker_id)
        if worker_id_str in worker_stats:
            stats = worker_stats[worker_id_str]
            worker_first = stats['first_frame']
            worker_last = stats['last_frame']
            
            for interval in range(num_intervals):
                start_frame = min_frame + interval * interval_frames
                end_frame = min(min_frame + (interval + 1) * interval_frames, max_frame + 1)
                
                # 检查工人是否在该时间段内活跃
                if not (worker_last < start_frame or worker_first >= end_frame):
                    # 计算活跃程度（重叠帧数 / 时间段总帧数）
                    overlap_start = max(start_frame, worker_first)
                    overlap_end = min(end_frame, worker_last + 1)
                    overlap_frames = max(0, overlap_end - overlap_start)
                    activity_intensity = overlap_frames / interval_frames
                    activity_matrix[i, interval] = activity_intensity
    
    # 创建热力图
    plt.figure(figsize=(14, 8))
    
    # 生成时间标签
    time_labels = [f"{i*interval_seconds}s" for i in range(num_intervals)]
    worker_labels = [f"Worker {wid}" for wid in worker_ids]
    
    # 绘制热力图
    im = plt.imshow(activity_matrix, cmap='YlOrRd', aspect='auto', interpolation='nearest')
    
    # 设置坐标轴
    plt.xticks(range(num_intervals), time_labels, rotation=45)
    plt.yticks(range(len(worker_ids)), worker_labels)
    
    # 添加颜色条
    cbar = plt.colorbar(im)
    cbar.set_label('Activity Intensity', rotation=270, labelpad=20)
    
    # 设置标题和标签
    plt.title('Worker Activity Heatmap Over Time', fontsize=16, fontweight='bold')
    plt.xlabel('Time', fontsize=12)
    plt.ylabel('Workers', fontsize=12)
    
    # 添加网格
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存热力图
    heatmap_path = os.path.join(output_dir, "worker_activity_heatmap.png")
    plt.savefig(heatmap_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"工人活动热力图已保存到: {heatmap_path}")

def generate_worker_statistics_chart(worker_stats, output_dir):
    """生成工人统计信息图表"""
    print("生成工人统计信息图表...")
    
    # 提取统计数据
    worker_ids = []
    durations = []
    distances = []
    speeds = []
    track_points = []
    
    for worker_id, stats in worker_stats.items():
        worker_ids.append(int(worker_id))
        durations.append(stats['duration'])
        distances.append(stats['total_distance'])
        speeds.append(stats['avg_speed'])
        track_points.append(stats['track_points'])
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Worker Statistics Summary', fontsize=16, fontweight='bold')
    
    # 1. 工人活动时长
    axes[0, 0].bar(worker_ids, durations, color='skyblue', alpha=0.7)
    axes[0, 0].set_title('Worker Activity Duration')
    axes[0, 0].set_xlabel('Worker ID')
    axes[0, 0].set_ylabel('Duration (seconds)')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, v in enumerate(durations):
        axes[0, 0].text(worker_ids[i], v + max(durations)*0.01, f'{v:.1f}s', 
                       ha='center', va='bottom', fontsize=8)
    
    # 2. 工人移动距离
    axes[0, 1].bar(worker_ids, distances, color='lightgreen', alpha=0.7)
    axes[0, 1].set_title('Worker Total Distance')
    axes[0, 1].set_xlabel('Worker ID')
    axes[0, 1].set_ylabel('Distance (pixels)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 工人平均速度
    axes[1, 0].bar(worker_ids, speeds, color='orange', alpha=0.7)
    axes[1, 0].set_title('Worker Average Speed')
    axes[1, 0].set_xlabel('Worker ID')
    axes[1, 0].set_ylabel('Speed (pixels/second)')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 工人跟踪点数
    axes[1, 1].bar(worker_ids, track_points, color='pink', alpha=0.7)
    axes[1, 1].set_title('Worker Track Points')
    axes[1, 1].set_xlabel('Worker ID')
    axes[1, 1].set_ylabel('Number of Track Points')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存统计图表
    stats_chart_path = os.path.join(output_dir, "worker_statistics_summary.png")
    plt.savefig(stats_chart_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"工人统计信息图表已保存到: {stats_chart_path}")
    
    # 打印统计摘要
    print(f"\n工人统计摘要:")
    print(f"  总工人数: {len(worker_ids)}")
    print(f"  平均活动时长: {np.mean(durations):.2f} 秒")
    print(f"  平均移动距离: {np.mean(distances):.2f} 像素")
    print(f"  平均速度: {np.mean(speeds):.2f} 像素/秒")
    print(f"  最活跃工人: ID {worker_ids[np.argmax(durations)]} (时长 {max(durations):.2f}秒)")
    print(f"  最快工人: ID {worker_ids[np.argmax(speeds)]} (速度 {max(speeds):.2f}像素/秒)")

def main():
    # 设置路径
    analysis_dir = "runs/track/yolov5/best_22_osnet_x0_2518/analysis"
    video_file = "image/测试基准视频.mp4"
    
    # 检查文件是否存在
    stats_file = os.path.join(analysis_dir, "worker_stats_fixed.json")
    if not os.path.exists(stats_file):
        print(f"修复后的统计文件不存在: {stats_file}")
        return
    
    print("=" * 60)
    print("生成详细的工人分析图表")
    print("=" * 60)
    
    # 加载数据
    worker_stats = load_worker_stats(stats_file)
    
    # 获取视频信息
    if os.path.exists(video_file):
        cap = cv2.VideoCapture(video_file)
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        video_info = {'fps': fps, 'frame_count': frame_count}
        cap.release()
    else:
        video_info = None
    
    # 生成各种图表
    generate_detailed_worker_count_charts(worker_stats, video_info, analysis_dir)
    generate_worker_activity_heatmap(worker_stats, video_info, analysis_dir)
    generate_worker_statistics_chart(worker_stats, analysis_dir)
    
    print("=" * 60)
    print("详细图表生成完成！")
    print("=" * 60)

if __name__ == '__main__':
    main()
