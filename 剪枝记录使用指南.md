# 🔧 模型剪枝记录系统使用指南

## 📋 概述

模型剪枝记录系统能够自动记录每次剪枝实验的详细信息，包括：
- 剪枝参数配置
- 性能对比数据
- 模型文件信息
- 可视化图表
- HTML详细报告

## 🚀 快速开始

### 1. 运行剪枝并记录
```bash
# 基本剪枝命令（自动记录）
python optimized_sparse_prune.py \
    --weights yolov5/best_22.pt \
    --percent 0.15 \
    --save-path my_pruned_model.pt \
    --strategy balanced \
    --benchmark \
    --optimize \
    --note "我的第一次剪枝实验"
```

### 2. 查看所有实验记录
```bash
# 列出所有剪枝实验
python view_pruning_logs.py list
```

### 3. 查看特定实验详情
```bash
# 查看实验详细信息
python view_pruning_logs.py detail pruning_exp_20250603_213256
```

### 4. 打开HTML报告
```bash
# 在浏览器中打开详细报告
python view_pruning_logs.py report pruning_exp_20250603_213256
```

## 📊 记录系统功能详解

### 🔍 查看功能

#### 1. 列出所有实验
```bash
python view_pruning_logs.py list
```
显示所有实验的概览表格，包括：
- 实验ID
- 开始时间
- 剪枝方法
- 性能提升百分比

#### 2. 查看实验详情
```bash
python view_pruning_logs.py detail <实验ID>
```
显示特定实验的详细信息：
- 基本信息（时间、方法等）
- 剪枝参数配置
- 性能对比数据
- 剪枝结果统计
- 模型文件信息
- 实验备注

#### 3. 打开HTML报告
```bash
python view_pruning_logs.py report <实验ID>
```
在浏览器中打开包含图表的详细HTML报告

#### 4. 比较多个实验
```bash
python view_pruning_logs.py compare exp1 exp2 exp3
```
对比多个实验的关键指标

#### 5. 生成总结报告
```bash
python view_pruning_logs.py summary
```
生成所有实验的统计总结

### 🧹 管理功能

#### 清理旧记录
```bash
# 清理30天前的记录
python view_pruning_logs.py clean --days 30

# 清理7天前的记录
python view_pruning_logs.py clean --days 7
```

## 📁 文件结构

剪枝记录系统会创建以下目录结构：

```
pruning_logs/
├── master_log.json                    # 总体记录索引
├── experiments/                       # 实验记录目录
│   └── pruning_exp_20250603_213256/   # 单个实验目录
│       ├── experiment_log.json       # 实验详细记录
│       ├── report.html               # HTML报告
│       └── my_pruned_model.pt        # 剪枝后模型备份
├── plots/                            # 图表目录
│   └── pruning_exp_20250603_213256_performance.png
└── reports/                          # 报告目录
```

## 🎯 实际使用场景

### 场景1: 寻找最佳剪枝比例
```bash
# 测试不同剪枝比例
python optimized_sparse_prune.py --percent 0.1 --note "10%剪枝测试" --benchmark
python optimized_sparse_prune.py --percent 0.15 --note "15%剪枝测试" --benchmark
python optimized_sparse_prune.py --percent 0.2 --note "20%剪枝测试" --benchmark

# 比较结果
python view_pruning_logs.py summary
```

### 场景2: 对比不同剪枝策略
```bash
# 测试不同策略
python optimized_sparse_prune.py --strategy conservative --note "保守策略" --benchmark
python optimized_sparse_prune.py --strategy balanced --note "平衡策略" --benchmark
python optimized_sparse_prune.py --strategy aggressive --note "激进策略" --benchmark

# 查看对比
python view_pruning_logs.py list
```

### 场景3: 追踪实验历史
```bash
# 查看所有实验
python view_pruning_logs.py list

# 查看最佳实验详情
python view_pruning_logs.py detail <最佳实验ID>

# 打开详细报告
python view_pruning_logs.py report <最佳实验ID>
```

## 📈 记录的数据类型

### 性能数据
- **FPS**: 每秒帧数
- **推理时间**: 单次推理耗时（毫秒）
- **模型大小**: 文件大小（MB）

### 剪枝数据
- **稀疏度**: 被置零的参数比例
- **阈值**: 剪枝使用的阈值
- **层数统计**: 可剪枝层和忽略层数量

### 配置数据
- **剪枝比例**: 目标剪枝百分比
- **剪枝策略**: conservative/balanced/aggressive
- **优化选项**: 是否启用层融合等优化

## 🔧 高级功能

### 自定义日志目录
```bash
python optimized_sparse_prune.py --log-dir my_custom_logs
python view_pruning_logs.py --log-dir my_custom_logs list
```

### 添加详细备注
```bash
python optimized_sparse_prune.py \
    --note "针对建筑工地场景的优化实验，重点关注检测精度保持" \
    --benchmark
```

### 批量实验脚本示例
```bash
#!/bin/bash
# 批量测试不同参数组合

for percent in 0.1 0.15 0.2 0.25; do
    for strategy in conservative balanced aggressive; do
        python optimized_sparse_prune.py \
            --percent $percent \
            --strategy $strategy \
            --note "批量测试: ${percent}剪枝-${strategy}策略" \
            --benchmark \
            --save-path "batch_${percent}_${strategy}.pt"
    done
done

# 查看所有结果
python view_pruning_logs.py summary
```

## 📊 HTML报告功能

HTML报告包含：
1. **性能提升概览** - 关键指标的卡片显示
2. **剪枝配置表** - 详细的参数配置
3. **性能对比表** - 剪枝前后的数值对比
4. **可视化图表** - 性能对比的图形化展示

## 💡 最佳实践

### 1. 命名规范
- 使用有意义的备注描述实验目的
- 为不同用途的模型使用不同的保存路径

### 2. 实验管理
- 定期查看总结报告了解实验趋势
- 及时清理不需要的旧实验记录
- 备份重要的实验结果

### 3. 性能分析
- 关注FPS提升和时间减少的平衡
- 注意模型大小的变化
- 结合实际应用场景评估效果

## 🚨 注意事项

1. **中文字体问题**: 图表中的中文可能显示为方框，这不影响功能使用
2. **磁盘空间**: 每个实验会备份模型文件，注意磁盘空间使用
3. **实验ID**: 基于时间戳生成，确保唯一性
4. **JSON兼容性**: 记录数据使用JSON格式，便于程序处理

## 📞 故障排除

### 问题1: 无法找到实验记录
```bash
# 检查日志目录
ls pruning_logs/experiments/

# 检查master_log.json
cat pruning_logs/master_log.json
```

### 问题2: HTML报告打不开
```bash
# 检查文件是否存在
ls pruning_logs/experiments/*/report.html

# 手动打开文件
start pruning_logs/experiments/pruning_exp_*/report.html
```

### 问题3: 图表显示异常
- 中文字体问题是正常现象，不影响数据准确性
- 可以通过安装中文字体解决显示问题

---

**🎉 通过剪枝记录系统，您可以系统地管理和分析所有的模型优化实验，找到最佳的剪枝配置！**
