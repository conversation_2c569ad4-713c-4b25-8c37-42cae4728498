weights: yolov5s.pt
cfg: ''
data: "D:\\12914\\Documents\\Junior\\\u571F\u6728\u5DE5\u7A0B\u65BD\u5DE5\u57FA\u672C\
  \u539F\u7406\\\u5B9E\u9A8C\\2025\\yolo_deepsort\\Yolov5_DeepSort_Pytorch-master\\\
  yolov5\\data\\my_worker.yaml"
hyp: data\hyps\hyp.scratch-low.yaml
epochs: 1
batch_size: 1
imgsz: 640
rect: false
resume: false
nosave: false
noval: false
noautoanchor: false
evolve: null
bucket: ''
cache: null
image_weights: false
device: ''
multi_scale: false
single_cls: false
optimizer: SGD
sync_bn: false
workers: 8
project: runs\train
name: exp
exist_ok: false
quad: false
cos_lr: false
label_smoothing: 0.0
patience: 100
freeze:
- 0
save_period: -1
local_rank: -1
entity: null
upload_dataset: false
bbox_interval: -1
artifact_alias: latest
save_dir: runs\train\exp3
