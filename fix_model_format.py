import torch
import sys
import argparse

def fix_model_format(input_path, output_path):
    """
    修复模型格式，确保与YOLOv5兼容
    
    参数:
        input_path (str): 输入模型路径
        output_path (str): 输出模型路径
    """
    print(f"加载模型: {input_path}")
    
    try:
        # 加载模型
        model = torch.load(input_path, map_location='cpu')
        
        # 检查模型格式
        if isinstance(model, dict) and ('model' in model or 'ema' in model):
            print("模型已经是正确的格式，无需修复")
            fixed_model = model
        else:
            print("模型格式需要修复")
            # 创建正确格式的字典
            fixed_model = {'model': model}
            print("已将模型包装为字典格式")
        
        # 保存修复后的模型
        torch.save(fixed_model, output_path)
        print(f"修复后的模型已保存到: {output_path}")
        
        return True
    except Exception as e:
        print(f"修复模型时出错: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='修复YOLOv5模型格式')
    parser.add_argument('--input', type=str, required=True, help='输入模型路径')
    parser.add_argument('--output', type=str, required=True, help='输出模型路径')
    
    args = parser.parse_args()
    
    fix_model_format(args.input, args.output)

if __name__ == '__main__':
    main()
