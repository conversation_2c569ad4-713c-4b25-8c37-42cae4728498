name: CI CPU testing

on:  # https://help.github.com/en/actions/reference/events-that-trigger-workflows
  push:
    branches: [master, <PERSON><PERSON>debug]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [master, CIdebug]

jobs:
  cpu-tests:

    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest]  # Error: Container action is only supported on Linux
        python-version: [3.9]
        model: ['yolov5s']  # models to test

    # Timeout: https://stackoverflow.com/a/59076067/4521646
    timeout-minutes: 50
    steps:
      - name: Set up Repository
        uses: actions/checkout@v2
        with:
          submodules: recursive
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}

      # Note: This uses an internal pip API and may not always work
      # https://github.com/actions/cache/blob/master/examples.md#multiple-oss-in-a-workflow
      - name: Get pip cache
        id: pip-cache
        run: |
          python -c "from pip._internal.locations import USER_CACHE_DIR; print('::set-output name=dir::' + USER_CACHE_DIR)"
      - name: Cache pip
        uses: actions/cache@v1
        with:
          path: ${{ steps.pip-cache.outputs.dir }}
          key: ${{ runner.os }}-${{ matrix.python-version }}-pip-${{ hashFiles('requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.python-version }}-pip-  
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -qr requirements.txt -f https://download.pytorch.org/whl/cpu/torch_stable.html
          pip install requests
          python --version
          pip --version
          pip list
          cd
        shell: bash
      - name: Tests workflow
        run: |
          # otherwise problems placing the yolo weights in submodule
          sudo chmod -R 777 /home/
          python track.py --yolo_model yolov5n.pt --deep_sort_model osnet_x0_25_MSMT17 --source yolov5/data/images/
        shell: bash