#!/usr/bin/env python3
"""
测试整合后的工人分析功能
"""

import os
import sys
import json
import pandas as pd
from worker_analysis import WorkerAnalyzer

def create_mock_tracks_file():
    """创建模拟的tracks.txt文件用于测试"""
    print("创建模拟tracks.txt文件...")
    
    # 模拟跟踪数据
    tracks_data = []
    
    # 工人1: 全程活跃
    for frame in range(1, 501, 5):
        tracks_data.append([frame, 1, 100+frame//10, 200, 50, 100, 0.8, 0, 1])
    
    # 工人2: 中途出现
    for frame in range(100, 501, 5):
        tracks_data.append([frame, 2, 200+frame//15, 300, 50, 100, 0.9, 0, 1])
    
    # 工人3: 早期出现
    for frame in range(1, 200, 5):
        tracks_data.append([frame, 3, 300+frame//20, 400, 50, 100, 0.7, 0, 1])
    
    # 工人4: 短暂出现
    for frame in range(200, 250, 5):
        tracks_data.append([frame, 4, 400, 500, 50, 100, 0.6, 0, 1])
    
    # 保存到文件
    tracks_file = "test_tracks.txt"
    with open(tracks_file, 'w') as f:
        for track in tracks_data:
            f.write(' '.join(map(str, track)) + '\n')
    
    print(f"模拟tracks文件已创建: {tracks_file}")
    print(f"包含 {len(tracks_data)} 条跟踪记录")
    
    return tracks_file

def test_integrated_analysis():
    """测试整合后的工人分析功能"""
    print("=" * 60)
    print("测试整合后的工人分析功能")
    print("=" * 60)
    
    # 创建模拟数据
    tracks_file = create_mock_tracks_file()
    video_file = "image/测试基准视频.mp4"
    output_dir = "test_integrated_output"
    
    try:
        # 创建分析器实例
        print("\n创建WorkerAnalyzer实例...")
        analyzer = WorkerAnalyzer(tracks_file, video_file, output_dir)
        
        # 运行分析
        print("\n开始分析...")
        analyzer.analyze()
        
        print("\n✅ 整合后的工人分析功能测试成功！")
        
        # 检查生成的文件
        print("\n检查生成的文件:")
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            for file in files:
                file_path = os.path.join(output_dir, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  ✅ {file} - {size} bytes")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        if os.path.exists(tracks_file):
            os.remove(tracks_file)
            print(f"\n清理临时文件: {tracks_file}")

def test_with_existing_data():
    """使用现有数据测试"""
    print("=" * 60)
    print("使用现有数据测试整合功能")
    print("=" * 60)
    
    # 检查现有数据
    existing_stats = "runs/track/yolov5/best_22_osnet_x0_2518/analysis/worker_stats_fixed.json"
    video_file = "image/测试基准视频.mp4"
    output_dir = "test_existing_data_output"
    
    if not os.path.exists(existing_stats):
        print(f"❌ 现有统计文件不存在: {existing_stats}")
        return
    
    try:
        # 从现有统计数据重建tracks数据
        print("从现有统计数据重建tracks数据...")
        
        with open(existing_stats, 'r', encoding='utf-8') as f:
            stats_data = json.load(f)
        
        # 创建模拟的tracks数据
        tracks_data = []
        for worker_id, stats in stats_data.items():
            first_frame = stats['first_frame']
            last_frame = stats['last_frame']
            track_points = stats['track_points']
            
            # 生成该工人的轨迹点
            step = max(1, (last_frame - first_frame) // track_points) if track_points > 0 else 1
            for frame in range(first_frame, last_frame + 1, step):
                x = 100 + int(worker_id) * 50
                y = 200 + frame // 10
                tracks_data.append([frame, int(worker_id), x, y, 50, 100, 0.8, 0, 1])
        
        # 保存tracks文件
        tracks_file = "reconstructed_tracks.txt"
        with open(tracks_file, 'w') as f:
            for track in tracks_data:
                f.write(' '.join(map(str, track)) + '\n')
        
        print(f"重建的tracks文件已创建: {tracks_file}")
        print(f"包含 {len(tracks_data)} 条跟踪记录")
        
        # 运行分析
        print("\n创建WorkerAnalyzer实例...")
        analyzer = WorkerAnalyzer(tracks_file, video_file, output_dir)
        
        print("\n开始分析...")
        analyzer.analyze()
        
        print("\n✅ 使用现有数据的测试成功！")
        
        # 检查生成的文件
        print("\n检查生成的文件:")
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            for file in files:
                file_path = os.path.join(output_dir, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  ✅ {file} - {size} bytes")
        
        # 清理临时文件
        if os.path.exists(tracks_file):
            os.remove(tracks_file)
            print(f"\n清理临时文件: {tracks_file}")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 开始测试整合后的工人分析功能")
    
    # 测试1: 使用模拟数据
    test_integrated_analysis()
    
    print("\n" + "="*60)
    
    # 测试2: 使用现有数据
    test_with_existing_data()
    
    print("\n🎉 所有测试完成！")

if __name__ == '__main__':
    main()
