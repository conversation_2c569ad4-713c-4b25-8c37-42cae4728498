import torch
import sys
import argparse
import os
import copy

def check_model(model_path):
    """
    检查模型结构和格式
    
    参数:
        model_path (str): 模型文件路径
    
    返回:
        model: 模型对象
        model_dict: 原始模型字典
    """
    print(f"检查模型: {model_path}")
    
    try:
        # 加载模型
        model_data = torch.load(model_path, map_location='cpu')
        
        # 检查模型类型
        print(f"模型类型: {type(model_data)}")
        
        # 如果是字典，提取模型
        if isinstance(model_data, dict):
            print(f"模型是字典格式，包含以下键: {list(model_data.keys())}")
            
            # 尝试获取模型
            model = None
            
            # 首先尝试EMA模型
            if 'ema' in model_data and model_data['ema'] is not None:
                print("使用EMA模型")
                model = model_data['ema']
            # 然后尝试普通模型
            elif 'model' in model_data and model_data['model'] is not None:
                print("使用普通模型")
                model = model_data['model']
            
            # 如果仍然没有找到模型，尝试其他键
            if model is None:
                for key, value in model_data.items():
                    if isinstance(value, torch.nn.Module):
                        print(f"使用键'{key}'中的模型")
                        model = value
                        break
            
            # 如果仍然没有找到模型，返回错误
            if model is None:
                raise ValueError("在模型字典中找不到有效的模型")
            
            return model, model_data
        
        # 如果直接是模型对象
        elif isinstance(model_data, torch.nn.Module):
            print("模型是直接的PyTorch模型对象")
            return model_data, {'model': model_data}
        
        else:
            raise TypeError(f"未知模型格式: {type(model_data)}")
    
    except Exception as e:
        print(f"检查模型时出错: {e}")
        raise

def analyze_model(model):
    """
    分析模型结构和参数量
    
    参数:
        model: PyTorch模型
    """
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型总参数量: {total_params:,}")
    
    # 分析每层参数
    print("\n各层参数分布:")
    for name, module in model.named_modules():
        if isinstance(module, torch.nn.Conv2d) or isinstance(module, torch.nn.Linear):
            params = sum(p.numel() for p in module.parameters())
            print(f"{name}: {params:,} 参数")

def prune_model_l1(model, amount=0.15):
    """
    使用L1范数剪枝YOLOv5模型
    
    参数:
        model: PyTorch模型
        amount (float): 剪枝比例 (0.0-1.0)
    
    返回:
        pruned_model: 剪枝后的模型
    """
    print(f"开始L1范数剪枝，剪枝比例: {amount*100:.1f}%")
    
    # 创建模型副本
    pruned_model = copy.deepcopy(model)
    
    # 获取所有卷积层
    conv_layers = []
    for name, module in pruned_model.named_modules():
        if isinstance(module, torch.nn.Conv2d) and not name.startswith('model.24'):  # 不剪枝检测头
            conv_layers.append((name, module))
    
    print(f"找到 {len(conv_layers)} 个卷积层可以剪枝")
    
    # 对每个卷积层进行剪枝
    for name, conv in conv_layers:
        # 获取卷积权重
        weight = conv.weight.data.clone()
        
        # 计算每个通道的L1范数
        channel_norms = torch.sum(torch.abs(weight), dim=(1, 2, 3))
        
        # 确定要保留的通道数
        num_channels = weight.size(0)
        num_to_keep = int(num_channels * (1 - amount))
        num_to_keep = max(num_to_keep, 1)  # 至少保留一个通道
        
        # 找出重要的通道
        _, indices = torch.topk(channel_norms, num_to_keep)
        mask = torch.zeros(num_channels)
        mask[indices] = 1
        
        # 应用掩码
        conv.weight.data = conv.weight.data * mask.view(-1, 1, 1, 1)
        
        print(f"剪枝层 {name}: 从 {num_channels} 个通道中保留 {num_to_keep} 个")
    
    return pruned_model

def save_model(model, original_dict, save_path):
    """
    保存模型，保持与原始模型相同的格式
    
    参数:
        model: 剪枝后的模型
        original_dict: 原始模型字典
        save_path: 保存路径
    """
    print(f"保存模型到: {save_path}")
    
    # 创建新的模型字典，保持与原始模型相同的格式
    new_dict = copy.deepcopy(original_dict)
    
    # 更新模型
    if 'ema' in new_dict and new_dict['ema'] is not None:
        new_dict['ema'] = model
    elif 'model' in new_dict:
        new_dict['model'] = model
    else:
        # 如果原始字典没有标准键，创建一个新的字典
        new_dict = {'model': model}
    
    # 保存模型
    torch.save(new_dict, save_path)
    print(f"模型已保存")

def benchmark_model(model, img_size=640, iterations=50):
    """
    基准测试模型推理速度
    
    参数:
        model: PyTorch模型
        img_size (int): 图像大小
        iterations (int): 迭代次数
    """
    print(f"基准测试模型推理速度...")
    
    # 准备输入
    dummy_input = torch.randn(1, 3, img_size, img_size)
    model.eval()
    
    try:
        # 预热
        print("开始预热...")
        with torch.no_grad():
            for i in range(5):
                _ = model(dummy_input)
                if i == 0:
                    print("成功完成第一次推理")
        
        # 计时
        print(f"开始计时 {iterations} 次迭代...")
        import time
        start_time = time.time()
        with torch.no_grad():
            for _ in range(iterations):
                _ = model(dummy_input)
        end_time = time.time()
        
        # 计算FPS
        elapsed_time = end_time - start_time
        fps = iterations / elapsed_time
        
        print(f"推理速度: {fps:.2f} FPS (每帧 {1000/fps:.2f} ms)")
        print(f"{iterations}次迭代总耗时: {elapsed_time:.2f} 秒")
    except Exception as e:
        print(f"基准测试时出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='YOLOv5简单模型剪枝工具')
    parser.add_argument('--weights', type=str, default='yolov5/best_22.pt', help='模型权重文件路径')
    parser.add_argument('--amount', type=float, default=0.15, help='剪枝比例 (0.0-1.0)')
    parser.add_argument('--save-path', type=str, default='pruned_worker_model.pt', help='剪枝后模型保存路径')
    parser.add_argument('--benchmark', action='store_true', help='是否进行基准测试')
    parser.add_argument('--img-size', type=int, default=640, help='图像大小')
    
    args = parser.parse_args()
    
    try:
        # 检查并加载模型
        model, model_dict = check_model(args.weights)
        
        # 分析原始模型
        print("\n原始模型信息:")
        analyze_model(model)
        
        # 如果需要，进行基准测试
        if args.benchmark:
            print("\n原始模型基准测试:")
            benchmark_model(model, args.img_size)
        
        # 剪枝模型
        print("\n开始剪枝模型...")
        pruned_model = prune_model_l1(model, args.amount)
        
        # 分析剪枝后的模型
        print("\n剪枝后模型信息:")
        analyze_model(pruned_model)
        
        # 保存剪枝后的模型
        save_model(pruned_model, model_dict, args.save_path)
        
        # 如果需要，进行基准测试
        if args.benchmark:
            print("\n剪枝后模型基准测试:")
            benchmark_model(pruned_model, args.img_size)
        
        print("\n剪枝完成!")
    
    except Exception as e:
        print(f"剪枝过程中出错: {e}")

if __name__ == '__main__':
    main()
