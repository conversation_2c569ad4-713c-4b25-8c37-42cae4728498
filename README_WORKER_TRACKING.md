# 施工现场工人追踪系统使用说明

本系统基于YOLOv5和DeepSort，针对施工现场工人追踪进行了优化。系统可以实时检测和追踪工人，分析工人行为，并提供安全监控功能。

## 功能特点

1. **工人检测与追踪**
   - 使用优化的YOLOv5模型检测工人
   - 使用改进的DeepSort算法进行工人追踪
   - 支持多摄像头输入

2. **工人行为分析**
   - 工人活动区域热图生成
   - 工人移动轨迹分析
   - 工人工作时长统计

3. **安全监控**
   - 危险区域警告
   - 禁止区域监控
   - 工人静止不动检测（可能表示事故）
   - 人群聚集检测

4. **数据分析与报告**
   - 生成工人行为分析报告
   - 提供异常行为检测
   - 支持数据可视化

## 系统要求

- Python 3.8+
- PyTorch 1.7+
- CUDA 10.2+ (推荐，用于GPU加速)
- OpenCV 4.5+

## 安装说明

1. 克隆仓库
```bash
git clone https://github.com/your-username/yolov5-deepsort-worker-tracking.git
cd yolov5-deepsort-worker-tracking
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 下载预训练模型
```bash
# 下载YOLOv5预训练模型
wget https://github.com/ultralytics/yolov5/releases/download/v6.0/yolov5s.pt -O yolov5/yolov5s.pt

# 或使用已训练好的工人检测模型
# 已包含在项目中: yolov5/best.pt
```

## 使用方法

### 基本追踪

```bash
# 使用摄像头进行实时追踪
python track.py --source 0 --show-vid

# 处理视频文件
python track.py --source path/to/video.mp4 --save-vid

# 处理图像文件夹
python track.py --source path/to/images --save-vid
```

### 启用工人监控功能

```bash
# 启用工人监控功能
python track.py --source path/to/video.mp4 --save-vid --enable-worker-monitor

# 自定义危险区域和禁止区域
python track.py --source path/to/video.mp4 --save-vid --enable-worker-monitor \
    --danger-zone "100,100,300,100,300,300,100,300" \
    --restricted-area "400,400,600,400,600,600,400,600"

# 调整静止检测时间和人群聚集阈值
python track.py --source path/to/video.mp4 --save-vid --enable-worker-monitor \
    --stationary-time 20 --crowd-threshold 5
```

### 工人行为分析

```bash
# 分析追踪结果并生成报告
python worker_analysis.py --tracks runs/track/exp/tracks/video_name.txt --video path/to/video.mp4 --output analysis_results
```

## 参数说明

### track.py 主要参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--source` | 输入源 (0=摄像头, 视频文件路径, 图像文件夹路径) | `./image` |
| `--yolo_model` | YOLOv5模型路径 | `yolov5/best.pt` |
| `--deep_sort_model` | DeepSort模型名称 | `osnet_ibn_x1_0_MSMT17` |
| `--output` | 输出文件夹 | `inference/output` |
| `--imgsz` | 推理尺寸 | `640` |
| `--conf-thres` | 目标置信度阈值 | `0.5` |
| `--iou-thres` | NMS IOU阈值 | `0.5` |
| `--device` | CUDA设备 (例如 0, 1, 2, 3 或 cpu) | `''` |
| `--show-vid` | 显示追踪视频结果 | `False` |
| `--save-vid` | 保存视频追踪结果 | `False` |
| `--save-txt` | 保存MOT格式结果到*.txt | `False` |
| `--classes` | 按类别过滤 (例如 --class 0) | `None` |
| `--enable-worker-monitor` | 启用工人监控功能 | `False` |
| `--danger-zone` | 危险区域坐标 (格式: x1,y1,x2,y2,x3,y3...) | `''` |
| `--restricted-area` | 禁止区域坐标 (格式: x1,y1,x2,y2,x3,y3...) | `''` |
| `--stationary-time` | 判定工人静止不动的时间阈值（秒） | `15` |
| `--crowd-threshold` | 判定为人群聚集的人数阈值 | `3` |

### worker_analysis.py 主要参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--tracks` | 跟踪结果文件路径（MOT格式） | 必填 |
| `--video` | 视频文件路径（可选） | `None` |
| `--output` | 输出目录 | `analysis_results` |

## 自定义模型训练

如果需要针对特定施工场景训练自己的工人检测模型，可以按照以下步骤操作：

1. 准备数据集
   - 收集施工现场工人图像
   - 使用标注工具（如LabelImg）标注工人位置
   - 将数据集组织为YOLOv5格式

2. 修改配置文件
   - 编辑 `yolov5/data/my_worker.yaml` 文件，指定数据集路径和类别

3. 训练模型
```bash
cd yolov5
python train.py --data data/my_worker.yaml --weights yolov5s.pt --img 640 --epochs 100
```

4. 使用新训练的模型
```bash
python track.py --source path/to/video.mp4 --yolo_model yolov5/runs/train/exp/weights/best.pt --save-vid
```

## 常见问题

1. **Q: 系统运行速度较慢怎么办？**
   
   A: 可以尝试以下方法提高速度：
   - 使用更小的YOLOv5模型（如yolov5s或yolov5n）
   - 减小输入图像尺寸（使用--imgsz参数）
   - 使用GPU加速（确保CUDA正确安装）
   - 降低处理帧率（对于视频文件）

2. **Q: 如何提高工人识别准确率？**
   
   A: 可以尝试以下方法：
   - 使用更多施工现场的图像数据训练模型
   - 调整检测置信度阈值（--conf-thres参数）
   - 优化摄像头位置和角度
   - 在不同光照条件下收集训练数据

3. **Q: 如何自定义危险区域和禁止区域？**
   
   A: 使用--danger-zone和--restricted-area参数，格式为多边形顶点坐标序列，例如：
   ```
   --danger-zone "x1,y1,x2,y2,x3,y3,x4,y4"
   ```

## 联系方式

如有问题或建议，请联系：<EMAIL>
