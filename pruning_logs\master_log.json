{"experiments": [{"experiment_id": "pruning_exp_20250603_213256", "start_time": "2025-06-03T21:32:56.151017", "end_time": "2025-06-03T21:33:14.313585", "pruning_method": "sparse_pruning", "improvements": {"fps_improvement_percent": -24.52524452175322, "time_reduction_percent": -32.49463263093557, "size_reduction_percent": -96.14929044392157}, "experiment_dir": "pruning_logs\\experiments\\pruning_exp_20250603_213256"}, {"experiment_id": "pruning_exp_20250604_212622", "start_time": "2025-06-04T21:26:22.664349", "end_time": "2025-06-04T21:27:10.242347", "pruning_method": "sparse_pruning", "improvements": {"fps_improvement_percent": -4.30484928024396, "time_reduction_percent": -4.498503056702163, "size_reduction_percent": -97.48230606970859}, "experiment_dir": "pruning_logs\\experiments\\pruning_exp_20250604_212622"}, {"experiment_id": "pruning_exp_20250604_213127", "start_time": "2025-06-04T21:31:27.534360", "end_time": "2025-06-04T21:31:39.308507", "pruning_method": "sparse_pruning", "improvements": {"fps_improvement_percent": 2.04591755552459, "time_reduction_percent": 2.004898975416014, "size_reduction_percent": -97.48230606970859}, "experiment_dir": "pruning_logs\\experiments\\pruning_exp_20250604_213127"}, {"experiment_id": "pruning_exp_20250604_213735", "start_time": "2025-06-04T21:37:35.391666", "end_time": "2025-06-04T21:37:48.300383", "pruning_method": "sparse_pruning_v2", "improvements": {"fps_improvement_percent": 0.4805684317324532, "time_reduction_percent": 0.47827001701226113, "size_reduction_percent": -97.49276717184381}, "experiment_dir": "pruning_logs\\experiments\\pruning_exp_20250604_213735"}]}