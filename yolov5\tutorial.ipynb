{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"name": "YOLOv5 Tutorial", "provenance": [], "collapsed_sections": [], "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU", "widgets": {"application/vnd.jupyter.widget-state+json": {"eb95db7cae194218b3fcefb439b6352f": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_view_name": "HBoxView", "_dom_classes": [], "_model_name": "HBoxModel", "_view_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_view_count": null, "_view_module_version": "1.5.0", "box_style": "", "layout": "IPY_MODEL_769ecde6f2e64bacb596ce972f8d3d2d", "_model_module": "@jupyter-widgets/controls", "children": ["IPY_MODEL_384a001876054c93b0af45cd1e960bfe", "IPY_MODEL_dded0aeae74440f7ba2ffa0beb8dd612", "IPY_MODEL_5296d28be75740b2892ae421bbec3657"]}}, "769ecde6f2e64bacb596ce972f8d3d2d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_view_name": "LayoutView", "grid_template_rows": null, "right": null, "justify_content": null, "_view_module": "@jupyter-widgets/base", "overflow": null, "_model_module_version": "1.2.0", "_view_count": null, "flex_flow": null, "width": null, "min_width": null, "border": null, "align_items": null, "bottom": null, "_model_module": "@jupyter-widgets/base", "top": null, "grid_column": null, "overflow_y": null, "overflow_x": null, "grid_auto_flow": null, "grid_area": null, "grid_template_columns": null, "flex": null, "_model_name": "LayoutModel", "justify_items": null, "grid_row": null, "max_height": null, "align_content": null, "visibility": null, "align_self": null, "height": null, "min_height": null, "padding": null, "grid_auto_rows": null, "grid_gap": null, "max_width": null, "order": null, "_view_module_version": "1.2.0", "grid_template_areas": null, "object_position": null, "object_fit": null, "grid_auto_columns": null, "margin": null, "display": null, "left": null}}, "384a001876054c93b0af45cd1e960bfe": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_view_name": "HTMLView", "style": "IPY_MODEL_9f09facb2a6c4a7096810d327c8b551c", "_dom_classes": [], "description": "", "_model_name": "HTMLModel", "placeholder": "​", "_view_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "value": "100%", "_view_count": null, "_view_module_version": "1.5.0", "description_tooltip": null, "_model_module": "@jupyter-widgets/controls", "layout": "IPY_MODEL_25621cff5d16448cb7260e839fd0f543"}}, "dded0aeae74440f7ba2ffa0beb8dd612": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_view_name": "ProgressView", "style": "IPY_MODEL_0ce7164fc0c74bb9a2b5c7037375a727", "_dom_classes": [], "description": "", "_model_name": "FloatProgressModel", "bar_style": "success", "max": 818322941, "_view_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "value": 818322941, "_view_count": null, "_view_module_version": "1.5.0", "orientation": "horizontal", "min": 0, "description_tooltip": null, "_model_module": "@jupyter-widgets/controls", "layout": "IPY_MODEL_c4c4593c10904cb5b8a5724d60c7e181"}}, "5296d28be75740b2892ae421bbec3657": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_view_name": "HTMLView", "style": "IPY_MODEL_473371611126476c88d5d42ec7031ed6", "_dom_classes": [], "description": "", "_model_name": "HTMLModel", "placeholder": "​", "_view_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "value": " 780M/780M [00:11&lt;00:00, 91.9MB/s]", "_view_count": null, "_view_module_version": "1.5.0", "description_tooltip": null, "_model_module": "@jupyter-widgets/controls", "layout": "IPY_MODEL_65efdfd0d26c46e79c8c5ff3b77126cc"}}, "9f09facb2a6c4a7096810d327c8b551c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_view_name": "StyleView", "_model_name": "DescriptionStyleModel", "description_width": "", "_view_module": "@jupyter-widgets/base", "_model_module_version": "1.5.0", "_view_count": null, "_view_module_version": "1.2.0", "_model_module": "@jupyter-widgets/controls"}}, "25621cff5d16448cb7260e839fd0f543": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_view_name": "LayoutView", "grid_template_rows": null, "right": null, "justify_content": null, "_view_module": "@jupyter-widgets/base", "overflow": null, "_model_module_version": "1.2.0", "_view_count": null, "flex_flow": null, "width": null, "min_width": null, "border": null, "align_items": null, "bottom": null, "_model_module": "@jupyter-widgets/base", "top": null, "grid_column": null, "overflow_y": null, "overflow_x": null, "grid_auto_flow": null, "grid_area": null, "grid_template_columns": null, "flex": null, "_model_name": "LayoutModel", "justify_items": null, "grid_row": null, "max_height": null, "align_content": null, "visibility": null, "align_self": null, "height": null, "min_height": null, "padding": null, "grid_auto_rows": null, "grid_gap": null, "max_width": null, "order": null, "_view_module_version": "1.2.0", "grid_template_areas": null, "object_position": null, "object_fit": null, "grid_auto_columns": null, "margin": null, "display": null, "left": null}}, "0ce7164fc0c74bb9a2b5c7037375a727": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_view_name": "StyleView", "_model_name": "ProgressStyleModel", "description_width": "", "_view_module": "@jupyter-widgets/base", "_model_module_version": "1.5.0", "_view_count": null, "_view_module_version": "1.2.0", "bar_color": null, "_model_module": "@jupyter-widgets/controls"}}, "c4c4593c10904cb5b8a5724d60c7e181": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_view_name": "LayoutView", "grid_template_rows": null, "right": null, "justify_content": null, "_view_module": "@jupyter-widgets/base", "overflow": null, "_model_module_version": "1.2.0", "_view_count": null, "flex_flow": null, "width": null, "min_width": null, "border": null, "align_items": null, "bottom": null, "_model_module": "@jupyter-widgets/base", "top": null, "grid_column": null, "overflow_y": null, "overflow_x": null, "grid_auto_flow": null, "grid_area": null, "grid_template_columns": null, "flex": null, "_model_name": "LayoutModel", "justify_items": null, "grid_row": null, "max_height": null, "align_content": null, "visibility": null, "align_self": null, "height": null, "min_height": null, "padding": null, "grid_auto_rows": null, "grid_gap": null, "max_width": null, "order": null, "_view_module_version": "1.2.0", "grid_template_areas": null, "object_position": null, "object_fit": null, "grid_auto_columns": null, "margin": null, "display": null, "left": null}}, "473371611126476c88d5d42ec7031ed6": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_view_name": "StyleView", "_model_name": "DescriptionStyleModel", "description_width": "", "_view_module": "@jupyter-widgets/base", "_model_module_version": "1.5.0", "_view_count": null, "_view_module_version": "1.2.0", "_model_module": "@jupyter-widgets/controls"}}, "65efdfd0d26c46e79c8c5ff3b77126cc": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_view_name": "LayoutView", "grid_template_rows": null, "right": null, "justify_content": null, "_view_module": "@jupyter-widgets/base", "overflow": null, "_model_module_version": "1.2.0", "_view_count": null, "flex_flow": null, "width": null, "min_width": null, "border": null, "align_items": null, "bottom": null, "_model_module": "@jupyter-widgets/base", "top": null, "grid_column": null, "overflow_y": null, "overflow_x": null, "grid_auto_flow": null, "grid_area": null, "grid_template_columns": null, "flex": null, "_model_name": "LayoutModel", "justify_items": null, "grid_row": null, "max_height": null, "align_content": null, "visibility": null, "align_self": null, "height": null, "min_height": null, "padding": null, "grid_auto_rows": null, "grid_gap": null, "max_width": null, "order": null, "_view_module_version": "1.2.0", "grid_template_areas": null, "object_position": null, "object_fit": null, "grid_auto_columns": null, "margin": null, "display": null, "left": null}}}}}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/ultralytics/yolov5/blob/master/tutorial.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {"id": "t6MPjfT5NrKQ"}, "source": ["<a align=\"left\" href=\"https://ultralytics.com/yolov5\" target=\"_blank\">\n", "<img width=\"1024\", src=\"https://user-images.githubusercontent.com/26833433/125273437-35b3fc00-e30d-11eb-9079-46f313325424.png\"></a>\n", "\n", "This is the **official YOLOv5 🚀 notebook** by **Ultralytics**, and is freely available for redistribution under the [GPL-3.0 license](https://choosealicense.com/licenses/gpl-3.0/). \n", "For more information please visit https://github.com/ultralytics/yolov5 and https://ultralytics.com. Thank you!"]}, {"cell_type": "markdown", "metadata": {"id": "7mGmQbAO5pQb"}, "source": ["# Setup\n", "\n", "Clone repo, install dependencies and check PyTorch and GPU."]}, {"cell_type": "code", "metadata": {"id": "wbvMlHd_QwMG", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "3809e5a9-dd41-4577-fe62-5531abf7cca2"}, "source": ["!git clone https://github.com/ultralytics/yolov5  # clone\n", "%cd yolov5\n", "%pip install -qr requirements.txt  # install\n", "\n", "import torch\n", "from yolov5 import utils\n", "display = utils.notebook_init()  # checks"], "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["YOLOv5 🚀 v6.0-48-g84a8099 torch 1.10.0+cu102 CUDA:0 (Tesla V100-SXM2-16GB, 16160MiB)\n", "Setup complete ✅ (2 CPUs, 12.7 GB RAM, 42.2/166.8 GB disk)\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "4JnkELT0cIJg"}, "source": ["# 1. Inference\n", "\n", "`detect.py` runs YOLOv5 inference on a variety of sources, downloading models automatically from the [latest YOLOv5 release](https://github.com/ultralytics/yolov5/releases), and saving results to `runs/detect`. Example inference sources are:\n", "\n", "```shell\n", "python detect.py --source 0  # webcam\n", "                          img.jpg  # image \n", "                          vid.mp4  # video\n", "                          path/  # directory\n", "                          path/*.jpg  # glob\n", "                          'https://youtu.be/Zgi9g1ksQHc'  # YouTube\n", "                          'rtsp://example.com/media.mp4'  # RTSP, RTMP, HTTP stream\n", "```"]}, {"cell_type": "code", "metadata": {"id": "zR9ZbuQCH7FX", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "8f7e6588-215d-4ebd-93af-88b871e770a7"}, "source": ["!python detect.py --weights yolov5s.pt --img 640 --conf 0.25 --source data/images\n", "display.Image(filename='runs/detect/exp/zidane.jpg', width=600)"], "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1mdetect: \u001b[0mweights=['yolov5s.pt'], source=data/images, imgsz=[640, 640], conf_thres=0.25, iou_thres=0.45, max_det=1000, device=, view_img=False, save_txt=False, save_conf=False, save_crop=False, nosave=False, classes=None, agnostic_nms=False, augment=False, visualize=False, update=False, project=runs/detect, name=exp, exist_ok=False, line_thickness=3, hide_labels=False, hide_conf=False, half=False, dnn=False\n", "YOLOv5 🚀 v6.0-48-g84a8099 torch 1.10.0+cu102 CUDA:0 (Tesla V100-SXM2-16GB, 16160MiB)\n", "\n", "Fusing layers... \n", "Model Summary: 213 layers, 7225885 parameters, 0 gradients\n", "image 1/2 /content/yolov5/data/images/bus.jpg: 640x480 4 persons, 1 bus, Done. (0.007s)\n", "image 2/2 /content/yolov5/data/images/zidane.jpg: 384x640 2 persons, 1 tie, Done. (0.007s)\n", "Speed: 0.5ms pre-process, 6.9ms inference, 1.3ms NMS per image at shape (1, 3, 640, 640)\n", "Results saved to \u001b[1mruns/detect/exp\u001b[0m\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "hkAzDWJ7cWTr"}, "source": ["&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;\n", "<img align=\"left\" src=\"https://user-images.githubusercontent.com/26833433/127574988-6a558aa1-d268-44b9-bf6b-62d4c605cc72.jpg\" width=\"600\">"]}, {"cell_type": "markdown", "metadata": {"id": "0eq1SMWl6Sfn"}, "source": ["# 2. <PERSON><PERSON><PERSON>\n", "Validate a model's accuracy on [COCO](https://cocodataset.org/#home) val or test-dev datasets. Models are downloaded automatically from the [latest YOLOv5 release](https://github.com/ultralytics/yolov5/releases). To show results by class use the `--verbose` flag. Note that `pycocotools` metrics may be ~1% better than the equivalent repo metrics, as is visible below, due to slight differences in mAP computation."]}, {"cell_type": "markdown", "metadata": {"id": "eyTZYGgRjnMc"}, "source": ["## COCO val\n", "Download [COCO val 2017](https://github.com/ultralytics/yolov5/blob/74b34872fdf41941cddcf243951cdb090fbac17b/data/coco.yaml#L14) dataset (1GB - 5000 images), and test model accuracy."]}, {"cell_type": "code", "metadata": {"id": "WQPtK1QYVaD_", "colab": {"base_uri": "https://localhost:8080/", "height": 48, "referenced_widgets": ["eb95db7cae194218b3fcefb439b6352f", "769ecde6f2e64bacb596ce972f8d3d2d", "384a001876054c93b0af45cd1e960bfe", "dded0aeae74440f7ba2ffa0beb8dd612", "5296d28be75740b2892ae421bbec3657", "9f09facb2a6c4a7096810d327c8b551c", "25621cff5d16448cb7260e839fd0f543", "0ce7164fc0c74bb9a2b5c7037375a727", "c4c4593c10904cb5b8a5724d60c7e181", "473371611126476c88d5d42ec7031ed6", "65efdfd0d26c46e79c8c5ff3b77126cc"]}, "outputId": "bcf9a448-1f9b-4a41-ad49-12f181faf05a"}, "source": ["# Download COCO val\n", "torch.hub.download_url_to_file('https://ultralytics.com/assets/coco2017val.zip', 'tmp.zip')\n", "!unzip -q tmp.zip -d ../datasets && rm tmp.zip"], "execution_count": null, "outputs": [{"output_type": "display_data", "data": {"application/vnd.jupyter.widget-view+json": {"model_id": "eb95db7cae194218b3fcefb439b6352f", "version_minor": 0, "version_major": 2}, "text/plain": ["  0%|          | 0.00/780M [00:00<?, ?B/s]"]}, "metadata": {}}]}, {"cell_type": "code", "metadata": {"id": "X58w8JLpMnjH", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "74f1dfa9-6b6d-4b36-f67e-bbae243869f9"}, "source": ["# Run YOLOv5x on COCO val\n", "!python val.py --weights yolov5x.pt --data coco.yaml --img 640 --iou 0.65 --half"], "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1mval: \u001b[0mdata=/content/yolov5/data/coco.yaml, weights=['yolov5x.pt'], batch_size=32, imgsz=640, conf_thres=0.001, iou_thres=0.65, task=val, device=, single_cls=False, augment=False, verbose=False, save_txt=False, save_hybrid=False, save_conf=False, save_json=True, project=runs/val, name=exp, exist_ok=False, half=True\n", "YOLOv5 🚀 v6.0-48-g84a8099 torch 1.10.0+cu102 CUDA:0 (Tesla V100-SXM2-16GB, 16160MiB)\n", "\n", "Downloading https://github.com/ultralytics/yolov5/releases/download/v6.0/yolov5x.pt to yolov5x.pt...\n", "100% 166M/166M [00:03<00:00, 54.1MB/s]\n", "\n", "Fusing layers... \n", "Model Summary: 444 layers, 86705005 parameters, 0 gradients\n", "\u001b[34m\u001b[1mval: \u001b[0mScanning '../datasets/coco/val2017' images and labels...4952 found, 48 missing, 0 empty, 0 corrupted: 100% 5000/5000 [00:01<00:00, 2636.64it/s]\n", "\u001b[34m\u001b[1mval: \u001b[0mNew cache created: ../datasets/coco/val2017.cache\n", "               Class     Images     Labels          P          R     mAP@.5 mAP@.5:.95: 100% 157/157 [01:12<00:00,  2.17it/s]\n", "                 all       5000      36335      0.729       0.63      0.683      0.496\n", "Speed: 0.1ms pre-process, 4.9ms inference, 1.9ms NMS per image at shape (32, 3, 640, 640)\n", "\n", "Evaluating pycocotools mAP... saving runs/val/exp/yolov5x_predictions.json...\n", "loading annotations into memory...\n", "Done (t=0.46s)\n", "creating index...\n", "index created!\n", "Loading and preparing results...\n", "DONE (t=5.15s)\n", "creating index...\n", "index created!\n", "Running per image evaluation...\n", "Evaluate annotation type *bbox*\n", "DONE (t=90.39s).\n", "Accumulating evaluation results...\n", "DONE (t=14.54s).\n", " Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.507\n", " Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = 0.689\n", " Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = 0.552\n", " Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.345\n", " Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.559\n", " Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.652\n", " Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = 0.381\n", " Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = 0.630\n", " Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.682\n", " Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.526\n", " Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.732\n", " Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.829\n", "Results saved to \u001b[1mruns/val/exp\u001b[0m\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "rc_KbFk0juX2"}, "source": ["## COCO test\n", "Download [COCO test2017](https://github.com/ultralytics/yolov5/blob/74b34872fdf41941cddcf243951cdb090fbac17b/data/coco.yaml#L15) dataset (7GB - 40,000 images), to test model accuracy on test-dev set (**20,000 images, no labels**). Results are saved to a `*.json` file which should be **zipped** and submitted to the evaluation server at https://competitions.codalab.org/competitions/20794."]}, {"cell_type": "code", "metadata": {"id": "V0AJnSeCIHyJ"}, "source": ["# Download COCO test-dev2017\n", "torch.hub.download_url_to_file('https://ultralytics.com/assets/coco2017labels.zip', 'tmp.zip')\n", "!unzip -q tmp.zip -d ../datasets && rm tmp.zip\n", "!f=\"test2017.zip\" && curl http://images.cocodataset.org/zips/$f -o $f && unzip -q $f -d ../datasets/coco/images"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "29GJXAP_lPrt"}, "source": ["# Run YOLOv5x on COCO test\n", "!python val.py --weights yolov5x.pt --data coco.yaml --img 640 --iou 0.65 --half --task test"], "execution_count": null, "outputs": []}, {"cell_type": "markdown", "metadata": {"id": "ZY2VXXXu74w5"}, "source": ["# 3. <PERSON>\n", "\n", "<p align=\"\"><a href=\"https://roboflow.com/?ref=ultralytics\"><img width=\"1000\" src=\"https://uploads-ssl.webflow.com/5f6bc60e665f54545a1e52a5/615627e5824c9c6195abfda9_computer-vision-cycle.png\"/></a></p>\n", "Close the active learning loop by sampling images from your inference conditions with the `roboflow` pip package\n", "<br><br>\n", "\n", "Train a YOLOv5s model on the [COCO128](https://www.kaggle.com/ultralytics/coco128) dataset with `--data coco128.yaml`, starting from pretrained `--weights yolov5s.pt`, or from randomly initialized `--weights '' --cfg yolov5s.yaml`.\n", "\n", "- **Pretrained [Models](https://github.com/ultralytics/yolov5/tree/master/models)** are downloaded\n", "automatically from the [latest YOLOv5 release](https://github.com/ultralytics/yolov5/releases)\n", "- **[Datasets](https://github.com/ultralytics/yolov5/tree/master/data)** available for autodownload include: [COCO](https://github.com/ultralytics/yolov5/blob/master/data/coco.yaml), [COCO128](https://github.com/ultralytics/yolov5/blob/master/data/coco128.yaml), [VOC](https://github.com/ultralytics/yolov5/blob/master/data/VOC.yaml), [Argoverse](https://github.com/ultralytics/yolov5/blob/master/data/Argoverse.yaml), [VisDrone](https://github.com/ultralytics/yolov5/blob/master/data/VisDrone.yaml), [GlobalWheat](https://github.com/ultralytics/yolov5/blob/master/data/GlobalWheat2020.yaml), [xView](https://github.com/ultralytics/yolov5/blob/master/data/xView.yaml), [Objects365](https://github.com/ultralytics/yolov5/blob/master/data/Objects365.yaml), [SKU-110K](https://github.com/ultralytics/yolov5/blob/master/data/SKU-110K.yaml).\n", "- **Training Results** are saved to `runs/train/` with incrementing run directories, i.e. `runs/train/exp2`, `runs/train/exp3` etc.\n", "<br><br>\n", "\n", "## Train on Custom Data with Rob<PERSON>low 🌟 NEW\n", "\n", "[Robof<PERSON>](https://roboflow.com/?ref=ultralytics) enables you to easily **organize, label, and prepare** a high quality dataset with your own custom data. Roboflow also makes it easy to establish an active learning pipeline, collaborate with your team on dataset improvement, and integrate directly into your model building workflow with the `roboflow` pip package.\n", "\n", "- Custom Training Example: [https://blog.roboflow.com/how-to-train-yolov5-on-a-custom-dataset/](https://blog.roboflow.com/how-to-train-yolov5-on-a-custom-dataset/?ref=ultralytics)\n", "- Custom Training Notebook: [![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/roboflow-ai/yolov5-custom-training-tutorial/blob/main/yolov5-custom-training.ipynb)\n", "<br>\n", "\n", "<p align=\"\"><a href=\"https://roboflow.com/?ref=ultralytics\"><img width=\"480\" src=\"https://uploads-ssl.webflow.com/5f6bc60e665f54545a1e52a5/6152a275ad4b4ac20cd2e21a_roboflow-annotate.gif\"/></a></p>Label images lightning fast (including with model-assisted labeling)"]}, {"cell_type": "code", "metadata": {"id": "bOy5KI2ncnWd"}, "source": ["# Tensorboard  (optional)\n", "%load_ext tensorboard\n", "%tensorboard --logdir runs/train"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "2fLAV42oNb7M"}, "source": ["# Weights & Biases  (optional)\n", "%pip install -q wandb\n", "import wandb\n", "wandb.login()"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "1NcFxRcFdJ_O", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "8724d13d-6711-4a12-d96a-1c655e5c3549"}, "source": ["# Train YOLOv5s on COCO128 for 3 epochs\n", "!python train.py --img 640 --batch 16 --epochs 3 --data coco128.yaml --weights yolov5s.pt --cache"], "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1mtrain: \u001b[0mweights=yolov5s.pt, cfg=, data=coco128.yaml, hyp=data/hyps/hyp.scratch-low.yaml, epochs=3, batch_size=16, imgsz=640, rect=False, resume=False, nosave=False, noval=False, noautoanchor=False, evolve=None, bucket=, cache=ram, image_weights=False, device=, multi_scale=False, single_cls=False, adam=False, sync_bn=False, workers=8, project=runs/train, name=exp, exist_ok=False, quad=False, linear_lr=False, label_smoothing=0.0, patience=100, freeze=0, save_period=-1, local_rank=-1, entity=None, upload_dataset=False, bbox_interval=-1, artifact_alias=latest\n", "\u001b[34m\u001b[1mgithub: \u001b[0mup to date with https://github.com/ultralytics/yolov5 ✅\n", "YOLOv5 🚀 v6.0-48-g84a8099 torch 1.10.0+cu102 CUDA:0 (Tesla V100-SXM2-16GB, 16160MiB)\n", "\n", "\u001b[34m\u001b[1mhyperparameters: \u001b[0mlr0=0.01, lrf=0.1, momentum=0.937, weight_decay=0.0005, warmup_epochs=3.0, warmup_momentum=0.8, warmup_bias_lr=0.1, box=0.05, cls=0.5, cls_pw=1.0, obj=1.0, obj_pw=1.0, iou_t=0.2, anchor_t=4.0, fl_gamma=0.0, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, degrees=0.0, translate=0.1, scale=0.5, shear=0.0, perspective=0.0, flipud=0.0, fliplr=0.5, mosaic=1.0, mixup=0.0, copy_paste=0.0\n", "\u001b[34m\u001b[1mWeights & Biases: \u001b[0mrun 'pip install wandb' to automatically track and visualize YOLOv5 🚀 runs (RECOMMENDED)\n", "\u001b[34m\u001b[1mTensorBoard: \u001b[0mStart with 'tensorboard --logdir runs/train', view at http://localhost:6006/\n", "\n", "                 from  n    params  module                                  arguments                     \n", "  0                -1  1      3520  models.common.Conv                      [3, 32, 6, 2, 2]              \n", "  1                -1  1     18560  models.common.Conv                      [32, 64, 3, 2]                \n", "  2                -1  1     18816  models.common.C3                        [64, 64, 1]                   \n", "  3                -1  1     73984  models.common.Conv                      [64, 128, 3, 2]               \n", "  4                -1  2    115712  models.common.C3                        [128, 128, 2]                 \n", "  5                -1  1    295424  models.common.Conv                      [128, 256, 3, 2]              \n", "  6                -1  3    625152  models.common.C3                        [256, 256, 3]                 \n", "  7                -1  1   1180672  models.common.Conv                      [256, 512, 3, 2]              \n", "  8                -1  1   1182720  models.common.C3                        [512, 512, 1]                 \n", "  9                -1  1    656896  models.common.SPPF                      [512, 512, 5]                 \n", " 10                -1  1    131584  models.common.Conv                      [512, 256, 1, 1]              \n", " 11                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          \n", " 12           [-1, 6]  1         0  models.common.Concat                    [1]                           \n", " 13                -1  1    361984  models.common.C3                        [512, 256, 1, False]          \n", " 14                -1  1     33024  models.common.Conv                      [256, 128, 1, 1]              \n", " 15                -1  1         0  torch.nn.modules.upsampling.Upsample    [None, 2, 'nearest']          \n", " 16           [-1, 4]  1         0  models.common.Concat                    [1]                           \n", " 17                -1  1     90880  models.common.C3                        [256, 128, 1, False]          \n", " 18                -1  1    147712  models.common.Conv                      [128, 128, 3, 2]              \n", " 19          [-1, 14]  1         0  models.common.Concat                    [1]                           \n", " 20                -1  1    296448  models.common.C3                        [256, 256, 1, False]          \n", " 21                -1  1    590336  models.common.Conv                      [256, 256, 3, 2]              \n", " 22          [-1, 10]  1         0  models.common.Concat                    [1]                           \n", " 23                -1  1   1182720  models.common.C3                        [512, 512, 1, False]          \n", " 24      [17, 20, 23]  1    229245  models.yolo.Detect                      [80, [[10, 13, 16, 30, 33, 23], [30, 61, 62, 45, 59, 119], [116, 90, 156, 198, 373, 326]], [128, 256, 512]]\n", "Model Summary: 270 layers, 7235389 parameters, 7235389 gradients, 16.5 GFLOPs\n", "\n", "Transferred 349/349 items from yolov5s.pt\n", "Scaled weight_decay = 0.0005\n", "\u001b[34m\u001b[1moptimizer:\u001b[0m SGD with parameter groups 57 weight, 60 weight (no decay), 60 bias\n", "\u001b[34m\u001b[1malbumentations: \u001b[0mversion 1.0.3 required by YOLOv5, but version 0.1.12 is currently installed\n", "\u001b[34m\u001b[1mtrain: \u001b[0mScanning '../datasets/coco128/labels/train2017.cache' images and labels... 128 found, 0 missing, 2 empty, 0 corrupted: 100% 128/128 [00:00<?, ?it/s]\n", "\u001b[34m\u001b[1mtrain: \u001b[0mCaching images (0.1GB ram): 100% 128/128 [00:00<00:00, 296.04it/s]\n", "\u001b[34m\u001b[1mval: \u001b[0mScanning '../datasets/coco128/labels/train2017.cache' images and labels... 128 found, 0 missing, 2 empty, 0 corrupted: 100% 128/128 [00:00<?, ?it/s]\n", "\u001b[34m\u001b[1mval: \u001b[0mCaching images (0.1GB ram): 100% 128/128 [00:01<00:00, 121.58it/s]\n", "Plotting labels... \n", "\n", "\u001b[34m\u001b[1mAutoAnchor: \u001b[0mAnalyzing anchors... anchors/target = 4.27, Best Possible Recall (BPR) = 0.9935\n", "Image sizes 640 train, 640 val\n", "Using 2 dataloader workers\n", "Logging results to \u001b[1mruns/train/exp\u001b[0m\n", "Starting training for 3 epochs...\n", "\n", "     Epoch   gpu_mem       box       obj       cls    labels  img_size\n", "       0/2     3.62G   0.04621    0.0711   0.02112       203       640: 100% 8/8 [00:04<00:00,  1.99it/s]\n", "               Class     Images     Labels          P          R     mAP@.5 mAP@.5:.95: 100% 4/4 [00:00<00:00,  4.37it/s]\n", "                 all        128        929      0.655      0.547      0.622       0.41\n", "\n", "     Epoch   gpu_mem       box       obj       cls    labels  img_size\n", "       1/2     5.31G   0.04564   0.06898   0.02116       143       640: 100% 8/8 [00:01<00:00,  4.77it/s]\n", "               Class     Images     Labels          P          R     mAP@.5 mAP@.5:.95: 100% 4/4 [00:00<00:00,  4.27it/s]\n", "                 all        128        929       0.68      0.554      0.632      0.419\n", "\n", "     Epoch   gpu_mem       box       obj       cls    labels  img_size\n", "       2/2     5.31G   0.04487   0.06883   0.01998       253       640: 100% 8/8 [00:01<00:00,  4.91it/s]\n", "               Class     Images     Labels          P          R     mAP@.5 mAP@.5:.95: 100% 4/4 [00:00<00:00,  4.30it/s]\n", "                 all        128        929       0.71      0.544      0.629      0.423\n", "\n", "3 epochs completed in 0.003 hours.\n", "Optimizer stripped from runs/train/exp/weights/last.pt, 14.9MB\n", "Optimizer stripped from runs/train/exp/weights/best.pt, 14.9MB\n", "\n", "Validating runs/train/exp/weights/best.pt...\n", "Fusing layers... \n", "Model Summary: 213 layers, 7225885 parameters, 0 gradients, 16.5 GFLOPs\n", "               Class     Images     Labels          P          R     mAP@.5 mAP@.5:.95: 100% 4/4 [00:03<00:00,  1.04it/s]\n", "                 all        128        929       0.71      0.544       0.63      0.423\n", "              person        128        254      0.816      0.669      0.774      0.507\n", "             bicycle        128          6      0.799      0.667      0.614      0.371\n", "                 car        128         46      0.803      0.355      0.486      0.209\n", "          motorcycle        128          5      0.704        0.6      0.791      0.583\n", "            airplane        128          6          1      0.795      0.995      0.717\n", "                 bus        128          7      0.656      0.714       0.72      0.606\n", "               train        128          3      0.852          1      0.995      0.682\n", "               truck        128         12      0.521       0.25      0.395      0.215\n", "                boat        128          6      0.795      0.333      0.445      0.137\n", "       traffic light        128         14      0.576      0.143       0.24      0.161\n", "           stop sign        128          2      0.636        0.5      0.828      0.713\n", "               bench        128          9      0.972      0.444      0.575       0.25\n", "                bird        128         16      0.939      0.968      0.988      0.645\n", "                 cat        128          4      0.984       0.75      0.822      0.694\n", "                 dog        128          9      0.888      0.667      0.903       0.54\n", "               horse        128          2      0.689          1      0.995      0.697\n", "            elephant        128         17       0.96      0.882      0.943      0.681\n", "                bear        128          1      0.549          1      0.995      0.995\n", "               zebra        128          4       0.86          1      0.995      0.952\n", "             giraffe        128          9      0.822      0.778      0.905       0.57\n", "            backpack        128          6          1      0.309      0.457      0.195\n", "            umbrella        128         18      0.775      0.576       0.74      0.418\n", "             handbag        128         19      0.628      0.105      0.167      0.111\n", "                 tie        128          7       0.96      0.571      0.701      0.441\n", "            suitcase        128          4          1      0.895      0.995      0.621\n", "             frisbee        128          5      0.641        0.8      0.798      0.664\n", "                skis        128          1      0.627          1      0.995      0.497\n", "           snowboard        128          7      0.988      0.714      0.768      0.556\n", "         sports ball        128          6      0.671        0.5      0.579      0.339\n", "                kite        128         10      0.631      0.515      0.598      0.221\n", "        baseball bat        128          4       0.47      0.456      0.277      0.137\n", "      baseball glove        128          7      0.459      0.429      0.334      0.182\n", "          skateboard        128          5        0.7       0.48      0.736      0.548\n", "       tennis racket        128          7      0.559      0.571      0.538      0.315\n", "              bottle        128         18      0.607      0.389      0.484      0.282\n", "          wine glass        128         16      0.722      0.812       0.82      0.385\n", "                 cup        128         36      0.881      0.361      0.532      0.312\n", "                fork        128          6      0.384      0.167      0.239      0.191\n", "               knife        128         16      0.908      0.616      0.681      0.443\n", "               spoon        128         22      0.836      0.364      0.536      0.264\n", "                bowl        128         28      0.793      0.536      0.633      0.471\n", "              banana        128          1          0          0      0.142     0.0995\n", "            sandwich        128          2          0          0     0.0951     0.0717\n", "              orange        128          4          1          0       0.67      0.317\n", "            broccoli        128         11      0.345      0.182      0.283      0.243\n", "              carrot        128         24      0.688      0.459      0.612      0.402\n", "             hot dog        128          2      0.424      0.771      0.497      0.473\n", "               pizza        128          5      0.622          1      0.824      0.551\n", "               donut        128         14      0.703          1      0.952      0.853\n", "                cake        128          4      0.733          1      0.945      0.777\n", "               chair        128         35      0.512      0.486      0.488      0.222\n", "               couch        128          6       0.68       0.36      0.746      0.406\n", "        potted plant        128         14      0.797      0.714      0.808      0.482\n", "                 bed        128          3          1          0      0.474      0.318\n", "        dining table        128         13      0.852      0.445      0.478      0.315\n", "              toilet        128          2      0.512        0.5      0.554      0.487\n", "                  tv        128          2      0.754          1      0.995      0.895\n", "              laptop        128          3          1          0       0.39      0.147\n", "               mouse        128          2          1          0     0.0283     0.0226\n", "              remote        128          8      0.747      0.625      0.636      0.488\n", "          cell phone        128          8      0.555      0.166      0.417      0.222\n", "           microwave        128          3      0.417          1      0.995      0.732\n", "                oven        128          5       0.37        0.4      0.432      0.249\n", "                sink        128          6      0.356      0.167      0.269      0.149\n", "        refrigerator        128          5      0.705        0.8      0.814       0.45\n", "                book        128         29      0.628      0.138      0.298      0.136\n", "               clock        128          9      0.857      0.778      0.893      0.574\n", "                vase        128          2      0.242          1      0.663      0.622\n", "            scissors        128          1          1          0     0.0207    0.00207\n", "          teddy bear        128         21      0.847      0.381      0.622      0.345\n", "          toothbrush        128          5       0.99        0.6      0.662       0.45\n", "Results saved to \u001b[1mruns/train/exp\u001b[0m\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "15glLzbQx5u0"}, "source": ["# 4. Visualize"]}, {"cell_type": "markdown", "metadata": {"id": "DLI1JmHU7B0l"}, "source": ["## Weights & Biases Logging 🌟 NEW\n", "\n", "[Weights & Biases](https://wandb.ai/site?utm_campaign=repo_yolo_notebook) (W&B) is now integrated with YOLOv5 for real-time visualization and cloud logging of training runs. This allows for better run comparison and introspection, as well improved visibility and collaboration for teams. To enable W&B `pip install wandb`, and then train normally (you will be guided through setup on first use). \n", "\n", "During training you will see live updates at [https://wandb.ai/home](https://wandb.ai/home?utm_campaign=repo_yolo_notebook), and you can create and share detailed [Reports](https://wandb.ai/glenn-jocher/yolov5_tutorial/reports/YOLOv5-COCO128-Tutorial-Results--VmlldzozMDI5OTY) of your results. For more information see the [YOLOv5 Weights & Biases Tutorial](https://github.com/ultralytics/yolov5/issues/1289). \n", "\n", "<p align=\"left\"><img width=\"900\" alt=\"Weights & Biases dashboard\" src=\"https://user-images.githubusercontent.com/26833433/135390767-c28b050f-8455-4004-adb0-3b730386e2b2.png\"></p>"]}, {"cell_type": "markdown", "metadata": {"id": "-WPvRbS5Swl6"}, "source": ["## Local Logging\n", "\n", "All results are logged by default to `runs/train`, with a new experiment directory created for each new training as `runs/train/exp2`, `runs/train/exp3`, etc. View train and val jpgs to see mosaics, labels, predictions and augmentation effects. Note an Ultralytics **Mosaic Dataloader** is used for training (shown below), which combines 4 images into 1 mosaic during training.\n", "\n", "> <img src=\"https://user-images.githubusercontent.com/26833433/131255960-b536647f-7c61-4f60-bbc5-cb2544d71b2a.jpg\" width=\"700\">  \n", "`train_batch0.jpg` shows train batch 0 mosaics and labels\n", "\n", "> <img src=\"https://user-images.githubusercontent.com/26833433/131256748-603cafc7-55d1-4e58-ab26-83657761aed9.jpg\" width=\"700\">  \n", "`test_batch0_labels.jpg` shows val batch 0 labels\n", "\n", "> <img src=\"https://user-images.githubusercontent.com/26833433/131256752-3f25d7a5-7b0f-4bb3-ab78-46343c3800fe.jpg\" width=\"700\">  \n", "`test_batch0_pred.jpg` shows val batch 0 _predictions_\n", "\n", "Training results are automatically logged to [Tensorboard](https://www.tensorflow.org/tensorboard) and [CSV](https://github.com/ultralytics/yolov5/pull/4148) as `results.csv`, which is plotted as `results.png` (below) after training completes. You can also plot any `results.csv` file manually:\n", "\n", "```python\n", "from utils.plots import plot_results \n", "plot_results('path/to/results.csv')  # plot 'results.csv' as 'results.png'\n", "```\n", "\n", "<img align=\"left\" width=\"800\" alt=\"COCO128 Training Results\" src=\"https://user-images.githubusercontent.com/26833433/126906780-8c5e2990-6116-4de6-b78a-367244a33ccf.png\">"]}, {"cell_type": "markdown", "metadata": {"id": "Zelyeqbyt3GD"}, "source": ["# Environments\n", "\n", "YOLOv5 may be run in any of the following up-to-date verified environments (with all dependencies including [CUDA](https://developer.nvidia.com/cuda)/[CUDNN](https://developer.nvidia.com/cudnn), [Python](https://www.python.org/) and [PyTorch](https://pytorch.org/) preinstalled):\n", "\n", "- **Google Colab and Kaggle** notebooks with free GPU: <a href=\"https://colab.research.google.com/github/ultralytics/yolov5/blob/master/tutorial.ipynb\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"></a> <a href=\"https://www.kaggle.com/ultralytics/yolov5\"><img src=\"https://kaggle.com/static/images/open-in-kaggle.svg\" alt=\"Open In Kaggle\"></a>\n", "- **Google Cloud** Deep Learning VM. See [GCP Quickstart Guide](https://github.com/ultralytics/yolov5/wiki/GCP-Quickstart)\n", "- **Amazon** Deep Learning AMI. See [AWS Quickstart Guide](https://github.com/ultralytics/yolov5/wiki/AWS-Quickstart)\n", "- **Docker Image**. See [Docker Quickstart Guide](https://github.com/ultralytics/yolov5/wiki/Docker-Quickstart) <a href=\"https://hub.docker.com/r/ultralytics/yolov5\"><img src=\"https://img.shields.io/docker/pulls/ultralytics/yolov5?logo=docker\" alt=\"Docker Pulls\"></a>\n"]}, {"cell_type": "markdown", "metadata": {"id": "6Qu7Iesl0p54"}, "source": ["# Status\n", "\n", "![CI CPU testing](https://github.com/ultralytics/yolov5/workflows/CI%20CPU%20testing/badge.svg)\n", "\n", "If this badge is green, all [YOLOv5 GitHub Actions](https://github.com/ultralytics/yolov5/actions) Continuous Integration (CI) tests are currently passing. CI tests verify correct operation of YOLOv5 training ([train.py](https://github.com/ultralytics/yolov5/blob/master/train.py)), testing ([val.py](https://github.com/ultralytics/yolov5/blob/master/val.py)), inference ([detect.py](https://github.com/ultralytics/yolov5/blob/master/detect.py)) and export ([export.py](https://github.com/ultralytics/yolov5/blob/master/export.py)) on macOS, Windows, and Ubuntu every 24 hours and on every commit.\n"]}, {"cell_type": "markdown", "metadata": {"id": "IEijrePND_2I"}, "source": ["# Appendix\n", "\n", "Optional extras below. Unit tests validate repo functionality and should be run on any PRs submitted.\n"]}, {"cell_type": "code", "metadata": {"id": "mcKoSIK2WSzj"}, "source": ["# Reproduce\n", "for x in 'yolov5n', 'yolov5s', 'yolov5m', 'yolov5l', 'yolov5x':\n", "  !python val.py --weights {x}.pt --data coco.yaml --img 640 --task speed  # speed\n", "  !python val.py --weights {x}.pt --data coco.yaml --img 640 --conf 0.001 --iou 0.65  # mAP"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "GMusP4OAxFu6"}, "source": ["# PyTorch Hub\n", "import torch\n", "\n", "# Model\n", "model = torch.hub.load('ultralytics/yolov5', 'yolov5s')\n", "\n", "# Images\n", "dir = 'https://ultralytics.com/images/'\n", "imgs = [dir + f for f in ('zidane.jpg', 'bus.jpg')]  # batch of images\n", "\n", "# Inference\n", "results = model(imgs)\n", "results.print()  # or .show(), .save()"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "FGH0ZjkGjejy"}, "source": ["# CI Checks\n", "%%shell\n", "export PYTHONPATH=\"$PWD\"  # to run *.py. files in subdirectories\n", "rm -rf runs  # remove runs/\n", "for m in yolov5n; do  # models\n", "  python train.py --img 64 --batch 32 --weights $m.pt --epochs 1 --device 0  # train pretrained\n", "  python train.py --img 64 --batch 32 --weights '' --cfg $m.yaml --epochs 1 --device 0  # train scratch\n", "  for d in 0 cpu; do  # devices\n", "    python val.py --weights $m.pt --device $d # val official\n", "    python val.py --weights runs/train/exp/weights/best.pt --device $d # val custom\n", "    python detect.py --weights $m.pt --device $d  # detect official\n", "    python detect.py --weights runs/train/exp/weights/best.pt --device $d  # detect custom\n", "  done\n", "  python hubconf.py  # hub\n", "  python models/yolo.py --cfg $m.yaml  # build PyTorch model\n", "  python models/tf.py --weights $m.pt  # build TensorFlow model\n", "  python export.py --img 64 --batch 1 --weights $m.pt --include torchscript onnx  # export\n", "done"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "gogI-kwi3Tye"}, "source": ["# Profile\n", "from utils.torch_utils import profile\n", "\n", "m1 = lambda x: x * torch.sigmoid(x)\n", "m2 = torch.nn.SiLU()\n", "results = profile(input=torch.randn(16, 3, 640, 640), ops=[m1, m2], n=100)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "RVRSOhEvUdb5"}, "source": ["# Evolve\n", "!python train.py --img 640 --batch 64 --epochs 100 --data coco128.yaml --weights yolov5s.pt --cache --noautoanchor --evolve\n", "!d=runs/train/evolve && cp evolve.* $d && zip -r evolve.zip $d && gsutil mv evolve.zip gs://bucket  # upload results (optional)"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "BSgFCAcMbk1R"}, "source": ["# VOC\n", "for b, m in zip([64, 64, 64, 32, 16], ['yolov5n', 'yolov5s', 'yolov5m', 'yolov5l', 'yolov5x']):  # batch, model\n", "  !python train.py --batch {b} --weights {m}.pt --data VOC.yaml --epochs 50 --img 512 --hyp hyp.VOC.yaml --project VOC --name {m} --cache"], "execution_count": null, "outputs": []}, {"cell_type": "code", "metadata": {"id": "VTRwsvA9u7ln"}, "source": ["# TensorRT \n", "# https://docs.nvidia.com/deeplearning/tensorrt/install-guide/index.html#installing-pip\n", "!pip install -U nvidia-tensorrt --index-url https://pypi.ngc.nvidia.com  # install\n", "!python export.py --weights yolov5s.pt --include engine --imgsz 640 640 --device 0  # export\n", "!python detect.py --weights yolov5s.engine --imgsz 640 640 --device 0  # inference"], "execution_count": null, "outputs": []}]}