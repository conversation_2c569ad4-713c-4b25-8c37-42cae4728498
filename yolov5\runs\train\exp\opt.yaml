weights: yolov5\yolov5s.pt
cfg: ''
data: yolov5\data\my_worker.yaml
hyp: yolov5\data\hyps\hyp.scratch-low.yaml
epochs: 100
batch_size: 16
imgsz: 640
rect: false
resume: false
nosave: false
noval: false
noautoanchor: false
evolve: null
bucket: ''
cache: null
image_weights: false
device: ''
multi_scale: false
single_cls: false
optimizer: SGD
sync_bn: false
workers: 8
project: yolov5\runs\train
name: exp
exist_ok: false
quad: false
cos_lr: false
label_smoothing: 0.0
patience: 100
freeze:
- 0
save_period: -1
local_rank: -1
entity: null
upload_dataset: false
bbox_interval: -1
artifact_alias: latest
save_dir: yolov5\runs\train\exp
