.. _torchreid_models:

torchreid.models
=================

Interface
---------

.. automodule:: torchreid.models.__init__
    :members:


ImageNet Classification Models
-------------------------------

.. autoclass:: torchreid.models.resnet.ResNet
.. autoclass:: torchreid.models.senet.SENet
.. autoclass:: torchreid.models.densenet.DenseNet
.. autoclass:: torchreid.models.inceptionresnetv2.InceptionResNetV2
.. autoclass:: torchreid.models.inceptionv4.InceptionV4
.. autoclass:: torchreid.models.xception.Xception


Lightweight Models
------------------

.. autoclass:: torchreid.models.nasnet.NASNetAMobile
.. autoclass:: torchreid.models.mobilenetv2.MobileNetV2
.. autoclass:: torchreid.models.shufflenet.ShuffleNet
.. autoclass:: torchreid.models.squeezenet.SqueezeNet
.. autoclass:: torchreid.models.shufflenetv2.ShuffleNetV2


ReID-specific Models
--------------------

.. autoclass:: torchreid.models.mudeep.MuDeep
.. autoclass:: torchreid.models.resnetmid.ResNetMid
.. autoclass:: torchreid.models.hacnn.HACNN
.. autoclass:: torchreid.models.pcb.PCB
.. autoclass:: torchreid.models.mlfn.MLFN
.. autoclass:: torchreid.models.osnet.OSNet
.. autoclass:: torchreid.models.osnet_ain.OSNet