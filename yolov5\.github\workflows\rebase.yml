# https://github.com/marketplace/actions/automatic-rebase

name: Automatic Rebase
on:
  issue_comment:
    types: [created]
jobs:
  rebase:
    name: Rebase
    if: github.event.issue.pull_request != '' && contains(github.event.comment.body, '/rebase')
    runs-on: ubuntu-latest
    steps:
      - name: Checkout the latest code
        uses: actions/checkout@v3
        with:
          token: ${{ secrets.ACTIONS_TOKEN }}
          fetch-depth: 0 # otherwise, you will fail to push refs to dest repo
      - name: Automatic Rebase
        uses: cirrus-actions/rebase@1.5
        env:
          GITHUB_TOKEN: ${{ secrets.ACTIONS_TOKEN }}
