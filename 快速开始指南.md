# 🚀 建筑工地工人行为分析系统 - 快速开始指南

## 📦 一键安装

### 1. 环境准备
```bash
# 创建conda环境
conda create -n yolo python=3.8
conda activate yolo

# 安装依赖
pip install torch torchvision opencv-python matplotlib seaborn torch-pruning
```

### 2. 模型准备
确保以下文件存在：
- `yolov5/best_22.pt` - 训练好的YOLOv5模型
- `deep_sort/deep_sort/deep/checkpoint/osnet_x0_25_msmt17.pth` - DeepSORT特征提取模型

## ⚡ 三步快速使用

### 步骤1: 运行检测和跟踪
```bash
python track.py --source 测试基准视频.mp4 --weights yolov5/best_22.pt --conf-thres 0.6 --iou-thres 0.45
```

### 步骤2: 生成行为分析报告
```bash
python worker_analysis.py --tracks-file runs/track/exp/tracks.txt --video-file 测试基准视频.mp4
```

### 步骤3: 查看结果
打开 `runs/track/exp/analysis/worker_report.html` 查看详细分析报告

## 🔧 模型优化 (可选)

### 推荐：使用优化后的模型
```bash
# 1. 生成优化模型 (只需运行一次)
python optimized_sparse_prune.py --weights yolov5/best_22.pt --percent 0.15 --save-path optimized_yolo.pt --benchmark --optimize

# 2. 使用优化模型进行检测
python track.py --source 测试基准视频.mp4 --weights optimized_yolo.pt --conf-thres 0.6 --iou-thres 0.45
```

**性能提升**: 推理速度提升65.6%，推理时间减少39.6%

## 📊 核心功能一览

### ✅ 实时检测与跟踪
- 高精度工人检测 (mAP@0.5 = 0.706)
- 稳定的多目标跟踪 (ID切换率 < 5%)
- 解决ID跳动和重叠检测问题

### ✅ 行为分析
- 工人活动时间统计
- 移动轨迹分析
- 活动区域热力图
- 工人数量变化趋势

### ✅ 可视化报告
- 详细HTML分析报告
- 统计图表和热力图
- JSON格式数据导出

## 🎯 最佳参数配置

基于大量实验，推荐使用以下参数：

```bash
python track.py \
    --source 测试基准视频.mp4 \
    --weights optimized_yolo.pt \
    --conf-thres 0.6 \              # 解决ID跳动问题
    --iou-thres 0.45 \              # 解决重叠检测问题
    --device cpu \                  # 或使用 cuda:0
    --save-txt \
    --save-vid
```

## 📁 输出文件说明

运行完成后，在 `runs/track/exp/` 目录下会生成：

```
runs/track/exp/
├── tracks.txt                    # 跟踪数据 (每行: frame_id, track_id, x, y, w, h)
├── 测试基准视频.mp4               # 标注后的视频
└── analysis/                     # 分析结果目录
    ├── worker_stats.json         # 详细统计数据
    ├── worker_report.html         # HTML分析报告 ⭐
    ├── worker_count_over_time.png # 工人数量变化图
    └── worker_heatmap.png         # 活动热力图
```

## 🔧 常见问题快速解决

### Q1: 检测不到工人
**解决**: 降低置信度阈值
```bash
python track.py --conf-thres 0.3
```

### Q2: 工人ID频繁跳动
**解决**: 提高置信度阈值 (已在推荐参数中)
```bash
python track.py --conf-thres 0.6
```

### Q3: 工人重叠时识别框合并
**解决**: 降低IoU阈值 (已在推荐参数中)
```bash
python track.py --iou-thres 0.45
```

### Q4: 内存不足
**解决**: 使用CPU或减小图像尺寸
```bash
python track.py --device cpu --imgsz 416
```

### Q5: HTML报告打不开
**解决**: 使用Chrome或Firefox浏览器打开

## 📈 性能对比

| 配置 | 推理速度 | 推理时间 | 模型大小 |
|------|----------|----------|----------|
| 原始模型 | 11.89 FPS | 84.11 ms | 28.2 MB |
| 优化模型 | 19.69 FPS | 50.79 ms | 27.45 MB |
| **提升** | **+65.6%** | **-39.6%** | **-2.7%** |

## 🎯 使用场景

### 1. 建筑工地安全监控
```bash
# 实时监控
python track.py --source 0 --weights optimized_yolo.pt --view-img

# 录像分析
python track.py --source 工地监控录像.mp4 --weights optimized_yolo.pt
```

### 2. 工人效率分析
```bash
# 生成详细报告
python worker_analysis.py --tracks-file runs/track/exp/tracks.txt --video-file 工地监控录像.mp4
```

### 3. 批量视频处理
```bash
# 处理多个视频
for video in *.mp4; do
    python track.py --source "$video" --weights optimized_yolo.pt
    python worker_analysis.py --tracks-file runs/track/exp/tracks.txt --video-file "$video"
done
```

## 🛠️ 高级功能

### 模型性能测试
```bash
# 比较原始模型和优化模型
python test_pruned_model.py --pruned-weights optimized_yolo.pt --original-weights yolov5/best_22.pt --compare
```

### 重新生成报告
```bash
# 基于现有跟踪数据重新生成报告
python regenerate_report.py --tracks-file runs/track/exp/tracks.txt --video-file 测试基准视频.mp4
```

### 不同剪枝策略
```bash
# 保守剪枝 (精度优先)
python optimized_sparse_prune.py --weights yolov5/best_22.pt --percent 0.1 --strategy conservative

# 激进剪枝 (速度优先)
python optimized_sparse_prune.py --weights yolov5/best_22.pt --percent 0.2 --strategy aggressive
```

## 📞 获取帮助

- 📖 **详细文档**: 查看 `技术使用手册.md`
- 📊 **项目总结**: 查看 `项目总结报告.md`
- 🔧 **完整说明**: 查看 `PROJECT_README.md`

---

**🎉 恭喜！您已经掌握了建筑工地工人行为分析系统的基本使用方法！**

**💡 提示**: 首次使用建议先用小视频文件测试，确认系统正常工作后再处理大文件。
