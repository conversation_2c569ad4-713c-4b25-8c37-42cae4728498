# YOLO模型剪枝实验报告

**实验ID**: final_pruning_20250604_215556  
**实验时间**: 2025-06-04T21:55:56.445566 - 2025-06-04T21:56:45.175254  
**原始模型**: yolov5/best_22.pt  
**测试设备**: cpu  

## 📊 实验结果汇总

| 剪枝比例 | 实际比例 | FPS变化 | 时间变化 | 检测变化 | 模型路径 |
|---------|---------|---------|----------|----------|----------|
| 10.0% | 10.0% | -15.4% | +18.1% | -4.5 | pruned_yolo_10percent.pt |
| 15.0% | 15.0% | +26.3% | -20.8% | -4.5 | pruned_yolo_15percent.pt |
| 20.0% | 20.0% | +1.8% | -1.8% | -4.5 | pruned_yolo_20percent.pt |
| 25.0% | 25.0% | +3.7% | -3.6% | -4.5 | pruned_yolo_25percent.pt |
| 30.0% | 30.0% | -3.7% | +3.8% | -4.5 | pruned_yolo_30percent.pt |

## 📈 详细性能分析

### 1. 剪枝比例 10.0%

**剪枝信息**:
- 目标比例: 10.0%
- 实际比例: 10.0%
- 剪枝阈值: 0.629395
- 剪枝通道: 955 / 9,504

**推理性能对比**:
- 原始FPS: 16.55
- 剪枝后FPS: 14.01
- FPS变化: -15.4%
- 原始推理时间: 60.41ms
- 剪枝后推理时间: 71.37ms
- 时间变化: +18.1%

**检测能力对比**:
- 原始检测: 4.53 个工人/图像
- 剪枝后检测: 0.00 个工人/图像
- 检测变化: -4.53 个工人/图像

### 2. 剪枝比例 15.0%

**剪枝信息**:
- 目标比例: 15.0%
- 实际比例: 15.0%
- 剪枝阈值: 0.703125
- 剪枝通道: 1,429 / 9,504

**推理性能对比**:
- 原始FPS: 15.52
- 剪枝后FPS: 19.60
- FPS变化: +26.3%
- 原始推理时间: 64.44ms
- 剪枝后推理时间: 51.02ms
- 时间变化: -20.8%

**检测能力对比**:
- 原始检测: 4.53 个工人/图像
- 剪枝后检测: 0.00 个工人/图像
- 检测变化: -4.53 个工人/图像

### 3. 剪枝比例 20.0%

**剪枝信息**:
- 目标比例: 20.0%
- 实际比例: 20.0%
- 剪枝阈值: 0.757324
- 剪枝通道: 1,901 / 9,504

**推理性能对比**:
- 原始FPS: 16.43
- 剪枝后FPS: 16.73
- FPS变化: +1.8%
- 原始推理时间: 60.86ms
- 剪枝后推理时间: 59.77ms
- 时间变化: -1.8%

**检测能力对比**:
- 原始检测: 4.53 个工人/图像
- 剪枝后检测: 0.00 个工人/图像
- 检测变化: -4.53 个工人/图像

### 4. 剪枝比例 25.0%

**剪枝信息**:
- 目标比例: 25.0%
- 实际比例: 25.0%
- 剪枝阈值: 0.806152
- 剪枝通道: 2,380 / 9,504

**推理性能对比**:
- 原始FPS: 16.29
- 剪枝后FPS: 16.89
- FPS变化: +3.7%
- 原始推理时间: 61.38ms
- 剪枝后推理时间: 59.19ms
- 时间变化: -3.6%

**检测能力对比**:
- 原始检测: 4.53 个工人/图像
- 剪枝后检测: 0.00 个工人/图像
- 检测变化: -4.53 个工人/图像

### 5. 剪枝比例 30.0%

**剪枝信息**:
- 目标比例: 30.0%
- 实际比例: 30.0%
- 剪枝阈值: 0.848145
- 剪枝通道: 2,854 / 9,504

**推理性能对比**:
- 原始FPS: 16.77
- 剪枝后FPS: 16.16
- FPS变化: -3.7%
- 原始推理时间: 59.62ms
- 剪枝后推理时间: 61.89ms
- 时间变化: +3.8%

**检测能力对比**:
- 原始检测: 4.53 个工人/图像
- 剪枝后检测: 0.00 个工人/图像
- 检测变化: -4.53 个工人/图像

## 📁 生成的文件

本次实验生成的文件:
- `pruned_yolo_10percent.pt` - 10.0%剪枝模型
- `pruned_yolo_15percent.pt` - 15.0%剪枝模型
- `pruned_yolo_20percent.pt` - 20.0%剪枝模型
- `pruned_yolo_25percent.pt` - 25.0%剪枝模型
- `pruned_yolo_30percent.pt` - 30.0%剪枝模型
- `experiment_results.json` - 详细实验数据
- `performance_comparison.png` - 性能对比图表
- `experiment_report.html` - HTML格式报告
