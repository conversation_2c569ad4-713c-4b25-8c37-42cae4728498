import sys
import os
import argparse
import torch
import numpy as np
from pathlib import Path
import copy

# 添加YOLOv5路径
sys.path.insert(0, './yolov5')

from yolov5.models.yolo import Model
from yolov5.utils.torch_utils import select_device

def load_model(weights, device, to_float=True):
    """
    加载YOLOv5模型

    参数:
        weights (str): 模型权重文件路径
        device (str): 设备 ('cpu' 或 'cuda:0' 等)
        to_float (bool): 是否将模型转换为全精度 (FP32)

    返回:
        model: 加载的模型
    """
    print(f"加载模型: {weights}")
    model = torch.load(weights, map_location=device)
    if isinstance(model, dict):
        model = model['ema' if model.get('ema') else 'model']  # 提取模型

    # 确保是PyTorch模型
    if isinstance(model, torch.nn.Module):
        # 检查模型权重类型
        weight_dtype = next(model.parameters()).dtype
        print(f"原始模型权重类型: {weight_dtype}")

        # 如果需要，将模型转换为全精度
        if to_float and weight_dtype != torch.float32:
            print("将模型转换为全精度 (FP32)...")
            model = model.float()
            print(f"转换后模型权重类型: {next(model.parameters()).dtype}")

        return model
    else:
        # 如果是state_dict，需要先加载模型结构
        print("加载模型结构...")
        yaml_file = Path(weights).with_suffix('.yaml')
        if not yaml_file.exists():
            yaml_file = Path('yolov5/models/yolov5s.yaml')  # 默认使用yolov5s结构

        model_structure = Model(yaml_file, ch=3, nc=80)  # 创建模型结构
        model_structure.load_state_dict(model)  # 加载权重

        # 如果需要，将模型转换为全精度
        if to_float:
            model_structure = model_structure.float()
            print("模型已转换为全精度 (FP32)")

        return model_structure

def analyze_model(model):
    """
    分析模型结构和参数量

    参数:
        model: PyTorch模型
    """
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型总参数量: {total_params:,}")

    # 分析每层参数
    print("\n各层参数分布:")
    for name, module in model.named_modules():
        if isinstance(module, torch.nn.Conv2d) or isinstance(module, torch.nn.Linear):
            params = sum(p.numel() for p in module.parameters())
            print(f"{name}: {params:,} 参数")

def install_torch_pruning():
    """安装torch-pruning库"""
    try:
        import torch_pruning as tp
        print("torch-pruning已安装")
    except ImportError:
        print("正在安装torch-pruning...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "torch-pruning"])
        import torch_pruning as tp
        print("torch-pruning安装完成")
    return

def advanced_prune(model, amount=0.3, save_path='pruned_model.pt', img_size=640, device='cpu'):
    """
    使用torch-pruning库进行高级剪枝

    参数:
        model: PyTorch模型
        amount (float): 剪枝比例 (0.0-1.0)
        save_path (str): 保存路径
        img_size (int): 图像大小
        device (str): 设备

    返回:
        pruned_model: 剪枝后的模型
    """
    # 安装并导入torch-pruning
    install_torch_pruning()
    import torch_pruning as tp

    print(f"开始高级剪枝，剪枝比例: {amount*100:.1f}%")

    # 创建模型副本
    model = copy.deepcopy(model)
    model.eval()

    # 准备示例输入
    example_inputs = torch.randn(1, 3, img_size, img_size).to(device)

    # 创建剪枝器
    pruner = tp.pruner.MagnitudePruner(
        model,
        example_inputs,
        importance=tp.importance.MagnitudeImportance(p=1),  # L1范数
        ch_sparsity=amount,  # 通道稀疏度
        root_module_types=[torch.nn.Conv2d],  # 只剪枝卷积层
        ignored_layers=[model.model[-1]],  # 不剪枝检测头
    )

    # 执行剪枝
    pruner.step()

    # 保存剪枝后的模型
    torch.save(model, save_path)
    print(f"剪枝后的模型已保存到: {save_path}")

    # 分析剪枝后的模型
    analyze_model(model)

    return model

def slim_prune(model, amount=0.3, save_path='slim_model.pt'):
    """
    使用网络瘦身方法剪枝YOLOv5模型

    参数:
        model: PyTorch模型
        amount (float): 剪枝比例 (0.0-1.0)
        save_path (str): 保存路径

    返回:
        pruned_model: 剪枝后的模型
    """
    print(f"开始网络瘦身剪枝，剪枝比例: {amount*100:.1f}%")

    # 创建模型副本
    model = copy.deepcopy(model)
    model.eval()

    # 获取所有卷积层
    conv_layers = []
    for name, module in model.named_modules():
        if isinstance(module, torch.nn.Conv2d) and not name.startswith('model.24'):  # 不剪枝检测头
            conv_layers.append((name, module))

    # 对每个卷积层进行剪枝
    for name, conv in conv_layers:
        # 获取卷积权重
        weight = conv.weight.data.clone()

        # 计算每个通道的L1范数
        channel_norms = torch.sum(torch.abs(weight), dim=(1, 2, 3))

        # 确定要保留的通道数
        num_channels = weight.size(0)
        num_to_keep = int(num_channels * (1 - amount))
        num_to_keep = max(num_to_keep, 1)  # 至少保留一个通道

        # 找出重要的通道
        _, indices = torch.topk(channel_norms, num_to_keep)

        # 创建新的卷积层
        new_conv = torch.nn.Conv2d(
            in_channels=conv.in_channels,
            out_channels=num_to_keep,
            kernel_size=conv.kernel_size,
            stride=conv.stride,
            padding=conv.padding,
            dilation=conv.dilation,
            groups=1,  # 简化为不使用分组卷积
            bias=conv.bias is not None
        )

        # 复制权重和偏置
        new_conv.weight.data = weight[indices]
        if conv.bias is not None:
            new_conv.bias.data = conv.bias.data[indices]

        # 替换原始卷积层
        # 注意：这种简单替换可能会破坏模型结构，实际应用中需要更复杂的处理
        parent_name = '.'.join(name.split('.')[:-1])
        child_name = name.split('.')[-1]
        parent_module = model
        for n in parent_name.split('.'):
            if n:
                parent_module = getattr(parent_module, n)
        setattr(parent_module, child_name, new_conv)

    # 保存剪枝后的模型
    torch.save(model, save_path)
    print(f"剪枝后的模型已保存到: {save_path}")

    # 分析剪枝后的模型
    analyze_model(model)

    return model

def benchmark_model(model, img_size=640, device='cpu', iterations=100):
    """
    基准测试模型推理速度

    参数:
        model: PyTorch模型
        img_size (int): 图像大小
        device (str): 设备
        iterations (int): 迭代次数
    """
    print(f"基准测试模型推理速度...")

    # 检查模型权重类型
    weight_dtype = next(model.parameters()).dtype
    print(f"模型权重数据类型: {weight_dtype}")

    # 准备输入，确保与模型权重类型匹配
    dummy_input = torch.randn(1, 3, img_size, img_size, dtype=weight_dtype).to(device)
    model = model.to(device)
    model.eval()

    try:
        # 预热
        print("开始预热...")
        with torch.no_grad():
            for i in range(10):
                _ = model(dummy_input)
                if i == 0:
                    print("成功完成第一次推理")

        # 计时
        print(f"开始计时 {iterations} 次迭代...")
        import time
        start_time = time.time()
        with torch.no_grad():
            for _ in range(iterations):
                _ = model(dummy_input)
        end_time = time.time()

        # 计算FPS
        elapsed_time = end_time - start_time
        fps = iterations / elapsed_time

        print(f"推理速度: {fps:.2f} FPS (每帧 {1000/fps:.2f} ms)")
        print(f"{iterations}次迭代总耗时: {elapsed_time:.2f} 秒")
    except Exception as e:
        print(f"基准测试时出错: {e}")
        print("尝试将模型转换为全精度后再测试...")

        # 转换为全精度并重试
        model = model.float()
        dummy_input = torch.randn(1, 3, img_size, img_size).to(device)

        try:
            # 预热
            with torch.no_grad():
                for _ in range(10):
                    _ = model(dummy_input)

            # 计时
            start_time = time.time()
            with torch.no_grad():
                for _ in range(iterations):
                    _ = model(dummy_input)
            end_time = time.time()

            # 计算FPS
            elapsed_time = end_time - start_time
            fps = iterations / elapsed_time

            print(f"推理速度 (全精度): {fps:.2f} FPS (每帧 {1000/fps:.2f} ms)")
            print(f"{iterations}次迭代总耗时: {elapsed_time:.2f} 秒")
        except Exception as e2:
            print(f"全精度基准测试也失败: {e2}")
            print("无法完成基准测试")

def export_onnx(model, img_size=640, save_path='pruned_model.onnx'):
    """
    将模型导出为ONNX格式

    参数:
        model: PyTorch模型
        img_size (int): 图像大小
        save_path (str): 保存路径
    """
    print(f"导出模型为ONNX格式...")

    # 准备输入
    dummy_input = torch.randn(1, 3, img_size, img_size)
    model.eval()

    # 导出
    torch.onnx.export(
        model,
        dummy_input,
        save_path,
        verbose=False,
        opset_version=12,
        input_names=['images'],
        output_names=['output'],
        dynamic_axes={
            'images': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        }
    )

    print(f"ONNX模型已保存到: {save_path}")

def main():
    parser = argparse.ArgumentParser(description='YOLOv5高级模型剪枝工具')
    parser.add_argument('--weights', type=str, default='yolov5/best_22.pt', help='模型权重文件路径')
    parser.add_argument('--device', type=str, default='cpu', help='设备 (cpu 或 cuda:0 等)')
    parser.add_argument('--amount', type=float, default=0.3, help='剪枝比例 (0.0-1.0)')
    parser.add_argument('--method', type=str, default='advanced', choices=['advanced', 'slim', 'l1'], help='剪枝方法')
    parser.add_argument('--save-path', type=str, default='pruned_model.pt', help='剪枝后模型保存路径')
    parser.add_argument('--benchmark', action='store_true', help='是否进行基准测试')
    parser.add_argument('--export-onnx', action='store_true', help='是否导出为ONNX格式')
    parser.add_argument('--img-size', type=int, default=640, help='图像大小')
    parser.add_argument('--to-float', action='store_true', default=True, help='将模型转换为全精度 (FP32)')
    parser.add_argument('--no-float', dest='to_float', action='store_false', help='不将模型转换为全精度')

    args = parser.parse_args()

    # 选择设备
    device = select_device(args.device)

    # 加载模型
    model = load_model(args.weights, device, to_float=args.to_float)

    # 分析原始模型
    print("\n原始模型信息:")
    analyze_model(model)

    # 如果需要，进行基准测试
    if args.benchmark:
        print("\n原始模型基准测试:")
        benchmark_model(model, args.img_size, device)

    # 剪枝模型
    print(f"\n开始{args.method}剪枝模型...")
    if args.method == 'advanced':
        pruned_model = advanced_prune(model, args.amount, args.save_path, args.img_size, device)
    elif args.method == 'slim':
        pruned_model = slim_prune(model, args.amount, args.save_path)
    else:  # l1
        from prune_yolov5 import prune_model_l1
        pruned_model = prune_model_l1(model, args.amount, args.save_path)

    # 如果需要，进行基准测试
    if args.benchmark:
        print("\n剪枝后模型基准测试:")
        benchmark_model(pruned_model, args.img_size, device)

    # 如果需要，导出为ONNX格式
    if args.export_onnx:
        onnx_path = os.path.splitext(args.save_path)[0] + '.onnx'
        export_onnx(pruned_model, args.img_size, onnx_path)

    print("\n剪枝完成!")

if __name__ == '__main__':
    main()
