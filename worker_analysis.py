import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime, timedelta
import argparse
from pathlib import Path
import json

class WorkerAnalyzer:
    """
    工人行为分析工具，用于分析跟踪结果并生成报告
    """
    def __init__(self, tracks_file, video_file=None, output_dir='analysis_results'):
        """
        初始化工人分析器

        参数:
            tracks_file (str): 跟踪结果文件路径（MOT格式）
            video_file (str): 视频文件路径（可选）
            output_dir (str): 输出目录
        """
        self.tracks_file = tracks_file
        self.video_file = video_file
        self.output_dir = output_dir

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 读取跟踪数据
        self.tracks_data = self._load_tracks()

        # 视频信息
        self.video_info = self._get_video_info() if video_file else None

        # 工人统计信息
        self.worker_stats = {}

    def _load_tracks(self):
        """加载跟踪数据"""
        if not os.path.exists(self.tracks_file):
            raise FileNotFoundError(f"跟踪文件不存在: {self.tracks_file}")

        # MOT格式: frame_id, track_id, x, y, w, h, conf, -1, -1, -1
        columns = ['frame', 'id', 'x', 'y', 'w', 'h', 'conf', 'x3d', 'y3d', 'z3d']
        data = pd.read_csv(self.tracks_file, header=None, names=columns, sep=' ')
        return data

    def _get_video_info(self):
        """获取视频信息"""
        if not os.path.exists(self.video_file):
            raise FileNotFoundError(f"视频文件不存在: {self.video_file}")

        cap = cv2.VideoCapture(self.video_file)
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        cap.release()

        return {
            'fps': fps,
            'frame_count': frame_count,
            'width': width,
            'height': height,
            'duration': frame_count / fps if fps > 0 else 0
        }

    def analyze(self):
        """分析工人行为"""
        # 1. 计算每个工人的基本统计信息
        self._calculate_worker_stats()

        # 2. 生成工人活动热图
        self._generate_heatmap()

        # 3. 分析工人活动模式
        self._analyze_activity_patterns()

        # 4. 检测异常行为
        self._detect_anomalies()

        # 5. 生成报告
        self._generate_report()

        return self.worker_stats

    def _calculate_worker_stats(self):
        """计算每个工人的基本统计信息"""
        # 获取所有工人ID
        worker_ids = self.tracks_data['id'].unique()

        for worker_id in worker_ids:
            # 获取该工人的所有轨迹点
            worker_tracks = self.tracks_data[self.tracks_data['id'] == worker_id]

            # 计算工人出现的帧范围
            first_frame = worker_tracks['frame'].min()
            last_frame = worker_tracks['frame'].max()

            # 计算工人在画面中的持续时间（秒）
            duration = 0
            if self.video_info:
                duration = (last_frame - first_frame) / self.video_info['fps']

            # 计算工人移动的总距离
            total_distance = 0
            prev_x, prev_y = None, None

            for _, row in worker_tracks.iterrows():
                x = row['x'] + row['w'] / 2  # 中心点x
                y = row['y'] + row['h'] / 2  # 中心点y

                if prev_x is not None and prev_y is not None:
                    # 计算欧氏距离
                    distance = np.sqrt((x - prev_x)**2 + (y - prev_y)**2)
                    total_distance += distance

                prev_x, prev_y = x, y

            # 计算平均速度（像素/秒）
            avg_speed = 0
            if duration > 0:
                avg_speed = total_distance / duration

            # 计算工人活动区域（边界框）
            min_x = worker_tracks['x'].min()
            min_y = worker_tracks['y'].min()
            max_x = (worker_tracks['x'] + worker_tracks['w']).max()
            max_y = (worker_tracks['y'] + worker_tracks['h']).max()

            # 存储统计信息
            self.worker_stats[int(worker_id)] = {
                'first_frame': int(first_frame),
                'last_frame': int(last_frame),
                'duration': duration,
                'total_distance': total_distance,
                'avg_speed': avg_speed,
                'activity_area': {
                    'min_x': min_x,
                    'min_y': min_y,
                    'max_x': max_x,
                    'max_y': max_y,
                    'width': max_x - min_x,
                    'height': max_y - min_y
                },
                'track_points': len(worker_tracks)
            }

    def _generate_heatmap(self):
        """生成工人活动热图"""
        if not self.video_info:
            # 估计视频尺寸
            max_x = self.tracks_data['x'].max() + self.tracks_data['w'].max()
            max_y = self.tracks_data['y'].max() + self.tracks_data['h'].max()
            width, height = int(max_x), int(max_y)
        else:
            width, height = self.video_info['width'], self.video_info['height']

        # 创建热图
        heatmap = np.zeros((height, width), dtype=np.uint8)

        # 遍历所有轨迹点
        for _, row in self.tracks_data.iterrows():
            x = int(row['x'] + row['w'] / 2)  # 中心点x
            y = int(row['y'] + row['h'] / 2)  # 中心点y

            # 确保坐标在图像范围内
            if 0 <= x < width and 0 <= y < height:
                # 在热图上增加点的权重
                cv2.circle(heatmap, (x, y), 5, 255, -1)

        # 应用高斯模糊使热图更平滑
        heatmap = cv2.GaussianBlur(heatmap, (15, 15), 0)

        # 应用颜色映射
        heatmap_color = cv2.applyColorMap(heatmap, cv2.COLORMAP_JET)

        # 保存热图
        cv2.imwrite(os.path.join(self.output_dir, 'worker_heatmap.jpg'), heatmap_color)

        # 使用matplotlib生成更好的可视化效果
        plt.figure(figsize=(12, 8))
        plt.imshow(cv2.cvtColor(heatmap_color, cv2.COLOR_BGR2RGB))
        plt.title('Worker Activity Heatmap')  # 使用英文避免字体问题
        plt.colorbar(label='Activity Intensity')
        plt.savefig(os.path.join(self.output_dir, 'worker_heatmap_plt.png'), dpi=300)
        plt.close()



    def _generate_worker_count_charts(self):
        """生成每秒和每帧的工人数量图表"""
        print("生成工人数量图表...")

        # 获取基本信息
        if self.video_info:
            fps = self.video_info['fps']
            total_frames = self.video_info['frame_count']
            min_frame = 1
            max_frame = total_frames
        else:
            # 使用跟踪数据估算
            fps = 30.0
            all_first_frames = [stats['first_frame'] for stats in self.worker_stats.values()]
            all_last_frames = [stats['last_frame'] for stats in self.worker_stats.values()]
            min_frame = min(all_first_frames) if all_first_frames else 1
            max_frame = max(all_last_frames) if all_last_frames else 100
            total_frames = max_frame - min_frame + 1

        print(f"视频信息: FPS={fps:.2f}, 总帧数={total_frames}, 总时长={total_frames/fps:.2f}秒")

        # 生成每秒和每帧的工人计数图表
        self._generate_per_second_chart(fps, min_frame, max_frame, total_frames)
        self._generate_per_frame_chart(fps, min_frame, max_frame, total_frames)

    def _generate_per_second_chart(self, fps, min_frame, max_frame, total_frames):
        """生成每秒工人数量图表"""
        print("生成每秒工人数量图表...")

        # 计算总秒数
        total_seconds = int(np.ceil(total_frames / fps))

        worker_counts = []
        time_labels = []

        for second in range(total_seconds):
            # 计算该秒对应的帧范围
            start_frame = min_frame + int(second * fps)
            end_frame = min(min_frame + int((second + 1) * fps), max_frame + 1)

            # 统计该秒内活跃的工人数量
            active_workers = 0
            for worker_id, stats in self.worker_stats.items():
                worker_first = stats['first_frame']
                worker_last = stats['last_frame']

                # 检查工人是否在该时间段内活跃
                if not (worker_last < start_frame or worker_first >= end_frame):
                    active_workers += 1

            worker_counts.append(active_workers)
            time_labels.append(second)

        # 创建图表
        plt.figure(figsize=(15, 6))

        if len(time_labels) > 1:
            plt.plot(time_labels, worker_counts, marker='o', linestyle='-',
                    linewidth=2, markersize=4, color='#2E86AB', markerfacecolor='#A23B72')
        else:
            plt.scatter(time_labels, worker_counts, s=100, color='#2E86AB')

        plt.title('Worker Count Per Second', fontsize=16, fontweight='bold')
        plt.xlabel('Time (seconds)', fontsize=12)
        plt.ylabel('Worker Count', fontsize=12)
        plt.grid(True, alpha=0.3)

        # 设置Y轴范围
        max_count = max(worker_counts) if worker_counts and max(worker_counts) > 0 else 1
        plt.ylim(-0.5, max_count + 0.5)

        # 只在数据点不太多时添加数值标签
        if len(time_labels) <= 60:  # 1分钟以内显示标签
            for x, y in zip(time_labels, worker_counts):
                plt.annotate(f'{int(y)}', (x, y), textcoords="offset points",
                            xytext=(0,8), ha='center', fontsize=8)

        plt.tight_layout()

        # 保存图表
        per_second_path = os.path.join(self.output_dir, "worker_count_per_second.png")
        plt.savefig(per_second_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        print(f"每秒工人数量图表已保存到: {per_second_path}")
        print(f"总时长: {total_seconds}秒, 最大工人数: {max(worker_counts)}, 平均工人数: {np.mean(worker_counts):.2f}")

    def _generate_per_frame_chart(self, fps, min_frame, max_frame, total_frames):
        """生成每帧工人数量图表"""
        print("生成每帧工人数量图表...")

        # 如果帧数太多，进行采样以避免图表过于密集
        if total_frames > 1000:
            # 采样间隔，确保图表不超过1000个点
            sample_interval = max(1, total_frames // 1000)
            print(f"帧数较多({total_frames})，采样间隔: {sample_interval}")
        else:
            sample_interval = 1

        worker_counts = []
        frame_labels = []

        for frame in range(min_frame, max_frame + 1, sample_interval):
            # 统计该帧的工人数量
            active_workers = 0
            for worker_id, stats in self.worker_stats.items():
                worker_first = stats['first_frame']
                worker_last = stats['last_frame']

                # 检查工人是否在该帧活跃
                if worker_first <= frame <= worker_last:
                    active_workers += 1

            worker_counts.append(active_workers)
            frame_labels.append(frame)

        # 创建图表
        plt.figure(figsize=(15, 6))

        if len(frame_labels) > 1:
            plt.plot(frame_labels, worker_counts, linestyle='-',
                    linewidth=1, color='#2E86AB', alpha=0.8)

            # 如果数据点不太多，添加标记点
            if len(frame_labels) <= 200:
                plt.scatter(frame_labels, worker_counts, s=10, color='#A23B72', alpha=0.6)
        else:
            plt.scatter(frame_labels, worker_counts, s=100, color='#2E86AB')

        plt.title('Worker Count Per Frame', fontsize=16, fontweight='bold')
        plt.xlabel('Frame Number', fontsize=12)
        plt.ylabel('Worker Count', fontsize=12)
        plt.grid(True, alpha=0.3)

        # 设置Y轴范围
        max_count = max(worker_counts) if worker_counts and max(worker_counts) > 0 else 1
        plt.ylim(-0.5, max_count + 0.5)

        # 添加时间轴（秒）作为第二个X轴
        ax1 = plt.gca()
        ax2 = ax1.twiny()

        # 设置时间轴标签
        time_ticks = np.linspace(min_frame, max_frame, 10)
        time_labels_sec = [f"{int(t/fps):.0f}s" for t in time_ticks]
        ax2.set_xlim(ax1.get_xlim())
        ax2.set_xticks(time_ticks)
        ax2.set_xticklabels(time_labels_sec)
        ax2.set_xlabel('Time (seconds)', fontsize=12)

        plt.tight_layout()

        # 保存图表
        per_frame_path = os.path.join(self.output_dir, "worker_count_per_frame.png")
        plt.savefig(per_frame_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        print(f"每帧工人数量图表已保存到: {per_frame_path}")
        print(f"总帧数: {len(frame_labels)} (采样间隔: {sample_interval}), 最大工人数: {max(worker_counts)}, 平均工人数: {np.mean(worker_counts):.2f}")

    def _analyze_activity_patterns(self):
        """分析工人活动模式"""
        print("分析工人活动模式...")

        # 只生成每秒和每帧的工人计数图表
        self._generate_worker_count_charts()

    def _generate_worker_activity_heatmap(self):
        """生成工人活动热力图（时间 vs 工人ID）"""
        print("生成工人活动热力图...")

        # 获取基本信息
        if self.video_info:
            fps = self.video_info['fps']
            total_frames = self.video_info['frame_count']
            min_frame = 1
            max_frame = total_frames
        else:
            fps = 30.0
            all_first_frames = [stats['first_frame'] for stats in self.worker_stats.values()]
            all_last_frames = [stats['last_frame'] for stats in self.worker_stats.values()]
            min_frame = min(all_first_frames) if all_first_frames else 1
            max_frame = max(all_last_frames) if all_last_frames else 100
            total_frames = max_frame - min_frame + 1

        # 创建时间段（每5秒一个段）
        interval_seconds = 5
        interval_frames = int(fps * interval_seconds)
        num_intervals = max(1, int(np.ceil(total_frames / interval_frames)))

        # 获取所有工人ID并排序
        worker_ids = sorted([int(wid) for wid in self.worker_stats.keys()])

        # 创建活动矩阵
        activity_matrix = np.zeros((len(worker_ids), num_intervals))

        for i, worker_id in enumerate(worker_ids):
            worker_id_str = str(worker_id)
            if worker_id_str in self.worker_stats:
                stats = self.worker_stats[worker_id_str]
                worker_first = stats['first_frame']
                worker_last = stats['last_frame']

                for interval in range(num_intervals):
                    start_frame = min_frame + interval * interval_frames
                    end_frame = min(min_frame + (interval + 1) * interval_frames, max_frame + 1)

                    # 检查工人是否在该时间段内活跃
                    if not (worker_last < start_frame or worker_first >= end_frame):
                        # 计算活跃程度（重叠帧数 / 时间段总帧数）
                        overlap_start = max(start_frame, worker_first)
                        overlap_end = min(end_frame, worker_last + 1)
                        overlap_frames = max(0, overlap_end - overlap_start)
                        activity_intensity = overlap_frames / interval_frames
                        activity_matrix[i, interval] = activity_intensity

        # 创建热力图
        plt.figure(figsize=(14, 8))

        # 生成时间标签
        time_labels = [f"{i*interval_seconds}s" for i in range(num_intervals)]
        worker_labels = [f"Worker {wid}" for wid in worker_ids]

        # 绘制热力图
        im = plt.imshow(activity_matrix, cmap='YlOrRd', aspect='auto', interpolation='nearest')

        # 设置坐标轴
        plt.xticks(range(num_intervals), time_labels, rotation=45)
        plt.yticks(range(len(worker_ids)), worker_labels)

        # 添加颜色条
        cbar = plt.colorbar(im)
        cbar.set_label('Activity Intensity', rotation=270, labelpad=20)

        # 设置标题和标签
        plt.title('Worker Activity Heatmap Over Time', fontsize=16, fontweight='bold')
        plt.xlabel('Time', fontsize=12)
        plt.ylabel('Workers', fontsize=12)

        # 添加网格
        plt.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存热力图
        heatmap_path = os.path.join(self.output_dir, "worker_activity_heatmap.png")
        plt.savefig(heatmap_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        print(f"工人活动热力图已保存到: {heatmap_path}")

    def _generate_worker_statistics_chart(self):
        """生成工人统计信息图表"""
        print("生成工人统计信息图表...")

        # 修复工人统计数据中的时间计算
        self._fix_worker_stats_timing()

        # 提取统计数据
        worker_ids = []
        durations = []
        distances = []
        speeds = []
        track_points = []

        for worker_id, stats in self.worker_stats.items():
            worker_ids.append(int(worker_id))
            durations.append(stats.get('duration_fixed', stats.get('duration', 0)))
            distances.append(stats['total_distance'])
            speeds.append(stats.get('avg_speed_fixed', stats.get('avg_speed', 0)))
            track_points.append(stats['track_points'])

        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Worker Statistics Summary', fontsize=16, fontweight='bold')

        # 1. 工人活动时长
        axes[0, 0].bar(worker_ids, durations, color='skyblue', alpha=0.7)
        axes[0, 0].set_title('Worker Activity Duration')
        axes[0, 0].set_xlabel('Worker ID')
        axes[0, 0].set_ylabel('Duration (seconds)')
        axes[0, 0].grid(True, alpha=0.3)

        # 添加数值标签
        for i, v in enumerate(durations):
            if v > 0:
                axes[0, 0].text(worker_ids[i], v + max(durations)*0.01, f'{v:.1f}s',
                               ha='center', va='bottom', fontsize=8)

        # 2. 工人移动距离
        axes[0, 1].bar(worker_ids, distances, color='lightgreen', alpha=0.7)
        axes[0, 1].set_title('Worker Total Distance')
        axes[0, 1].set_xlabel('Worker ID')
        axes[0, 1].set_ylabel('Distance (pixels)')
        axes[0, 1].grid(True, alpha=0.3)

        # 3. 工人平均速度
        axes[1, 0].bar(worker_ids, speeds, color='orange', alpha=0.7)
        axes[1, 0].set_title('Worker Average Speed')
        axes[1, 0].set_xlabel('Worker ID')
        axes[1, 0].set_ylabel('Speed (pixels/second)')
        axes[1, 0].grid(True, alpha=0.3)

        # 4. 工人跟踪点数
        axes[1, 1].bar(worker_ids, track_points, color='pink', alpha=0.7)
        axes[1, 1].set_title('Worker Track Points')
        axes[1, 1].set_xlabel('Worker ID')
        axes[1, 1].set_ylabel('Number of Track Points')
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存统计图表
        stats_chart_path = os.path.join(self.output_dir, "worker_statistics_summary.png")
        plt.savefig(stats_chart_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        print(f"工人统计信息图表已保存到: {stats_chart_path}")

        # 打印统计摘要
        if durations:
            print(f"\n工人统计摘要:")
            print(f"  总工人数: {len(worker_ids)}")
            print(f"  平均活动时长: {np.mean(durations):.2f} 秒")
            print(f"  平均移动距离: {np.mean(distances):.2f} 像素")
            print(f"  平均速度: {np.mean(speeds):.2f} 像素/秒")
            print(f"  最活跃工人: ID {worker_ids[np.argmax(durations)]} (时长 {max(durations):.2f}秒)")
            print(f"  最快工人: ID {worker_ids[np.argmax(speeds)]} (速度 {max(speeds):.2f}像素/秒)")

    def _generate_worker_timeline_chart(self):
        """生成工人时间线图表"""
        print("生成工人时间线图表...")

        # 获取FPS信息
        if self.video_info:
            fps = self.video_info['fps']
        else:
            fps = 30.0

        # 准备数据
        worker_data = []
        for worker_id, stats in self.worker_stats.items():
            start_time = stats['first_frame'] / fps / 60  # 转换为分钟
            end_time = stats['last_frame'] / fps / 60
            duration = end_time - start_time

            worker_data.append({
                'worker_id': int(worker_id),
                'start_time': start_time,
                'end_time': end_time,
                'duration': duration
            })

        # 按工人ID排序
        worker_data.sort(key=lambda x: x['worker_id'])

        # 创建时间线图表
        fig, ax = plt.subplots(figsize=(14, 8))

        colors = plt.cm.Set3(np.linspace(0, 1, len(worker_data)))

        for i, data in enumerate(worker_data):
            worker_id = data['worker_id']
            start_time = data['start_time']
            duration = data['duration']

            # 绘制时间线条
            ax.barh(i, duration, left=start_time, height=0.6,
                   color=colors[i], alpha=0.8,
                   label=f'Worker {worker_id}')

            # 添加工人ID标签
            ax.text(start_time + duration/2, i, f'ID {worker_id}',
                   ha='center', va='center', fontweight='bold', fontsize=10)

        # 设置图表属性
        ax.set_xlabel('Time (minutes)', fontsize=12)
        ax.set_ylabel('Workers', fontsize=12)
        ax.set_title('Worker Activity Timeline', fontsize=16, fontweight='bold')

        # 设置Y轴标签
        ax.set_yticks(range(len(worker_data)))
        ax.set_yticklabels([f'Worker {data["worker_id"]}' for data in worker_data])

        # 添加网格
        ax.grid(True, alpha=0.3, axis='x')

        plt.tight_layout()

        # 保存时间线图表
        timeline_path = os.path.join(self.output_dir, "worker_timeline.png")
        plt.savefig(timeline_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        print(f"工人时间线图表已保存到: {timeline_path}")

    def _fix_worker_stats_timing(self):
        """修复工人统计数据中的时间计算"""
        if self.video_info:
            fps = self.video_info['fps']
        else:
            fps = 30.0

        for worker_id, stats in self.worker_stats.items():
            first_frame = stats['first_frame']
            last_frame = stats['last_frame']

            # 修复duration计算
            frame_duration = last_frame - first_frame + 1
            duration_seconds = frame_duration / fps

            # 修复avg_speed计算
            total_distance = stats['total_distance']
            avg_speed = total_distance / duration_seconds if duration_seconds > 0 else 0

            # 添加修复后的值
            stats['duration_fixed'] = duration_seconds
            stats['avg_speed_fixed'] = avg_speed
            stats['fps_used'] = fps



    def _detect_anomalies(self):
        """检测异常行为"""
        # 1. 检测长时间静止的工人
        for worker_id, stats in self.worker_stats.items():
            # 如果工人的平均速度非常低且持续时间较长，可能表示异常
            if stats['avg_speed'] < 5 and stats['duration'] > 60:  # 阈值可调整
                stats['anomalies'] = stats.get('anomalies', []) + ['Stationary for too long']

            # 2. 检测活动区域异常小的工人
            area_width = stats['activity_area']['width']
            area_height = stats['activity_area']['height']
            if area_width < 50 and area_height < 50 and stats['duration'] > 60:  # 阈值可调整
                stats['anomalies'] = stats.get('anomalies', []) + ['Abnormally small activity area']

            # 3. 检测移动速度异常快的工人
            if stats['avg_speed'] > 100:  # 阈值可调整
                stats['anomalies'] = stats.get('anomalies', []) + ['Abnormally fast movement']

    def _generate_report(self):
        """生成分析报告"""
        # 将统计信息保存为JSON文件
        # 首先转换NumPy类型为Python原生类型
        json_compatible_stats = self._convert_to_json_serializable(self.worker_stats)

        with open(os.path.join(self.output_dir, 'worker_stats.json'), 'w', encoding='utf-8') as f:
            json.dump(json_compatible_stats, f, ensure_ascii=False, indent=4)

        # 生成HTML报告
        html_report = self._generate_html_report()
        with open(os.path.join(self.output_dir, 'worker_report.html'), 'w', encoding='utf-8') as f:
            f.write(html_report)

    def _convert_to_json_serializable(self, obj):
        """将对象转换为JSON可序列化的格式"""
        import numpy as np

        if isinstance(obj, dict):
            return {k: self._convert_to_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj

    def _generate_html_report(self):
        """生成HTML格式的报告"""
        try:
            # 生成视频信息
            video_info = ""
            if self.video_info:
                duration_str = str(timedelta(seconds=int(self.video_info['duration'])))
                video_info = f"""
                <p>Video Resolution: {self.video_info['width']}x{self.video_info['height']}</p>
                <p>Video FPS: {self.video_info['fps']} FPS</p>
                <p>Video Duration: {duration_str}</p>
                """

            # 生成工人行信息
            worker_rows = ""
            for worker_id, stats in self.worker_stats.items():
                # 格式化异常行为
                anomalies = ""
                if 'anomalies' in stats and stats['anomalies']:
                    anomalies = f"<span class='anomaly'>{', '.join(stats['anomalies'])}</span>"

                # 格式化活动区域
                area = stats['activity_area']
                area_str = f"{int(area['width'])}x{int(area['height'])}"

                # 添加行
                worker_rows += f"""
                <tr>
                    <td>{worker_id}</td>
                    <td>{stats['duration']:.2f}</td>
                    <td>{stats['total_distance']:.2f}</td>
                    <td>{stats['avg_speed']:.2f}</td>
                    <td>{area_str}</td>
                    <td>{anomalies}</td>
                </tr>
                """

            # 基本HTML模板
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Worker Behavior Analysis Report</title>
                <style>
                    body {{
                        font-family: Arial, Helvetica, sans-serif;
                        margin: 20px;
                    }}
                    h1, h2 {{
                        color: #333;
                    }}
                    table {{
                        border-collapse: collapse;
                        width: 100%;
                        margin-bottom: 20px;
                    }}
                    th, td {{
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: left;
                    }}
                    th {{
                        background-color: #f2f2f2;
                    }}
                    tr:nth-child(even) {{
                        background-color: #f9f9f9;
                    }}
                    .anomaly {{
                        color: red;
                        font-weight: bold;
                    }}
                    .images {{
                        display: flex;
                        flex-wrap: wrap;
                        margin-top: 20px;
                    }}
                    .images div {{
                        margin-right: 20px;
                        margin-bottom: 20px;
                    }}
                    .images img {{
                        max-width: 100%;
                        height: auto;
                        border: 1px solid #ddd;
                    }}
                </style>
            </head>
            <body>
                <h1>Worker Behavior Analysis Report</h1>
                <p>Generated at: {date}</p>

                <h2>Overall Statistics</h2>
                <p>Total Workers: {total_workers}</p>
                <p>Analysis File: {tracks_file}</p>
                {video_info}

                <h2>Worker Details</h2>
                <table>
                    <tr>
                        <th>Worker ID</th>
                        <th>Duration (sec)</th>
                        <th>Total Distance (px)</th>
                        <th>Avg Speed (px/sec)</th>
                        <th>Activity Area (px)</th>
                        <th>Anomalies</th>
                    </tr>
                    {worker_rows}
                </table>

                <h2>Visualization Results</h2>
                <div class="images">
                    <div style="margin-right: 20px; margin-bottom: 20px;">
                        <h3>Worker Activity Heatmap</h3>
                        <img src="worker_heatmap_plt.png" alt="Worker Activity Heatmap" style="max-width: 500px;">
                    </div>
                    <div style="margin-bottom: 20px;">
                        <h3>Worker Count Over Time</h3>
                        <img src="worker_count_over_time.png" alt="Worker Count Over Time" style="max-width: 500px;">
                    </div>
                </div>
            </body>
            </html>
            """

            # 填充模板
            html = html.format(
                date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                total_workers=len(self.worker_stats),
                tracks_file=self.tracks_file,
                video_info=video_info,
                worker_rows=worker_rows
            )

            return html

        except Exception as e:
            print(f"生成HTML报告时出错: {e}")
            # 返回一个简单的HTML报告
            return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Worker Analysis Report</title>
                <style>
                    body {{
                        font-family: Arial, Helvetica, sans-serif;
                        margin: 20px;
                    }}
                    h1 {{
                        color: #333;
                    }}
                </style>
            </head>
            <body>
                <h1>Worker Analysis Report</h1>
                <p>Error generating detailed report: """ + str(e) + """</p>
                <p>Please check the JSON data file for analysis results.</p>
                <p>The following files were generated:</p>
                <ul>
                    <li>worker_stats.json - Contains detailed statistics for each worker</li>
                    <li>worker_heatmap.jpg - Worker activity heatmap</li>
                    <li>worker_heatmap_plt.png - Worker activity heatmap (matplotlib version)</li>
                    <li>worker_count_over_time.png - Worker count over time chart</li>
                </ul>
            </body>
            </html>
            """


def main():
    parser = argparse.ArgumentParser(description='工人行为分析工具')
    parser.add_argument('--tracks', type=str, required=True, help='跟踪结果文件路径（MOT格式）')
    parser.add_argument('--video', type=str, default=None, help='视频文件路径（可选）')
    parser.add_argument('--output', type=str, default='analysis_results', help='输出目录')
    args = parser.parse_args()

    analyzer = WorkerAnalyzer(args.tracks, args.video, args.output)
    analyzer.analyze()
    print(f"分析完成，结果保存在: {args.output}")


if __name__ == '__main__':
    main()
