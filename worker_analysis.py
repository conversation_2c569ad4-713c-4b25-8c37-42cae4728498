import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime, timedelta
import argparse
from pathlib import Path
import json

class WorkerAnalyzer:
    """
    工人行为分析工具，用于分析跟踪结果并生成报告
    """
    def __init__(self, tracks_file, video_file=None, output_dir='analysis_results'):
        """
        初始化工人分析器

        参数:
            tracks_file (str): 跟踪结果文件路径（MOT格式）
            video_file (str): 视频文件路径（可选）
            output_dir (str): 输出目录
        """
        self.tracks_file = tracks_file
        self.video_file = video_file
        self.output_dir = output_dir

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 读取跟踪数据
        self.tracks_data = self._load_tracks()

        # 视频信息
        self.video_info = self._get_video_info() if video_file else None

        # 工人统计信息
        self.worker_stats = {}

    def _load_tracks(self):
        """加载跟踪数据"""
        if not os.path.exists(self.tracks_file):
            raise FileNotFoundError(f"跟踪文件不存在: {self.tracks_file}")

        # MOT格式: frame_id, track_id, x, y, w, h, conf, -1, -1, -1
        columns = ['frame', 'id', 'x', 'y', 'w', 'h', 'conf', 'x3d', 'y3d', 'z3d']
        data = pd.read_csv(self.tracks_file, header=None, names=columns, sep=' ')
        return data

    def _get_video_info(self):
        """获取视频信息"""
        if not os.path.exists(self.video_file):
            raise FileNotFoundError(f"视频文件不存在: {self.video_file}")

        cap = cv2.VideoCapture(self.video_file)
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        cap.release()

        return {
            'fps': fps,
            'frame_count': frame_count,
            'width': width,
            'height': height,
            'duration': frame_count / fps if fps > 0 else 0
        }

    def analyze(self):
        """分析工人行为"""
        # 1. 计算每个工人的基本统计信息
        self._calculate_worker_stats()

        # 2. 生成工人活动热图
        self._generate_heatmap()

        # 3. 分析工人活动模式
        self._analyze_activity_patterns()

        # 4. 检测异常行为
        self._detect_anomalies()

        # 5. 生成报告
        self._generate_report()

        return self.worker_stats

    def _calculate_worker_stats(self):
        """计算每个工人的基本统计信息"""
        # 获取所有工人ID
        worker_ids = self.tracks_data['id'].unique()

        for worker_id in worker_ids:
            # 获取该工人的所有轨迹点
            worker_tracks = self.tracks_data[self.tracks_data['id'] == worker_id]

            # 计算工人出现的帧范围
            first_frame = worker_tracks['frame'].min()
            last_frame = worker_tracks['frame'].max()

            # 计算工人在画面中的持续时间（秒）
            duration = 0
            if self.video_info:
                duration = (last_frame - first_frame) / self.video_info['fps']

            # 计算工人移动的总距离
            total_distance = 0
            prev_x, prev_y = None, None

            for _, row in worker_tracks.iterrows():
                x = row['x'] + row['w'] / 2  # 中心点x
                y = row['y'] + row['h'] / 2  # 中心点y

                if prev_x is not None and prev_y is not None:
                    # 计算欧氏距离
                    distance = np.sqrt((x - prev_x)**2 + (y - prev_y)**2)
                    total_distance += distance

                prev_x, prev_y = x, y

            # 计算平均速度（像素/秒）
            avg_speed = 0
            if duration > 0:
                avg_speed = total_distance / duration

            # 计算工人活动区域（边界框）
            min_x = worker_tracks['x'].min()
            min_y = worker_tracks['y'].min()
            max_x = (worker_tracks['x'] + worker_tracks['w']).max()
            max_y = (worker_tracks['y'] + worker_tracks['h']).max()

            # 存储统计信息
            self.worker_stats[int(worker_id)] = {
                'first_frame': int(first_frame),
                'last_frame': int(last_frame),
                'duration': duration,
                'total_distance': total_distance,
                'avg_speed': avg_speed,
                'activity_area': {
                    'min_x': min_x,
                    'min_y': min_y,
                    'max_x': max_x,
                    'max_y': max_y,
                    'width': max_x - min_x,
                    'height': max_y - min_y
                },
                'track_points': len(worker_tracks)
            }

    def _generate_heatmap(self):
        """生成工人活动热图"""
        if not self.video_info:
            # 估计视频尺寸
            max_x = self.tracks_data['x'].max() + self.tracks_data['w'].max()
            max_y = self.tracks_data['y'].max() + self.tracks_data['h'].max()
            width, height = int(max_x), int(max_y)
        else:
            width, height = self.video_info['width'], self.video_info['height']

        # 创建热图
        heatmap = np.zeros((height, width), dtype=np.uint8)

        # 遍历所有轨迹点
        for _, row in self.tracks_data.iterrows():
            x = int(row['x'] + row['w'] / 2)  # 中心点x
            y = int(row['y'] + row['h'] / 2)  # 中心点y

            # 确保坐标在图像范围内
            if 0 <= x < width and 0 <= y < height:
                # 在热图上增加点的权重
                cv2.circle(heatmap, (x, y), 5, 255, -1)

        # 应用高斯模糊使热图更平滑
        heatmap = cv2.GaussianBlur(heatmap, (15, 15), 0)

        # 应用颜色映射
        heatmap_color = cv2.applyColorMap(heatmap, cv2.COLORMAP_JET)

        # 保存热图
        cv2.imwrite(os.path.join(self.output_dir, 'worker_heatmap.jpg'), heatmap_color)

        # 使用matplotlib生成更好的可视化效果
        plt.figure(figsize=(12, 8))
        plt.imshow(cv2.cvtColor(heatmap_color, cv2.COLOR_BGR2RGB))
        plt.title('Worker Activity Heatmap')  # 使用英文避免字体问题
        plt.colorbar(label='Activity Intensity')
        plt.savefig(os.path.join(self.output_dir, 'worker_heatmap_plt.png'), dpi=300)
        plt.close()

    def _analyze_activity_patterns(self):
        """分析工人活动模式"""
        # 计算每个时间段的工人数量
        if not self.video_info:
            # 如果没有视频信息，创建一个简单的活动模式图表
            # 设置matplotlib使用不会出现字体问题的配置
            import matplotlib as mpl
            mpl.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif']
            mpl.rcParams['axes.unicode_minus'] = False  # 正确显示负号

            # 创建一个简单的图表
            plt.figure(figsize=(12, 6))
            plt.plot([0, 1], [0, 0], marker='o', linestyle='-')
            plt.title('Worker Count Over Time (No Video Info)')
            plt.xlabel('Time (frames)')
            plt.ylabel('Worker Count')
            plt.grid(True)
            plt.savefig(os.path.join(self.output_dir, 'worker_count_over_time.png'), dpi=300)
            plt.close()
            return

        # 获取视频总帧数
        total_frames = self.video_info['frame_count']
        fps = self.video_info['fps']

        # 每分钟为一个时间段
        time_interval = int(fps * 60)
        num_intervals = int(np.ceil(total_frames / time_interval))

        # 初始化每个时间段的工人数量
        worker_counts = np.zeros(num_intervals)

        # 遍历所有帧，统计每个时间段的工人数量
        for interval in range(num_intervals):
            start_frame = interval * time_interval
            end_frame = min((interval + 1) * time_interval, total_frames)

            # 获取该时间段内的所有轨迹点
            interval_tracks = self.tracks_data[(self.tracks_data['frame'] >= start_frame) &
                                              (self.tracks_data['frame'] < end_frame)]

            # 统计不同工人ID的数量
            worker_counts[interval] = len(interval_tracks['id'].unique())

        # 生成时间标签（分钟）- 使用数字而不是字符串
        time_labels = list(range(num_intervals))

        # 设置matplotlib使用不会出现字体问题的配置
        import matplotlib as mpl
        mpl.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif']
        mpl.rcParams['axes.unicode_minus'] = False  # 正确显示负号

        # 绘制工人数量随时间变化的图表
        plt.figure(figsize=(12, 6))
        plt.plot(time_labels, worker_counts, marker='o', linestyle='-')
        plt.title('Worker Count Over Time')  # 使用英文避免字体问题
        plt.xlabel('Time (minutes)')
        plt.ylabel('Worker Count')
        plt.grid(True)
        plt.savefig(os.path.join(self.output_dir, 'worker_count_over_time.png'), dpi=300)
        plt.close()

        # 将活动模式信息添加到工人统计中
        for worker_id, stats in self.worker_stats.items():
            # 计算该工人在各时间段的活跃度
            worker_activity = np.zeros(num_intervals)
            worker_tracks = self.tracks_data[self.tracks_data['id'] == worker_id]

            for interval in range(num_intervals):
                start_frame = interval * time_interval
                end_frame = min((interval + 1) * time_interval, total_frames)

                # 获取该时间段内的轨迹点
                interval_points = worker_tracks[(worker_tracks['frame'] >= start_frame) &
                                               (worker_tracks['frame'] < end_frame)]

                # 统计轨迹点数量作为活跃度指标
                worker_activity[interval] = len(interval_points)

            # 添加到统计信息中
            self.worker_stats[worker_id]['activity_pattern'] = worker_activity.tolist()

    def _detect_anomalies(self):
        """检测异常行为"""
        # 1. 检测长时间静止的工人
        for worker_id, stats in self.worker_stats.items():
            # 如果工人的平均速度非常低且持续时间较长，可能表示异常
            if stats['avg_speed'] < 5 and stats['duration'] > 60:  # 阈值可调整
                stats['anomalies'] = stats.get('anomalies', []) + ['Stationary for too long']

            # 2. 检测活动区域异常小的工人
            area_width = stats['activity_area']['width']
            area_height = stats['activity_area']['height']
            if area_width < 50 and area_height < 50 and stats['duration'] > 60:  # 阈值可调整
                stats['anomalies'] = stats.get('anomalies', []) + ['Abnormally small activity area']

            # 3. 检测移动速度异常快的工人
            if stats['avg_speed'] > 100:  # 阈值可调整
                stats['anomalies'] = stats.get('anomalies', []) + ['Abnormally fast movement']

    def _generate_report(self):
        """生成分析报告"""
        # 将统计信息保存为JSON文件
        # 首先转换NumPy类型为Python原生类型
        json_compatible_stats = self._convert_to_json_serializable(self.worker_stats)

        with open(os.path.join(self.output_dir, 'worker_stats.json'), 'w', encoding='utf-8') as f:
            json.dump(json_compatible_stats, f, ensure_ascii=False, indent=4)

        # 生成HTML报告
        html_report = self._generate_html_report()
        with open(os.path.join(self.output_dir, 'worker_report.html'), 'w', encoding='utf-8') as f:
            f.write(html_report)

    def _convert_to_json_serializable(self, obj):
        """将对象转换为JSON可序列化的格式"""
        import numpy as np

        if isinstance(obj, dict):
            return {k: self._convert_to_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj

    def _generate_html_report(self):
        """生成HTML格式的报告"""
        try:
            # 生成视频信息
            video_info = ""
            if self.video_info:
                duration_str = str(timedelta(seconds=int(self.video_info['duration'])))
                video_info = f"""
                <p>Video Resolution: {self.video_info['width']}x{self.video_info['height']}</p>
                <p>Video FPS: {self.video_info['fps']} FPS</p>
                <p>Video Duration: {duration_str}</p>
                """

            # 生成工人行信息
            worker_rows = ""
            for worker_id, stats in self.worker_stats.items():
                # 格式化异常行为
                anomalies = ""
                if 'anomalies' in stats and stats['anomalies']:
                    anomalies = f"<span class='anomaly'>{', '.join(stats['anomalies'])}</span>"

                # 格式化活动区域
                area = stats['activity_area']
                area_str = f"{int(area['width'])}x{int(area['height'])}"

                # 添加行
                worker_rows += f"""
                <tr>
                    <td>{worker_id}</td>
                    <td>{stats['duration']:.2f}</td>
                    <td>{stats['total_distance']:.2f}</td>
                    <td>{stats['avg_speed']:.2f}</td>
                    <td>{area_str}</td>
                    <td>{anomalies}</td>
                </tr>
                """

            # 基本HTML模板
            html = """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Worker Behavior Analysis Report</title>
                <style>
                    body {
                        font-family: Arial, Helvetica, sans-serif;
                        margin: 20px;
                    }
                    h1, h2 {
                        color: #333;
                    }
                    table {
                        border-collapse: collapse;
                        width: 100%;
                        margin-bottom: 20px;
                    }
                    th, td {
                        border: 1px solid #ddd;
                        padding: 8px;
                        text-align: left;
                    }
                    th {
                        background-color: #f2f2f2;
                    }
                    tr:nth-child(even) {
                        background-color: #f9f9f9;
                    }
                    .anomaly {
                        color: red;
                        font-weight: bold;
                    }
                    .images {
                        display: flex;
                        flex-wrap: wrap;
                        margin-top: 20px;
                    }
                    .images div {
                        margin-right: 20px;
                        margin-bottom: 20px;
                    }
                    .images img {
                        max-width: 100%;
                        height: auto;
                        border: 1px solid #ddd;
                    }
                </style>
            </head>
            <body>
                <h1>Worker Behavior Analysis Report</h1>
                <p>Generated at: {date}</p>

                <h2>Overall Statistics</h2>
                <p>Total Workers: {total_workers}</p>
                <p>Analysis File: {tracks_file}</p>
                {video_info}

                <h2>Worker Details</h2>
                <table>
                    <tr>
                        <th>Worker ID</th>
                        <th>Duration (sec)</th>
                        <th>Total Distance (px)</th>
                        <th>Avg Speed (px/sec)</th>
                        <th>Activity Area (px)</th>
                        <th>Anomalies</th>
                    </tr>
                    {worker_rows}
                </table>

                <h2>Visualization Results</h2>
                <div class="images">
                    <div style="margin-right: 20px; margin-bottom: 20px;">
                        <h3>Worker Activity Heatmap</h3>
                        <img src="worker_heatmap_plt.png" alt="Worker Activity Heatmap" style="max-width: 500px;">
                    </div>
                    <div style="margin-bottom: 20px;">
                        <h3>Worker Count Over Time</h3>
                        <img src="worker_count_over_time.png" alt="Worker Count Over Time" style="max-width: 500px;">
                    </div>
                </div>
            </body>
            </html>
            """

            # 填充模板
            html = html.format(
                date=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                total_workers=len(self.worker_stats),
                tracks_file=self.tracks_file,
                video_info=video_info,
                worker_rows=worker_rows
            )

            return html

        except Exception as e:
            print(f"生成HTML报告时出错: {e}")
            # 返回一个简单的HTML报告
            return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Worker Analysis Report</title>
                <style>
                    body {
                        font-family: Arial, Helvetica, sans-serif;
                        margin: 20px;
                    }
                    h1 {
                        color: #333;
                    }
                </style>
            </head>
            <body>
                <h1>Worker Analysis Report</h1>
                <p>Error generating detailed report: """ + str(e) + """</p>
                <p>Please check the JSON data file for analysis results.</p>
                <p>The following files were generated:</p>
                <ul>
                    <li>worker_stats.json - Contains detailed statistics for each worker</li>
                    <li>worker_heatmap.jpg - Worker activity heatmap</li>
                    <li>worker_heatmap_plt.png - Worker activity heatmap (matplotlib version)</li>
                    <li>worker_count_over_time.png - Worker count over time chart</li>
                </ul>
            </body>
            </html>
            """


def main():
    parser = argparse.ArgumentParser(description='工人行为分析工具')
    parser.add_argument('--tracks', type=str, required=True, help='跟踪结果文件路径（MOT格式）')
    parser.add_argument('--video', type=str, default=None, help='视频文件路径（可选）')
    parser.add_argument('--output', type=str, default='analysis_results', help='输出目录')
    args = parser.parse_args()

    analyzer = WorkerAnalyzer(args.tracks, args.video, args.output)
    analyzer.analyze()
    print(f"分析完成，结果保存在: {args.output}")


if __name__ == '__main__':
    main()
