#!/usr/bin/env python3
"""
测试有移动的工人数据，验证duration和avg_speed计算
"""

import os
import json
from worker_analysis import WorkerAnalyzer

def create_moving_workers_data():
    """创建有移动的工人数据"""
    print("创建有移动的工人数据...")
    
    tracks_data = []
    
    # 工人1: 从左到右移动 (100帧，从x=100移动到x=500)
    for frame in range(1, 101):
        x = 100 + (frame - 1) * 4  # 每帧移动4像素
        y = 200
        tracks_data.append([frame, 1, x, y, 50, 100, 0.8, 0, 1])
    
    # 工人2: 从上到下移动 (150帧，从y=100移动到y=400)
    for frame in range(50, 201):
        x = 300
        y = 100 + (frame - 50) * 2  # 每帧移动2像素
        tracks_data.append([frame, 2, x, y, 50, 100, 0.9, 0, 1])
    
    # 工人3: 对角线移动 (80帧)
    for frame in range(20, 101):
        x = 200 + (frame - 20) * 3  # 每帧移动3像素
        y = 150 + (frame - 20) * 2  # 每帧移动2像素
        tracks_data.append([frame, 3, x, y, 50, 100, 0.7, 0, 1])
    
    # 保存到文件
    tracks_file = "moving_workers_tracks.txt"
    with open(tracks_file, 'w') as f:
        for track in tracks_data:
            f.write(' '.join(map(str, track)) + '\n')
    
    print(f"移动工人tracks文件已创建: {tracks_file}")
    print(f"包含 {len(tracks_data)} 条跟踪记录")
    
    # 计算预期的移动距离
    print("\n预期移动距离:")
    print(f"工人1: 水平移动 400像素 (100帧 * 4像素/帧)")
    print(f"工人2: 垂直移动 300像素 (150帧 * 2像素/帧)")
    print(f"工人3: 对角线移动 {((80*3)**2 + (80*2)**2)**0.5:.1f}像素")
    
    return tracks_file

def test_moving_workers():
    """测试有移动的工人数据"""
    print("=" * 60)
    print("测试有移动的工人数据")
    print("=" * 60)
    
    # 创建移动数据
    tracks_file = create_moving_workers_data()
    video_file = "image/测试基准视频.mp4"
    output_dir = "test_moving_workers_output"
    
    try:
        # 创建分析器
        print("\n创建WorkerAnalyzer实例...")
        analyzer = WorkerAnalyzer(tracks_file, video_file, output_dir)
        
        # 手动设置视频信息（模拟30 FPS）
        analyzer.video_info = {
            'fps': 30.0,
            'frame_count': 200,
            'width': 1920,
            'height': 1080,
            'duration': 6.67
        }
        
        print("设置模拟视频信息: 30 FPS")
        
        # 运行分析
        print("\n开始分析...")
        analyzer.analyze()
        
        print("\n✅ 移动工人测试成功！")
        
        # 检查生成的统计数据
        stats_file = os.path.join(output_dir, "worker_stats.json")
        if os.path.exists(stats_file):
            with open(stats_file, 'r', encoding='utf-8') as f:
                stats = json.load(f)
            
            print("\n工人统计结果:")
            print("-" * 40)
            for worker_id, data in stats.items():
                duration = data['duration']
                distance = data['total_distance']
                speed = data['avg_speed']
                frames = data['last_frame'] - data['first_frame'] + 1
                
                print(f"工人 {worker_id}:")
                print(f"  帧范围: {data['first_frame']}-{data['last_frame']} ({frames}帧)")
                print(f"  持续时间: {duration:.2f}秒")
                print(f"  移动距离: {distance:.1f}像素")
                print(f"  平均速度: {speed:.1f}像素/秒")
                print()
        
        # 检查生成的文件
        print("检查生成的文件:")
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            for file in sorted(files):
                file_path = os.path.join(output_dir, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  📄 {file} - {size} bytes")
        
        # 清理临时文件
        if os.path.exists(tracks_file):
            os.remove(tracks_file)
            print(f"\n清理临时文件: {tracks_file}")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_moving_workers()
