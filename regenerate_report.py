import os
import json
import sys
from worker_analysis import <PERSON>A<PERSON><PERSON><PERSON>

def regenerate_html_report(analysis_dir):
    """
    重新生成HTML报告

    参数:
        analysis_dir (str): 分析结果目录路径
    """
    # 检查目录是否存在
    if not os.path.exists(analysis_dir):
        print(f"错误: 目录不存在: {analysis_dir}")
        return False

    # 检查JSON文件是否存在
    json_file = os.path.join(analysis_dir, 'worker_stats.json')
    if not os.path.exists(json_file):
        print(f"错误: 工人统计数据文件不存在: {json_file}")
        return False

    # 创建临时跟踪文件
    temp_tracks_file = os.path.join(analysis_dir, 'temp_tracks.txt')

    # 创建一个空的临时跟踪文件
    with open(temp_tracks_file, 'w') as f:
        f.write("1 1 100 100 50 50 1 -1 -1 -1\n")

    try:
        # 查找视频文件
        video_file = None
        parent_dir = os.path.dirname(analysis_dir)

        # 在父目录中查找视频文件
        for file in os.listdir(parent_dir):
            if file.endswith(('.mp4', '.avi', '.mov', '.mkv')):
                video_file = os.path.join(parent_dir, file)
                break

        # 创建分析器
        analyzer = WorkerAnalyzer(temp_tracks_file, video_file, analysis_dir)

        # 从JSON文件加载工人统计数据
        with open(json_file, 'r', encoding='utf-8') as f:
            analyzer.worker_stats = json.load(f)

        # 如果找到了视频文件，重新计算duration和avg_speed
        if video_file and analyzer.video_info:
            fps = analyzer.video_info['fps']
            for worker_id, stats in analyzer.worker_stats.items():
                # 重新计算duration
                first_frame = stats['first_frame']
                last_frame = stats['last_frame']
                duration = (last_frame - first_frame) / fps if fps > 0 else 0

                # 重新计算avg_speed
                total_distance = stats['total_distance']
                avg_speed = total_distance / duration if duration > 0 else 0

                # 更新统计数据
                analyzer.worker_stats[worker_id]['duration'] = duration
                analyzer.worker_stats[worker_id]['avg_speed'] = avg_speed

        # 生成HTML报告
        html_report = analyzer._generate_html_report()

        # 保存HTML报告
        html_file = os.path.join(analysis_dir, 'worker_report.html')
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_report)

        print(f"HTML报告已重新生成: {html_file}")
        return True

    except Exception as e:
        print(f"重新生成HTML报告时出错: {e}")
        return False

    finally:
        # 删除临时文件
        if os.path.exists(temp_tracks_file):
            os.remove(temp_tracks_file)

if __name__ == '__main__':
    if len(sys.argv) < 2:
        print("用法: python regenerate_report.py <分析结果目录路径>")
        print("例如: python regenerate_report.py runs/track/exp/analysis")
        sys.exit(1)

    analysis_dir = sys.argv[1]
    regenerate_html_report(analysis_dir)
