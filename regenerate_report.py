import os
import json
import sys
from worker_analysis import <PERSON><PERSON><PERSON><PERSON><PERSON>

def regenerate_html_report(analysis_dir):
    """
    重新生成HTML报告
    
    参数:
        analysis_dir (str): 分析结果目录路径
    """
    # 检查目录是否存在
    if not os.path.exists(analysis_dir):
        print(f"错误: 目录不存在: {analysis_dir}")
        return False
    
    # 检查JSON文件是否存在
    json_file = os.path.join(analysis_dir, 'worker_stats.json')
    if not os.path.exists(json_file):
        print(f"错误: 工人统计数据文件不存在: {json_file}")
        return False
    
    # 创建临时跟踪文件
    temp_tracks_file = os.path.join(analysis_dir, 'temp_tracks.txt')
    
    # 创建一个空的临时跟踪文件
    with open(temp_tracks_file, 'w') as f:
        f.write("1 1 100 100 50 50 1 -1 -1 -1\n")
    
    try:
        # 创建分析器
        analyzer = WorkerAnalyzer(temp_tracks_file, None, analysis_dir)
        
        # 从JSON文件加载工人统计数据
        with open(json_file, 'r', encoding='utf-8') as f:
            analyzer.worker_stats = json.load(f)
        
        # 生成HTML报告
        html_report = analyzer._generate_html_report()
        
        # 保存HTML报告
        html_file = os.path.join(analysis_dir, 'worker_report.html')
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_report)
        
        print(f"HTML报告已重新生成: {html_file}")
        return True
    
    except Exception as e:
        print(f"重新生成HTML报告时出错: {e}")
        return False
    
    finally:
        # 删除临时文件
        if os.path.exists(temp_tracks_file):
            os.remove(temp_tracks_file)

if __name__ == '__main__':
    if len(sys.argv) < 2:
        print("用法: python regenerate_report.py <分析结果目录路径>")
        print("例如: python regenerate_report.py runs/track/exp/analysis")
        sys.exit(1)
    
    analysis_dir = sys.argv[1]
    regenerate_html_report(analysis_dir)
