model:
  name: 'osnet_ain_x1_0'
  pretrained: True

data:
  type: 'image'
  sources: ['market1501']
  targets: ['market1501', 'dukemtmcreid']
  height: 256
  width: 128
  combineall: False
  transforms: ['random_flip', 'color_jitter']
  save_dir: 'log/osnet_ain_x1_0_market1501_softmax_cosinelr'

loss:
  name: 'softmax'
  softmax:
    label_smooth: True

train:
  optim: 'amsgrad'
  lr: 0.0015
  max_epoch: 100
  batch_size: 64
  fixbase_epoch: 10
  open_layers: ['classifier']
  lr_scheduler: 'cosine'

test:
  batch_size: 300
  dist_metric: 'cosine'
  normalize_feature: False
  evaluate: False
  eval_freq: -1
  rerank: False