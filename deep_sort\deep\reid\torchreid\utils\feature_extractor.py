from __future__ import absolute_import
import numpy as np
import torch
import torch.nn as nn
import torchvision.transforms as T
from PIL import Image

from torchreid.utils import (
    check_isfile, load_pretrained_weights, compute_model_complexity
)
from torchreid.models import build_model


class FeatureExtractor(object):
    """A feature extractor for person re-identification with enhancements for construction sites.

    Args:
        model_name (str): model name.
        model_path (str): path to model weights.
        image_size (sequence or int): image height and width.
        pixel_mean (list): pixel mean for normalization.
        pixel_std (list): pixel std for normalization.
        pixel_norm (bool): whether to normalize pixels.
        device (str): 'cpu' or 'cuda' (could be specific gpu devices).
        verbose (bool): show model details.
        augment (bool): use data augmentation for robust feature extraction.
        use_worker_head (bool): replace classifier with worker-specific head.
        multiscale (bool): use multi-scale feature extraction.
        quantize (bool): use quantization for faster inference.
    """

    def __init__(
        self,
        model_name='',
        model_path='',
        image_size=(256, 128),
        pixel_mean=[0.485, 0.456, 0.406],
        pixel_std=[0.229, 0.224, 0.225],
        pixel_norm=True,
        device='cuda',
        verbose=True,
        augment=False,
        use_worker_head=False,
        multiscale=False,
        quantize=False
    ):
        # Build model
        model = build_model(
            model_name,
            num_classes=1,
            pretrained=not (model_path and check_isfile(model_path)),
            use_gpu=device.startswith('cuda')
        )
        
        # Modify model for worker tracking
        if use_worker_head:
            if verbose:
                print("Using worker-specific feature head")
            # Replace classifier with worker-specific embedding layer
            num_features = model.classifier.in_features
            model.classifier = nn.Sequential(
                nn.Linear(num_features, 512),
                nn.BatchNorm1d(512),
                nn.ReLU(inplace=True),
                nn.Dropout(0.5),
                nn.Linear(512, 256)  # Output dimensions optimised for worker characteristics
            )
        
        model.eval()

        if verbose:
            num_params, flops = compute_model_complexity(
                model, (1, 3, image_size[0], image_size[1])
            )
            print('Model: {}'.format(model_name))
            print('- params: {:,}'.format(num_params))
            print('- flops: {:,}'.format(flops))

        if model_path and check_isfile(model_path):
            load_pretrained_weights(model, model_path)

        # Build transform functions with optional augmentation
        transforms = []
        if augment:
            transforms += [
                T.RandomBrightnessContrast(p=0.5),  # Brightness/Contrast Adjustment
                T.RandomGaussianBlur(kernel_size=3, sigma=(0.1, 2.0)),  # analogue blur
                T.RandomErasing(p=0.5, scale=(0.02, 0.2), ratio=(0.3, 3.3)),  # Simulated masking        
                T.RandomHorizontalFlip(p=0.5),
                T.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
                T.RandomRotation(10),
            ]
        transforms += [T.Resize(image_size)]
        transforms += [T.ToTensor()]
        if pixel_norm:
            transforms += [T.Normalize(mean=pixel_mean, std=pixel_std)]
        self.preprocess = T.Compose(transforms)

        self.to_pil = T.ToPILImage()
        self.device = torch.device(device)
        self.model = model.to(self.device)
        
        # Optimization flags
        self.multiscale = multiscale
        self.scales = [1.0, 0.75, 1.25]  # Multi-scale factors
        
        # Apply quantization if specified
        if quantize and self.device.type == 'cpu':
            self.model = torch.quantization.quantize_dynamic(
                self.model, {nn.Linear}, dtype=torch.qint8
            )
            print("Model quantized for CPU inference")

    def extract_multiscale(self, images):
        """Extract features from multiple scales"""
        features = []
        for scale in self.scales:
            if scale != 1.0:
                size = (int(images.shape[2] * scale), int(images.shape[3] * scale))
                scaled_images = torch.nn.functional.interpolate(
                    images, size=size, mode='bilinear', align_corners=False
                )
            else:
                scaled_images = images
                
            with torch.no_grad():
                scale_features = self.model(scaled_images)
            features.append(scale_features)
            
        # Feature fusion(拼接)
        return torch.cat(features, dim=1)
        ## Feature fusion (average pooling)
        # return torch.mean(torch.stack(features, dim=0), dim=0)

    def l2_normalize(self, features):
        """L2-normalize feature vectors"""
        return torch.nn.functional.normalize(features, p=2, dim=1)

    def __call__(self, input, normalize=True):
        """Extract features from input.
        
        Args:
            input: Image or batch of images. Can be:
                - str: Path to image
                - list: List of paths to images
                - numpy.ndarray: Image array
                - torch.Tensor: Image tensor
            normalize (bool): Apply L2 normalization to features
        """
        if isinstance(input, list):
            images = []

            for element in input:
                if isinstance(element, str):
                    image = Image.open(element).convert('RGB')
                elif isinstance(element, np.ndarray):
                    image = self.to_pil(element)
                else:
                    raise TypeError(
                        'Type of each element must be str or numpy.ndarray'
                    )

                image = self.preprocess(image)
                images.append(image)

            images = torch.stack(images, dim=0)
            images = images.to(self.device)

        elif isinstance(input, str):
            image = Image.open(input).convert('RGB')
            image = self.preprocess(image)
            images = image.unsqueeze(0).to(self.device)

        elif isinstance(input, np.ndarray):
            image = self.to_pil(input)
            image = self.preprocess(image)
            images = image.unsqueeze(0).to(self.device)

        elif isinstance(input, torch.Tensor):
            if input.dim() == 3:
                input = input.unsqueeze(0)
            images = input.to(self.device)

        else:
            raise NotImplementedError

        with torch.no_grad():
            if self.multiscale:
                features = self.extract_multiscale(images)
            else:
                features = self.model(images)

        if normalize:
            features = self.l2_normalize(features)

        return features.cpu()  # Return features on CPU for compatibility