# Performance Tracking User Guide

This document introduces the new performance tracking feature that can record detailed time consumption of YOLO detection, DeepSORT tracking and other modules, and generate Excel reports and visualization charts.

## 🚀 功能特性

### 📊 详细时间统计
- **YOLO检测时间**: 记录每帧YOLO模型的推理时间
- **DeepSORT跟踪时间**: 记录每帧DeepSORT的特征提取和跟踪时间
- **其他操作时间**: 记录工人监控、视频保存等其他操作的时间
- **总处理时间**: 记录每帧的总处理时间
- **实时FPS**: 计算实时的帧率性能

### 📈 数据分析
- **时间占比分析**: 分析各模块的时间占比
- **性能瓶颈识别**: 自动识别主要的性能瓶颈
- **趋势分析**: 分析性能随时间的变化趋势
- **统计摘要**: 提供平均值、最大值、最小值等统计信息

### 📋 报告生成
- **Excel报告**: 详细的数据表格和统计摘要
- **可视化图表**: 多种图表展示性能数据
- **实时监控**: 运行过程中的实时性能反馈

## 🛠️ 使用方法

### 启用性能统计

在运行跟踪程序时添加 `--enable-performance-tracking` 参数：

```bash
python track.py --source image/测试基准视频_2.mp4 \
                --yolo_model yolov5/best_22.pt \
                --deep_sort_model osnet_x0_25 \
                --enable-performance-tracking \
                --show-vid --save-vid --enable-worker-monitor
```

### 输出文件

启用性能统计后，会在输出目录下创建 `performance` 文件夹，包含：

```
runs/track/exp/performance/
├── performance_report_20241201_143022.xlsx    # Excel数据报告
└── performance_plots_20241201_143022.png      # 性能图表
```

## 📊 Excel报告内容

### 详细数据表 (Sheet: 详细数据)

| 列名 | 说明 | 单位 |
|------|------|------|
| frame_id | 帧编号 | - |
| timestamp | 时间戳 | - |
| yolo_time | YOLO检测时间 | ms |
| deepsort_time | DeepSORT跟踪时间 | ms |
| other_time | 其他操作时间 | ms |
| total_time | 总处理时间 | ms |
| yolo_percentage | YOLO时间占比 | % |
| deepsort_percentage | DeepSORT时间占比 | % |
| other_percentage | 其他操作时间占比 | % |
| fps | 实时FPS | fps |

### 统计摘要表 (Sheet: 统计摘要)

包含以下统计指标：
- 总帧数、总时长、平均FPS
- 各模块的平均时间、时间占比
- 各模块的最大时间、最小时间
- 性能瓶颈分析

## 📈 可视化图表

生成的图表包含6个子图：

### 1. 时间消耗趋势图
- 显示YOLO、DeepSORT和总时间随帧数的变化
- 帮助识别性能波动和异常

### 2. 时间占比饼图
- 显示各模块的平均时间占比
- 直观展示性能瓶颈

### 3. FPS趋势图
- 显示实时FPS的变化
- 包含平均FPS基准线

### 4. 时间分布直方图
- 显示YOLO和DeepSORT时间的分布
- 帮助了解性能的稳定性

### 5. 性能对比柱状图
- 对比各模块的平均耗时
- 包含具体数值标签

### 6. 滑动平均趋势
- 显示平滑后的性能趋势
- 减少噪声，突出长期趋势

## 🔧 高级功能

### 实时性能监控

程序运行时会显示实时性能信息：

```
image 1/164 D:\...\测试基准视频_2.mp4: 384x640 6 workers, Done. YOLO:(0.087s), DeepSort:(1.893s), 实时FPS:15.2
```

### 定期统计输出

每100帧会输出详细的性能统计：

```
============================================================
性能统计摘要
============================================================
测试时间: 2024-12-01 14:30:22
总时长: 45.67秒
总帧数: 164
平均FPS: 3.59

各模块平均耗时:
  YOLO检测: 87.2ms (8.5%)
  DeepSORT跟踪: 893.4ms (87.2%)
  其他操作: 44.1ms (4.3%)
  总计: 1024.7ms

性能瓶颈分析:
  主要瓶颈: DeepSORT跟踪
============================================================
```

### 自定义操作计时

可以为自定义操作添加计时：

```python
from performance_tracker import TimingContext

# 使用上下文管理器
with TimingContext(performance_tracker, "custom_operation"):
    # 执行自定义操作
    custom_function()

# 或者手动添加
performance_tracker.add_operation_time("custom_operation", duration_seconds)
```

## 📋 性能优化建议

根据统计结果，系统会自动识别性能瓶颈：

### 如果YOLO是瓶颈
- 考虑使用更小的模型（如YOLOv5s）
- 降低输入分辨率
- 启用半精度推理

### 如果DeepSORT是瓶颈
- 使用优化的DeepSORT配置
- 考虑模型剪枝
- 减少特征库大小
- 启用跳帧策略

### 如果其他操作是瓶颈
- 优化工人监控算法
- 减少视频保存的分辨率
- 优化图像处理操作

## 🧪 测试功能

可以使用测试脚本验证性能统计功能：

```bash
python test_performance_tracking.py
```

这会生成模拟的性能数据，用于验证统计和可视化功能。

## ⚙️ 配置选项

### 性能跟踪器参数

```python
performance_tracker = PerformanceTracker(
    save_dir="performance_logs",    # 保存目录
    window_size=100                 # 滑动窗口大小
)
```

### 图表样式

可以修改 `performance_tracker.py` 中的图表样式：
- 颜色主题
- 图表大小
- 字体设置
- 图例位置

## 🔍 故障排除

### 常见问题

1. **导入错误**
   ```
   ModuleNotFoundError: No module named 'performance_tracker'
   ```
   确保 `performance_tracker.py` 在项目根目录

2. **图表生成失败**
   ```
   生成性能图表时出错: No module named 'matplotlib'
   ```
   安装必要的依赖：
   ```bash
   pip install matplotlib pandas seaborn openpyxl
   ```

3. **中文字体问题**
   如果图表中文显示异常，修改 `performance_tracker.py` 中的字体设置

### 性能影响

启用性能统计会有轻微的性能开销（约1-2%），主要来自：
- 时间戳记录
- 数据存储
- 统计计算

如果对性能要求极高，可以：
- 增大统计输出间隔
- 减少滑动窗口大小
- 禁用实时图表生成

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本兼容性（推荐3.7+）
2. 依赖包版本
3. 磁盘空间（用于保存报告）
4. 权限设置（文件写入权限）
