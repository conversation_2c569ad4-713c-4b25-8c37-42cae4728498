
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型剪枝实验报告 - pruning_exp_20250604_213735</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .section { margin-bottom: 30px; }
        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .metric-card { background: #f9f9f9; padding: 15px; border-radius: 5px; text-align: center; }
        .improvement { color: green; font-weight: bold; }
        .degradation { color: red; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .plot { text-align: center; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 模型剪枝实验报告</h1>
        <p><strong>实验ID:</strong> pruning_exp_20250604_213735</p>
        <p><strong>开始时间:</strong> 2025-06-04T21:37:35.391666</p>
        <p><strong>结束时间:</strong> 2025-06-04T21:37:48.300383</p>
    </div>

    <div class="section">
        <h2>📊 性能提升概览</h2>
        <div class="metrics">
        
            <div class="metric-card">
                <h3>推理速度提升</h3>
                <p class="improvement">+0.5%</p>
            </div>
            
            <div class="metric-card">
                <h3>推理时间减少</h3>
                <p class="improvement">+0.5%</p>
            </div>
            
            <div class="metric-card">
                <h3>模型大小减少</h3>
                <p class="degradation">-97.5%</p>
            </div>
            
        </div>
    </div>

    <div class="section">
        <h2>🔧 剪枝配置</h2>
        <table>
            <tr><th>参数</th><th>值</th></tr>
        
            <tr><td>剪枝方法</td><td>sparse_pruning_v2</td></tr>
        <tr><td>percent</td><td>0.25</td></tr><tr><td>method</td><td>BN_weight_based_improved</td></tr><tr><td>device</td><td>cpu</td></tr>
        </table>
    </div>

    <div class="section">
        <h2>📈 性能对比</h2>
        <table>
            <tr><th>指标</th><th>剪枝前</th><th>剪枝后</th><th>变化</th></tr>
        
            <tr>
                <td>推理速度 (FPS)</td>
                <td>19.17</td>
                <td>19.26</td>
                <td>+0.09</td>
            </tr>
            
            <tr>
                <td>推理时间 (ms)</td>
                <td>52.17</td>
                <td>51.92</td>
                <td>-0.25</td>
            </tr>
            
        </table>
    </div>

    <div class="plot">
        <h2>📊 性能对比图表</h2>
        <img src="../plots/pruning_exp_20250604_213735_performance.png" alt="性能对比图表" style="max-width: 100%; height: auto;">
    </div>

    <div class="section">
        <h2>📝 备注</h2>
        <p>新稀疏剪枝 15.0% 完成，设备: cpu，模型: new_sparse_pruned_yolo_15percent.pt
新稀疏剪枝 25.0% 完成，设备: cpu，模型: new_sparse_pruned_yolo_25percent.pt</p>
    </div>

</body>
</html>
        