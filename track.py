# limit the number of cpus used by high performance libraries
import os
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["VECLIB_MAXIMUM_THREADS"] = "1"
os.environ["NUMEXPR_NUM_THREADS"] = "1"

import sys
sys.path.insert(0, './yolov5')

import argparse
import os
import platform
import shutil
import time
from pathlib import Path
import numpy as np
import cv2
import torch
import torch.backends.cudnn as cudnn
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime, timedelta
import json
from worker_monitor import WorkerMonitor
from worker_analysis import WorkerAnalyzer
from performance_tracker import PerformanceTracker, TimingContext

from yolov5.models.experimental import attempt_load
from yolov5.utils.downloads import attempt_download
from yolov5.models.common import DetectMultiBackend
from yolov5.utils.datasets import LoadImages, LoadStreams, VID_FORMATS
from yolov5.utils.general import (LOGGER, check_img_size, non_max_suppression, scale_coords,
                                  check_imshow, xyxy2xywh, increment_path, strip_optimizer, colorstr)
from yolov5.utils.torch_utils import select_device, time_sync
from yolov5.utils.plots import Annotator, colors, save_one_box
from deep_sort.utils.parser import get_config
from deep_sort.deep_sort import DeepSort

FILE = Path(__file__).resolve()
ROOT = FILE.parents[0]  # yolov5 deepsort root directory
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))  # add ROOT to PATH
ROOT = Path(os.path.relpath(ROOT, Path.cwd()))  # relative


def detect(opt):
    out, source, yolo_model, deep_sort_model, show_vid, save_vid, save_txt, imgsz, evaluate, half, \
        project, exist_ok, update, save_crop, enable_worker_monitor, enable_worker_analysis = \
        opt.output, opt.source, opt.yolo_model, opt.deep_sort_model, opt.show_vid, opt.save_vid, \
        opt.save_txt, opt.imgsz, opt.evaluate, opt.half, opt.project, opt.exist_ok, opt.update, opt.save_crop, \
        opt.enable_worker_monitor, opt.enable_worker_analysis
    webcam = source == '0' or source.startswith(
        'rtsp') or source.startswith('http') or source.endswith('.txt')

    # Initialize
    device = select_device(opt.device)
    half &= device.type != 'cpu'  # half precision only supported on CUDA

    # The MOT16 evaluation runs multiple inference streams in parallel, each one writing to
    # its own .txt file. Hence, in that case, the output folder is not restored
    if not evaluate:
        if os.path.exists(out):
            pass
            shutil.rmtree(out)  # delete output folder
        os.makedirs(out)  # make new output folder

    # Directories
    if type(yolo_model) is str:  # single yolo model
        exp_name = yolo_model.split(".")[0]
    elif type(yolo_model) is list and len(yolo_model) == 1:  # single models after --yolo_model
        exp_name = yolo_model[0].split(".")[0]
    else:  # multiple models after --yolo_model
        exp_name = "ensemble"
    exp_name = exp_name + "_" + deep_sort_model.split('/')[-1].split('.')[0]
    save_dir = increment_path(Path(project) / exp_name, exist_ok=exist_ok)  # increment run if project name exists
    (save_dir / 'tracks' if save_txt else save_dir).mkdir(parents=True, exist_ok=True)  # make dir

    # Load model
    model = DetectMultiBackend(yolo_model, device=device, dnn=opt.dnn)
    stride, names, pt = model.stride, model.names, model.pt
    imgsz = check_img_size(imgsz, s=stride)  # check image size

    # Half
    half &= pt and device.type != 'cpu'  # half precision only supported by PyTorch on CUDA
    if pt:
        model.model.half() if half else model.model.float()

    # Set Dataloader
    vid_path, vid_writer = None, None
    # Check if environment supports image displays
    if show_vid:
        show_vid = check_imshow()

    # Dataloader
    if webcam:
        show_vid = check_imshow()
        cudnn.benchmark = True  # set True to speed up constant image size inference
        dataset = LoadStreams(source, img_size=imgsz, stride=stride, auto=pt)
        nr_sources = len(dataset)
    else:
        dataset = LoadImages(source, img_size=imgsz, stride=stride, auto=pt)
        nr_sources = 1
    vid_path, vid_writer, txt_path = [None] * nr_sources, [None] * nr_sources, [None] * nr_sources

    # initialize deepsort
    cfg = get_config()
    cfg.merge_from_file(opt.config_deepsort)

    # Create as many trackers as there are video sources
    deepsort_list = []
    for i in range(nr_sources):
        deepsort_list.append(
            DeepSort(
                deep_sort_model,
                device,
                max_dist=cfg.DEEPSORT.MAX_DIST,
                max_iou_distance=cfg.DEEPSORT.MAX_IOU_DISTANCE,
                max_age=cfg.DEEPSORT.MAX_AGE, n_init=cfg.DEEPSORT.N_INIT, nn_budget=cfg.DEEPSORT.NN_BUDGET,
            )
        )
    outputs = [None] * nr_sources

    # Get names and colors
    names = model.module.names if hasattr(model, 'module') else model.names

    # Run tracking
    model.warmup(imgsz=(1 if pt else nr_sources, 3, *imgsz))  # warmup
    dt, seen = [0.0, 0.0, 0.0, 0.0], 0

    # 初始化性能跟踪器
    performance_tracker = None
    if opt.enable_performance_tracking:
        performance_tracker = PerformanceTracker(save_dir=str(save_dir / 'performance'))
        print("Performance tracking enabled, will record YOLO and DeepSORT time consumption")

    # 初始化工人监控器
    worker_monitors = []

    # 初始化工人分析器
    worker_analyzers = []
    worker_tracks_data = []  # 用于存储跟踪数据，格式：[frame_idx, track_id, x, y, w, h, conf, -1, -1, -1]

    # 获取视频帧率（用于计算实际时间）
    video_fps = 30.0  # 默认帧率
    if not webcam and hasattr(dataset, 'cap'):
        video_fps = dataset.cap.get(cv2.CAP_PROP_FPS)
        if video_fps <= 0:
            video_fps = 30.0  # 如果获取失败，使用默认值

    print(f"视频帧率: {video_fps} FPS")

    if enable_worker_monitor:
        for i in range(nr_sources):
            # 定义危险区域和禁止区域（示例，实际应根据场景定义）
            # 解析危险区域坐标
            danger_zones = []
            if opt.danger_zone:
                try:
                    # 解析格式: "x1,y1,x2,y2,x3,y3,..."
                    coords = list(map(int, opt.danger_zone.split(',')))
                    # 将坐标转换为点列表 [(x1,y1), (x2,y2), ...]
                    points = [(coords[j], coords[j+1]) for j in range(0, len(coords), 2)]
                    danger_zones = [points]
                except Exception as e:
                    print(f"解析危险区域坐标出错: {e}")
                    # 使用默认危险区域
                    danger_zones = [[(233,309), (892, 309), (892, 1006), (233, 1006)]] # [[(100, 100), (300, 100), (300, 300), (100, 300)]]
            else:
                # 使用默认危险区域
                danger_zones = [[(233,309), (892, 309), (892, 1006), (233, 1006)]]

            # 解析禁止区域坐标
            restricted_areas = []
            if opt.restricted_area:
                try:
                    # 解析格式: "x1,y1,x2,y2,x3,y3,..."
                    coords = list(map(int, opt.restricted_area.split(',')))
                    # 将坐标转换为点列表 [(x1,y1), (x2,y2), ...]
                    points = [(coords[j], coords[j+1]) for j in range(0, len(coords), 2)]
                    restricted_areas = [points]
                except Exception as e:
                    print(f"解析禁止区域坐标出错: {e}")
                    # 使用默认禁止区域
                    restricted_areas = [[(722, 14), (955, 14), (955, 181), (722, 181)], [(1306, 661), (1564, 661), (1564, 960), (1306, 960)]] # 定义两个禁止区域
            else:
                # 使用默认禁止区域
                restricted_areas = [[(722, 14), (955, 14), (955, 181), (722, 181)], [(1306, 661), (1564, 661), (1564, 960), (1306, 960)]] # 定义两个禁止区域

            # 获取视频/图像尺寸
            if webcam and hasattr(dataset, 'imgs') and i < len(dataset.imgs):
                h, w = dataset.imgs[i].shape[:2]
            elif not webcam and hasattr(dataset, 'cap'):
                # 从视频捕获对象获取尺寸
                w = int(dataset.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                h = int(dataset.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            else:
                # 使用默认尺寸或推理尺寸
                h, w = imgsz[0], imgsz[0]  # 假设图像是正方形的

            # 确保尺寸有效
            if h <= 0 or w <= 0:
                h, w = 640, 640  # 使用默认尺寸

            worker_monitors.append(WorkerMonitor(
                frame_width=w,
                frame_height=h,
                danger_zones=danger_zones,
                restricted_areas=restricted_areas,
                stationary_threshold=opt.stationary_threshold,  # 静止判定阈值
                stationary_time=opt.stationary_time,           # 静止时间阈值（秒）
                crowd_threshold=opt.crowd_threshold,           # 人群聚集人数阈值
                crowd_distance=opt.crowd_distance              # 人群聚集距离阈值
            ))
    for frame_idx, (path, im, im0s, vid_cap, s) in enumerate(dataset):
        # 开始帧计时
        if performance_tracker:
            performance_tracker.start_frame(frame_idx + 1)

        t1 = time_sync()
        im = torch.from_numpy(im).to(device)
        im = im.half() if half else im.float()  # uint8 to fp16/32
        im /= 255.0  # 0 - 255 to 0.0 - 1.0
        if len(im.shape) == 3:
            im = im[None]  # expand for batch dim
        t2 = time_sync()
        dt[0] += t2 - t1

        # 开始YOLO检测计时
        if performance_tracker:
            performance_tracker.start_yolo()

        # Inference
        visualize = increment_path(save_dir / Path(path[0]).stem, mkdir=True) if opt.visualize else False
        pred = model(im, augment=opt.augment, visualize=visualize)
        t3 = time_sync()
        dt[1] += t3 - t2

        # Apply NMS
        pred = non_max_suppression(pred, opt.conf_thres, opt.iou_thres, opt.classes, opt.agnostic_nms, max_det=opt.max_det)
        dt[2] += time_sync() - t3

        # 结束YOLO检测计时
        if performance_tracker:
            performance_tracker.end_yolo()



        # Process detections
        for i, det in enumerate(pred):  # detections per image
            seen += 1
            if webcam:  # nr_sources >= 1
                p, im0, _ = path[i], im0s[i].copy(), dataset.count
                p = Path(p)  # to Path
                s += f'{i}: '
                txt_file_name = p.name
                save_path = str(save_dir / p.name)  # im.jpg, vid.mp4, ...
            else:
                p, im0, _ = path, im0s.copy(), getattr(dataset, 'frame', 0)
                p = Path(p)  # to Path
                # video file
                if source.endswith(VID_FORMATS):
                    txt_file_name = p.stem
                    save_path = str(save_dir / p.name)  # im.jpg, vid.mp4, ...
                # folder with imgs
                else:
                    txt_file_name = p.parent.name  # get folder name containing current img
                    save_path = str(save_dir / p.parent.name)  # im.jpg, vid.mp4, ...

            txt_path = str(save_dir / 'tracks' / txt_file_name)  # im.txt
            s += '%gx%g ' % im.shape[2:]  # print string
            imc = im0.copy() if save_crop else im0  # for save_crop

            annotator = Annotator(im0, line_width=2, pil=not ascii)

            if det is not None and len(det):
                # Rescale boxes from img_size to im0 size
                det[:, :4] = scale_coords(im.shape[2:], det[:, :4], im0.shape).round()

                # Print results
                for c in det[:, -1].unique():
                    n = (det[:, -1] == c).sum()  # detections per class
                    s += f"{n} {names[int(c)]}{'s' * (n > 1)}, "  # add to string

                xywhs = xyxy2xywh(det[:, 0:4])
                confs = det[:, 4]
                clss = det[:, 5]

                # pass detections to deepsort
                t4 = time_sync()
                if performance_tracker:
                    performance_tracker.start_deepsort()
                outputs[i] = deepsort_list[i].update(xywhs.cpu(), confs.cpu(), clss.cpu(), im0)
                if performance_tracker:
                    performance_tracker.end_deepsort()
                t5 = time_sync()
                dt[3] += t5 - t4

                # draw boxes for visualization
                if len(outputs[i]) > 0:
                    for j, (output, conf) in enumerate(zip(outputs[i], confs)):

                        bboxes = output[0:4]
                        id = output[4]
                        cls = output[5]

                        # 准备MOT格式数据
                        bbox_left = output[0]
                        bbox_top = output[1]
                        bbox_w = output[2] - output[0]
                        bbox_h = output[3] - output[1]

                        if save_txt:
                            # Write MOT compliant results to file
                            with open(txt_path + '.txt', 'a') as f:
                                f.write(('%g ' * 10 + '\n') % (frame_idx + 1, id, bbox_left,  # MOT format
                                                               bbox_top, bbox_w, bbox_h, -1, -1, -1, i))

                        # 收集跟踪数据用于分析（无论是否保存txt文件）
                        if enable_worker_analysis:
                            worker_tracks_data.append([frame_idx + 1, id, bbox_left, bbox_top, bbox_w, bbox_h, conf, -1, -1, -1])

                        if save_vid or save_crop or show_vid:  # Add bbox to image
                            c = int(cls)  # integer class
                            label = f'{id} {names[c]} {conf:.2f}'
                            annotator.box_label(bboxes, label, color=colors(c, True))
                            if save_crop:
                                txt_file_name = txt_file_name if (isinstance(path, list) and len(path) > 1) else ''
                                save_one_box(bboxes, imc, file=save_dir / 'crops' / txt_file_name / names[c] / f'{id}' / f'{p.stem}.jpg', BGR=True)

                # 应用工人监控功能
                if enable_worker_monitor and len(outputs[i]) > 0:
                    # 计算视频时间（基于帧索引和帧率）
                    video_time = frame_idx / video_fps

                    # 记录工人监控时间
                    if performance_tracker:
                        with TimingContext(performance_tracker, "worker_monitor"):
                            # 将检测结果传递给工人监控器
                            im0, alerts = worker_monitors[i].update(im0, outputs[i], video_time)
                    else:
                        # 将检测结果传递给工人监控器
                        im0, alerts = worker_monitors[i].update(im0, outputs[i], video_time)

                    # 记录警报信息 (只在第一个视频源时输出，避免重复)
                    if alerts and i == 0:
                        for alert in alerts:
                            print(f"警报: {alert}")

                    # 每隔一段时间（例如5s）重置热力图 (只在第一个视频源时执行，避免重复)
                    if frame_idx % (30 * 5) == 0 and frame_idx > 0 and i == 0:  # 假设30fps，5秒
                        worker_monitors[i].reset_heatmap()

                    # 每隔一段时间（例如3s）输出工人统计信息 (只在第一个视频源时输出，避免重复)
                    if frame_idx % (30 * 3) == 0 and i == 0:  # 假设30fps，3秒
                        stats = worker_monitors[i].get_worker_statistics()
                        print(f"工人统计: {stats}")

                # 记录当前性能统计 (只在第一个视频源时输出，避免重复)
                if i == 0:  # 只在处理第一个视频源时输出日志
                    if performance_tracker:
                        current_stats = performance_tracker.get_current_stats()
                        if current_stats:
                            print(f'{s}Done. YOLO:({t3 - t2:.3f}s), DeepSort:({t5 - t4:.3f}s), 实时FPS:{current_stats["recent_fps"]:.1f}')
                        else:
                            print(f'{s}Done. YOLO:({t3 - t2:.3f}s), DeepSort:({t5 - t4:.3f}s)')
                    else:
                        print(f'{s}Done. YOLO:({t3 - t2:.3f}s), DeepSort:({t5 - t4:.3f}s)')

            else:
                deepsort_list[i].increment_ages()
                if i == 0:  # 只在处理第一个视频源时输出日志
                    print('No detections')

            # Stream results
            im0 = annotator.result()
            # 只在第一个视频源时显示，避免重复显示
            if i == 0:
                cv2.imshow(str(p), im0)
                cv2.waitKey(1)  # 1 millisecond
            # if show_vid:
            #     cv2.imshow(str(p), im0)
            #     cv2.waitKey(1)  # 1 millisecond

            # Save results (image with detections)
            if save_vid:
                if performance_tracker:
                    with TimingContext(performance_tracker, "video_save"):
                        if vid_path[i] != save_path:  # new video
                            vid_path[i] = save_path
                            if isinstance(vid_writer[i], cv2.VideoWriter):
                                vid_writer[i].release()  # release previous video writer
                            if vid_cap:  # video
                                fps = vid_cap.get(cv2.CAP_PROP_FPS)
                                w = int(vid_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                                h = int(vid_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                            else:  # stream
                                fps, w, h = 30, im0.shape[1], im0.shape[0]
                            save_path = str(Path(save_path).with_suffix('.mp4'))  # force *.mp4 suffix on results videos
                            vid_writer[i] = cv2.VideoWriter(save_path, cv2.VideoWriter_fourcc(*'mp4v'), fps, (w, h))
                        vid_writer[i].write(im0)
                else:
                    if vid_path[i] != save_path:  # new video
                        vid_path[i] = save_path
                        if isinstance(vid_writer[i], cv2.VideoWriter):
                            vid_writer[i].release()  # release previous video writer
                        if vid_cap:  # video
                            fps = vid_cap.get(cv2.CAP_PROP_FPS)
                            w = int(vid_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                            h = int(vid_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                        else:  # stream
                            fps, w, h = 30, im0.shape[1], im0.shape[0]
                        save_path = str(Path(save_path).with_suffix('.mp4'))  # force *.mp4 suffix on results videos
                        vid_writer[i] = cv2.VideoWriter(save_path, cv2.VideoWriter_fourcc(*'mp4v'), fps, (w, h))
                    vid_writer[i].write(im0)

            # 结束帧计时
            if performance_tracker:
                performance_tracker.end_frame()

                # 每100帧输出一次详细统计
                if frame_idx % 100 == 0 and frame_idx > 0:
                    performance_tracker.print_summary()

    # Print results
    t = tuple(x / seen * 1E3 for x in dt)  # speeds per image
    LOGGER.info(f'Speed: %.1fms pre-process, %.1fms inference, %.1fms NMS, %.1fms deep sort update \
        per image at shape {(1, 3, *imgsz)}' % t)

    s = f"\n{len(list(save_dir.glob('tracks/*.txt')))} tracks saved to {save_dir / 'tracks'}" if save_txt else ''
    LOGGER.info(f"Results saved to {colorstr('bold', save_dir)}{s}")

    # Generate performance report
    if performance_tracker:
        LOGGER.info("Generating performance statistics report...")
        performance_tracker.print_summary()

        # Save Excel report
        excel_path = performance_tracker.save_to_excel()
        LOGGER.info(f"Performance data saved to Excel: {excel_path}")

        # Generate performance charts
        try:
            performance_tracker.plot_performance(save_plots=True)
            LOGGER.info("Performance charts generated")
        except Exception as e:
            LOGGER.warning(f"Error generating performance charts: {e}")

    # 生成工人分析报告
    if enable_worker_analysis and len(worker_tracks_data) > 0:
        LOGGER.info("正在生成工人分析报告...")

        # 创建分析结果目录
        analysis_dir = save_dir / 'analysis'
        os.makedirs(analysis_dir, exist_ok=True)

        # 将跟踪数据保存为临时文件
        temp_tracks_file = os.path.join(analysis_dir, 'temp_tracks.txt')
        with open(temp_tracks_file, 'w') as f:
            for track in worker_tracks_data:
                f.write(' '.join(map(str, track)) + '\n')

        # 创建分析器并生成报告
        # 尝试获取视频文件路径
        video_file = None
        if isinstance(source, str) and os.path.isfile(source) and source.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
            video_file = source

        # 创建分析器，但不传入视频文件（避免视频文件路径问题）
        analyzer = WorkerAnalyzer(temp_tracks_file, None, str(analysis_dir))
        worker_stats = analyzer.analyze()

        # 输出分析结果摘要
        print(f"工人分析完成，共检测到 {len(worker_stats)} 名工人")
        print(f"分析报告保存在 {analysis_dir}")

        # 可选：删除临时文件
        if os.path.exists(temp_tracks_file):
            os.remove(temp_tracks_file)

    # if save_txt or save_vid:
    #     s = f"\n{len(list(save_dir.glob('tracks/*.txt')))} tracks saved to {save_dir / 'tracks'}" if save_txt else ''
    #     LOGGER.info(f"Results saved to {colorstr('bold', save_dir)}{s}")
    if update:
        strip_optimizer(yolo_model)  # update model (to fix SourceChangeWarning)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--yolo_model', nargs='+', type=str, default='yolov5/best.pt', help='model.pt path(s)') #    default='yolov5/yolov5m.pt' default='yolov5/runs/train/exp_myworker/weights/best.pt'
    parser.add_argument('--deep_sort_model', type=str, default='osnet_ibn_x1_0_MSMT17.pth')
    parser.add_argument('--source', type=str, default='./image', help='source')  # file/folder, 0 for webcam
    parser.add_argument('--output', type=str, default='inference/output', help='output folder')  # output folder
    parser.add_argument('--imgsz', '--img', '--img-size', nargs='+', type=int, default=[640], help='inference size h,w')
    parser.add_argument('--conf-thres', type=float, default=0.6
                        , help='object confidence threshold')
    parser.add_argument('--iou-thres', type=float, default=0.45, help='IOU threshold for NMS')
    parser.add_argument('--fourcc', type=str, default='mp4v', help='output video codec (verify ffmpeg support)')
    parser.add_argument('--device', default='', help='cuda device, i.e. 0 or 0,1,2,3 or cpu')
    parser.add_argument('--show-vid', action='store_true', help='display tracking video results')
    parser.add_argument('--save-vid', action='store_true', help='save video tracking results')
    parser.add_argument('--save-txt', action='store_true', help='save MOT compliant results to *.txt')
    # class 0 is person, 1 is bycicle, 2 is car... 79 is oven
    parser.add_argument('--classes', nargs='+', type=int, help='filter by class: --class 0, or --class 16 17')
    parser.add_argument('--agnostic-nms', action='store_true', help='class-agnostic NMS')
    parser.add_argument('--augment', action='store_true', help='augmented inference')
    parser.add_argument('--update', action='store_true', help='update all models')
    parser.add_argument('--evaluate', action='store_true', help='augmented inference')
    parser.add_argument("--config_deepsort", type=str, default="deep_sort/configs/deep_sort.yaml")
    parser.add_argument("--half", action="store_true", help="use FP16 half-precision inference")
    parser.add_argument('--visualize', action='store_true', help='visualize features')
    parser.add_argument('--max-det', type=int, default=1000, help='maximum detection per image')
    parser.add_argument('--save-crop', action='store_true', help='save cropped prediction boxes')
    parser.add_argument('--dnn', action='store_true', help='use OpenCV DNN for ONNX inference')
    parser.add_argument('--project', default=ROOT / 'runs/track', help='save results to project/name')
    parser.add_argument('--name', default='exp', help='save results to project/name')
    parser.add_argument('--exist-ok', action='store_true', help='existing project/name ok, do not increment')
    # 工人监控相关参数
    parser.add_argument('--enable-worker-monitor', action='store_true', help='启用工人监控功能')
    parser.add_argument('--danger-zone', type=str, default='', help='危险区域坐标，格式: x1,y1,x2,y2,x3,y3...')
    parser.add_argument('--restricted-area', type=str, default='', help='禁止区域坐标，格式: x1,y1,x2,y2,x3,y3...')
    parser.add_argument('--stationary-threshold', type=int, default=5, help='判定为静止的位移阈值（像素）')
    parser.add_argument('--stationary-time', type=int, default=15, help='判定工人静止不动的时间阈值（秒）')
    parser.add_argument('--crowd-threshold', type=int, default=3, help='判定为人群聚集的人数阈值')
    parser.add_argument('--crowd-distance', type=int, default=50, help='判定为人群聚集的距离阈值（像素）')

    # 工人分析相关参数
    parser.add_argument('--enable-worker-analysis', action='store_true', help='启用工人行为分析功能')

    # 性能统计相关参数
    parser.add_argument('--enable-performance-tracking', action='store_true', help='启用性能统计功能')
    opt = parser.parse_args()
    opt.imgsz *= 2 if len(opt.imgsz) == 1 else 1  # expand
    os.makedirs('./inference/output', exist_ok=True)


    with torch.no_grad():
        detect(opt)
