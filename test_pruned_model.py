#!/usr/bin/env python3
"""
测试剪枝后的YOLO模型
"""

import argparse
import torch
import torch.nn as nn
import time
import cv2
import numpy as np
import sys
from pathlib import Path

# 添加yolov5路径
sys.path.append('yolov5')

from yolov5.utils.torch_utils import select_device
from yolov5.utils.general import non_max_suppression, scale_coords, xyxy2xywh
from yolov5.utils.plots import Annotator, colors
from yolov5.utils.augmentations import letterbox

def load_pruned_model(weights, device):
    """加载剪枝后的模型"""
    print(f"加载剪枝后的模型: {weights}")
    
    ckpt = torch.load(weights, map_location=device)
    
    if 'model' in ckpt:
        model = ckpt['model']
    else:
        model = ckpt
    
    model = model.float().to(device).eval()
    
    print(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    return model

def preprocess_image(img_path, img_size=640):
    """预处理图像"""
    # 读取图像
    img0 = cv2.imread(str(img_path))
    assert img0 is not None, f'图像 {img_path} 未找到'
    
    # Letterbox
    img = letterbox(img0, img_size, stride=32, auto=True)[0]
    
    # Convert
    img = img.transpose((2, 0, 1))[::-1]  # HWC to CHW, BGR to RGB
    img = np.ascontiguousarray(img)
    
    return img, img0

def postprocess_detections(pred, img0, img, conf_thres=0.25, iou_thres=0.45, classes=None, agnostic_nms=False):
    """后处理检测结果"""
    # NMS
    pred = non_max_suppression(pred, conf_thres, iou_thres, classes, agnostic_nms, max_det=1000)
    
    detections = []
    for i, det in enumerate(pred):  # per image
        if len(det):
            # Rescale boxes from img_size to img0 size
            det[:, :4] = scale_coords(img.shape[1:], det[:, :4], img0.shape).round()
            
            # Results
            for *xyxy, conf, cls in reversed(det):
                detections.append({
                    'bbox': [int(x) for x in xyxy],
                    'confidence': float(conf),
                    'class': int(cls)
                })
    
    return detections

def draw_detections(img, detections, names):
    """在图像上绘制检测结果"""
    annotator = Annotator(img, line_width=3, example=str(names))
    
    for det in detections:
        bbox = det['bbox']
        conf = det['confidence']
        cls = det['class']
        
        # 绘制边界框
        label = f'{names[cls]} {conf:.2f}'
        annotator.box_label(bbox, label, color=colors(cls, True))
    
    return annotator.result()

def benchmark_model(model, device, img_size=640, num_iterations=100):
    """基准测试模型性能"""
    print(f"基准测试模型推理速度 (图像大小: {img_size}x{img_size})...")
    
    model.eval()
    
    # 创建随机输入
    dummy_input = torch.randn(1, 3, img_size, img_size).to(device)
    
    # 预热
    print("预热中...")
    with torch.no_grad():
        for _ in range(10):
            _ = model(dummy_input)
    
    # 计时
    print(f"开始计时 {num_iterations} 次迭代...")
    start_time = time.time()
    
    with torch.no_grad():
        for _ in range(num_iterations):
            _ = model(dummy_input)
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / num_iterations
    fps = 1.0 / avg_time
    
    print(f"推理速度: {fps:.2f} FPS")
    print(f"平均推理时间: {avg_time*1000:.2f} ms")
    print(f"总耗时: {total_time:.2f} 秒")
    
    return fps, avg_time

def test_single_image(model, img_path, device, img_size=640, conf_thres=0.25, iou_thres=0.45, save_path=None):
    """测试单张图像"""
    print(f"测试图像: {img_path}")
    
    # 预处理
    img, img0 = preprocess_image(img_path, img_size)
    img = torch.from_numpy(img).to(device)
    img = img.float() / 255.0  # 0 - 255 to 0.0 - 1.0
    if img.ndimension() == 3:
        img = img.unsqueeze(0)
    
    # 推理
    start_time = time.time()
    with torch.no_grad():
        pred = model(img)
    inference_time = time.time() - start_time
    
    # 后处理
    detections = postprocess_detections(pred, img0, img, conf_thres, iou_thres)
    
    print(f"推理时间: {inference_time*1000:.2f} ms")
    print(f"检测到 {len(detections)} 个目标")
    
    # 绘制结果
    names = {0: 'person'}  # 假设只检测人
    result_img = draw_detections(img0.copy(), detections, names)
    
    # 保存结果
    if save_path:
        cv2.imwrite(str(save_path), result_img)
        print(f"结果已保存到: {save_path}")
    
    return detections, inference_time, result_img

def compare_models(original_weights, pruned_weights, device, img_size=640):
    """比较原始模型和剪枝后模型的性能"""
    print("=" * 60)
    print("模型性能比较")
    print("=" * 60)
    
    # 加载原始模型
    print("\n加载原始模型...")
    try:
        original_ckpt = torch.load(original_weights, map_location=device)
        if 'model' in original_ckpt:
            original_model = original_ckpt['model']
        elif 'ema' in original_ckpt:
            original_model = original_ckpt['ema']
        else:
            original_model = original_ckpt
        original_model = original_model.float().to(device).eval()
        original_params = sum(p.numel() for p in original_model.parameters())
        print(f"原始模型参数量: {original_params:,}")
        
        # 基准测试原始模型
        original_fps, original_time = benchmark_model(original_model, device, img_size, 50)
    except Exception as e:
        print(f"加载原始模型失败: {e}")
        original_params = 0
        original_fps = 0
        original_time = 0
    
    # 加载剪枝后模型
    print("\n加载剪枝后模型...")
    pruned_model = load_pruned_model(pruned_weights, device)
    pruned_params = sum(p.numel() for p in pruned_model.parameters())
    
    # 基准测试剪枝后模型
    pruned_fps, pruned_time = benchmark_model(pruned_model, device, img_size, 50)
    
    # 比较结果
    print("\n" + "=" * 60)
    print("性能比较结果")
    print("=" * 60)
    if original_params > 0:
        param_reduction = (1 - pruned_params / original_params) * 100
        speed_improvement = (pruned_fps / original_fps - 1) * 100 if original_fps > 0 else 0
        
        print(f"参数量减少: {param_reduction:.1f}% ({original_params:,} → {pruned_params:,})")
        print(f"推理速度提升: {speed_improvement:.1f}% ({original_fps:.2f} → {pruned_fps:.2f} FPS)")
        print(f"推理时间减少: {(1 - pruned_time/original_time)*100:.1f}% ({original_time*1000:.2f} → {pruned_time*1000:.2f} ms)")
    else:
        print(f"剪枝后模型参数量: {pruned_params:,}")
        print(f"剪枝后模型推理速度: {pruned_fps:.2f} FPS")
    
    return pruned_model

def main():
    parser = argparse.ArgumentParser(description='测试剪枝后的YOLO模型')
    parser.add_argument('--pruned-weights', type=str, default='pruned_yolo_best22_20percent.pt', help='剪枝后模型权重')
    parser.add_argument('--original-weights', type=str, default='yolov5/best_22.pt', help='原始模型权重（用于比较）')
    parser.add_argument('--source', type=str, help='测试图像路径')
    parser.add_argument('--img-size', type=int, default=640, help='推理图像大小')
    parser.add_argument('--conf-thres', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--iou-thres', type=float, default=0.45, help='IoU阈值')
    parser.add_argument('--device', type=str, default='cpu', help='设备')
    parser.add_argument('--save-path', type=str, help='结果保存路径')
    parser.add_argument('--benchmark', action='store_true', help='只进行基准测试')
    parser.add_argument('--compare', action='store_true', help='比较原始模型和剪枝后模型')
    
    args = parser.parse_args()
    
    # 选择设备
    device = select_device(args.device)
    print(f"使用设备: {device}")
    
    if args.compare:
        # 比较模型性能
        pruned_model = compare_models(args.original_weights, args.pruned_weights, device, args.img_size)
    else:
        # 加载剪枝后模型
        pruned_model = load_pruned_model(args.pruned_weights, device)
    
    if args.benchmark:
        # 只进行基准测试
        benchmark_model(pruned_model, device, args.img_size)
    elif args.source:
        # 测试单张图像
        test_single_image(
            pruned_model, 
            args.source, 
            device, 
            args.img_size, 
            args.conf_thres, 
            args.iou_thres, 
            args.save_path
        )
    else:
        print("请指定 --source 进行图像测试，或使用 --benchmark 进行性能测试")

if __name__ == '__main__':
    main()
