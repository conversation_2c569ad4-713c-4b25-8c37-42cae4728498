#!/usr/bin/env python3
"""
综合YOLO模型剪枝实验
结合多种剪枝方法和完整的性能记录
"""

import argparse
import torch
import torch.nn as nn
import numpy as np
import time
import os
import sys
from pathlib import Path

# 添加yolov5路径
sys.path.append('yolov5')

from yolov5.utils.torch_utils import select_device
from yolov5.utils.general import check_dataset, check_yaml, colorstr, LOGGER
from yolov5.models.yolo import Model
from yolov5.models.common import Bottleneck

from pruning_logger import PruningLogger

class ComprehensivePruner:
    def __init__(self, weights_path, device='cpu'):
        """
        初始化综合剪枝器
        
        Args:
            weights_path: 模型权重路径
            device: 计算设备
        """
        self.weights_path = weights_path
        self.device = select_device(device)
        self.logger = PruningLogger()
        
        # 加载原始模型
        self.original_model, self.original_ckpt = self.load_model(weights_path)
        
        # 记录原始模型信息
        self.logger.log_original_model(
            weights_path, 
            {"model_object": self.original_model}
        )
        
        print(f"原始模型加载完成: {weights_path}")
        print(f"设备: {self.device}")
        
    def load_model(self, weights_path):
        """加载YOLO模型"""
        print(f"加载模型: {weights_path}")
        
        ckpt = torch.load(weights_path, map_location=self.device)
        
        if isinstance(ckpt, dict):
            if 'ema' in ckpt and ckpt['ema'] is not None:
                model = ckpt['ema']
            elif 'model' in ckpt and ckpt['model'] is not None:
                model = ckpt['model']
            else:
                raise ValueError("无法从模型字典中找到模型")
        else:
            model = ckpt
            ckpt = {'model': model}
        
        model = model.float().to(self.device)
        model.eval()
        
        return model, ckpt
    
    def benchmark_model(self, model, num_runs=100):
        """测试模型性能"""
        print(f"开始性能测试 ({num_runs} 次运行)...")
        
        # 创建测试输入
        test_input = torch.randn(1, 3, 640, 640).to(self.device)
        
        # 预热
        with torch.no_grad():
            for _ in range(10):
                _ = model(test_input)
        
        # 正式测试
        torch.cuda.synchronize() if torch.cuda.is_available() else None
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(num_runs):
                _ = model(test_input)
        
        torch.cuda.synchronize() if torch.cuda.is_available() else None
        end_time = time.time()
        
        # 计算性能指标
        total_time = end_time - start_time
        avg_time_ms = (total_time / num_runs) * 1000
        fps = num_runs / total_time
        
        return {
            "fps": fps,
            "inference_time_ms": avg_time_ms,
            "total_time_s": total_time,
            "num_runs": num_runs
        }
    
    def analyze_bn_layers(self, model):
        """分析BN层结构"""
        bn_layers = {}
        ignore_bn_list = []
        
        # 找出需要忽略的BN层（shortcut连接相关）
        for name, layer in model.named_modules():
            if isinstance(layer, Bottleneck):
                if layer.add:  # 有shortcut连接
                    ignore_bn_list.append(name.rsplit(".", 2)[0] + ".cv1.bn")
                    ignore_bn_list.append(name + '.cv1.bn')
                    ignore_bn_list.append(name + '.cv2.bn')
        
        # 收集所有BN层
        for name, layer in model.named_modules():
            if isinstance(layer, torch.nn.BatchNorm2d):
                if name not in ignore_bn_list:
                    bn_layers[name] = layer
        
        return bn_layers, ignore_bn_list
    
    def sparse_prune(self, percent=0.3):
        """稀疏剪枝"""
        print(f"\n开始稀疏剪枝 (剪枝比例: {percent:.1%})")
        
        # 记录剪枝配置
        self.logger.log_pruning_config("sparse_pruning", {
            "percent": percent,
            "method": "BN_weight_based"
        })
        
        # 测试原始模型性能
        print("测试原始模型性能...")
        original_performance = self.benchmark_model(self.original_model)
        self.logger.log_performance_before(original_performance)
        
        # 分析BN层
        bn_layers, ignore_bn_list = self.analyze_bn_layers(self.original_model)
        
        if len(bn_layers) == 0:
            raise ValueError("没有找到可剪枝的BN层!")
        
        print(f"可剪枝BN层数: {len(bn_layers)}")
        print(f"忽略的BN层数: {len(ignore_bn_list)}")
        
        # 收集所有BN权重
        bn_weights = []
        for layer in bn_layers.values():
            bn_weights.extend(layer.weight.data.abs().cpu().numpy())
        
        bn_weights = np.array(bn_weights)
        sorted_weights = np.sort(bn_weights)
        
        # 计算阈值
        threshold_index = int(len(sorted_weights) * percent)
        threshold = sorted_weights[threshold_index]
        
        print(f"剪枝阈值: {threshold:.6f}")
        
        # 应用剪枝
        pruned_channels = 0
        total_channels = 0
        
        for name, layer in bn_layers.items():
            mask = layer.weight.data.abs() > threshold
            layer.weight.data *= mask.float()
            layer.bias.data *= mask.float()
            
            pruned_channels += (mask == 0).sum().item()
            total_channels += mask.numel()
        
        actual_prune_ratio = pruned_channels / total_channels
        print(f"实际剪枝比例: {actual_prune_ratio:.1%}")
        
        # 测试剪枝后性能
        print("测试剪枝后模型性能...")
        pruned_performance = self.benchmark_model(self.original_model)
        self.logger.log_performance_after(pruned_performance)
        
        # 记录剪枝结果
        pruning_results = {
            "target_prune_ratio": percent,
            "actual_prune_ratio": actual_prune_ratio,
            "pruned_channels": pruned_channels,
            "total_channels": total_channels,
            "threshold": threshold,
            "prunable_bn_layers": len(bn_layers)
        }
        self.logger.log_pruning_results(pruning_results)
        
        # 保存剪枝后模型
        save_path = f"sparse_pruned_yolo_{int(percent*100)}percent.pt"
        self.save_pruned_model(save_path)
        self.logger.log_pruned_model(save_path)
        
        return save_path, pruning_results
    
    def save_pruned_model(self, save_path):
        """保存剪枝后的模型"""
        save_dict = {
            'model': self.original_model,
            'epoch': -1,
            'best_fitness': 0.0,
            'optimizer': None,
            'ema': None,
            'updates': 0
        }
        
        torch.save(save_dict, save_path)
        print(f"剪枝后模型已保存: {save_path}")
    
    def run_experiment(self, prune_ratios=[0.1, 0.2, 0.3]):
        """运行完整的剪枝实验"""
        print("=" * 60)
        print("开始综合剪枝实验")
        print("=" * 60)
        
        results = []
        
        for ratio in prune_ratios:
            print(f"\n{'='*20} 剪枝比例: {ratio:.1%} {'='*20}")
            
            # 重新加载原始模型（避免累积剪枝效果）
            self.original_model, self.original_ckpt = self.load_model(self.weights_path)
            
            try:
                save_path, pruning_results = self.sparse_prune(ratio)
                results.append({
                    "ratio": ratio,
                    "save_path": save_path,
                    "results": pruning_results
                })
                
                # 添加实验备注
                self.logger.add_note(f"稀疏剪枝 {ratio:.1%} 完成，模型保存至 {save_path}")
                
            except Exception as e:
                print(f"剪枝比例 {ratio:.1%} 失败: {e}")
                self.logger.add_note(f"剪枝比例 {ratio:.1%} 失败: {str(e)}")
        
        # 保存实验记录
        saved_files = self.logger.save_experiment()
        
        print(f"\n{'='*60}")
        print("实验完成!")
        print(f"实验ID: {self.logger.experiment_id}")
        print(f"成功剪枝: {len(results)}/{len(prune_ratios)} 个比例")
        print(f"实验记录: {saved_files['html_path']}")
        print(f"{'='*60}")
        
        return results

def main():
    parser = argparse.ArgumentParser(description='综合YOLO模型剪枝实验')
    parser.add_argument('--weights', type=str, default='yolov5/best_22.pt', 
                       help='模型权重文件路径')
    parser.add_argument('--device', type=str, default='cpu', 
                       help='设备 (cpu 或 cuda:0 等)')
    parser.add_argument('--ratios', type=float, nargs='+', default=[0.1, 0.2, 0.3], 
                       help='剪枝比例列表')
    
    args = parser.parse_args()
    
    # 检查模型文件
    if not os.path.exists(args.weights):
        print(f"错误: 模型文件不存在 {args.weights}")
        return
    
    # 创建剪枝器并运行实验
    pruner = ComprehensivePruner(args.weights, args.device)
    results = pruner.run_experiment(args.ratios)
    
    # 显示结果摘要
    print("\n实验结果摘要:")
    print("-" * 50)
    for result in results:
        ratio = result["ratio"]
        actual_ratio = result["results"]["actual_prune_ratio"]
        print(f"剪枝比例 {ratio:.1%} -> 实际 {actual_ratio:.1%}")
        print(f"  模型: {result['save_path']}")

if __name__ == '__main__':
    main()
