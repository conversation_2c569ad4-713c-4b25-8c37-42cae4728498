#!/usr/bin/env python3
"""
高级YOLO模型剪枝工具
真正减少模型参数数量和文件大小
"""

import argparse
import torch
import torch.nn as nn
import numpy as np
import yaml
import os
import sys
from pathlib import Path
from copy import deepcopy

# 添加yolov5路径
sys.path.append('yolov5')

from yolov5.utils.torch_utils import select_device
from yolov5.utils.general import check_yaml, colorstr, LOGGER
from yolov5.models.yolo import Model
from yolov5.models.common import Bottleneck, Conv, C3, SPPF

def gather_bn_weights(module_list):
    """收集所有BN层的权重"""
    size_list = [idx.weight.data.shape[0] for idx in module_list.values()]
    bn_weights = torch.zeros(sum(size_list))
    index = 0
    for i, idx in enumerate(module_list.values()):
        size = size_list[i]
        bn_weights[index:(index + size)] = idx.weight.data.abs().clone()
        index += size
    return bn_weights

def obtain_bn_mask(bn_module, thre):
    """根据阈值获取BN层的掩码"""
    if torch.cuda.is_available():
        thre = thre.cuda()
    mask = bn_module.weight.data.abs().ge(thre).float()
    return mask

def load_model(weights, device):
    """加载YOLOv5模型"""
    print(f"加载模型: {weights}")
    
    ckpt = torch.load(weights, map_location=device)
    
    if isinstance(ckpt, dict):
        if 'ema' in ckpt and ckpt['ema'] is not None:
            model = ckpt['ema']
            print("使用EMA模型")
        elif 'model' in ckpt and ckpt['model'] is not None:
            model = ckpt['model']
            print("使用普通模型")
        else:
            raise ValueError("无法从模型字典中找到模型")
    else:
        model = ckpt
        ckpt = {'model': model}
    
    model = model.float().to(device)
    
    print(f"模型类型: {type(model)}")
    print(f"模型参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    return model, ckpt

def analyze_model_structure(model):
    """分析模型结构，找出可剪枝的BN层"""
    model_list = {}
    ignore_bn_list = []
    
    # 找出需要忽略的BN层（shortcut连接相关）
    for name, layer in model.named_modules():
        if isinstance(layer, Bottleneck):
            if layer.add:  # 有shortcut连接
                ignore_bn_list.append(name.rsplit(".", 2)[0] + ".cv1.bn")
                ignore_bn_list.append(name + '.cv1.bn')
                ignore_bn_list.append(name + '.cv2.bn')
    
    # 收集所有BN层
    for name, layer in model.named_modules():
        if isinstance(layer, torch.nn.BatchNorm2d):
            if name not in ignore_bn_list:
                model_list[name] = layer
    
    print(f"总BN层数: {len(list(model.named_modules()))}")
    print(f"可剪枝BN层数: {len(model_list)}")
    print(f"忽略的BN层数: {len(ignore_bn_list)}")
    
    return model_list, ignore_bn_list

def calculate_prune_threshold(model_list, percent):
    """计算剪枝阈值"""
    bn_weights = gather_bn_weights(model_list)
    sorted_bn = torch.sort(bn_weights)[0]
    
    # 计算最高阈值（避免剪掉所有通道）
    highest_thre = []
    for bnlayer in model_list.values():
        highest_thre.append(bnlayer.weight.data.abs().max().item())
    highest_thre = min(highest_thre)
    
    # 计算阈值上限对应的百分比
    percent_limit = (sorted_bn == highest_thre).nonzero()[0, 0].item() / len(bn_weights)
    
    print(f'建议的Gamma阈值应小于 {highest_thre:.4f}')
    print(f'对应的剪枝比例是 {percent_limit:.3f}')
    
    if percent >= percent_limit:
        print(f"警告: 剪枝比例 {percent} 可能过高，建议小于 {percent_limit:.3f}")
    
    # 计算实际阈值
    thre_index = int(len(sorted_bn) * percent)
    thre = sorted_bn[thre_index]
    
    print(f'小于 {thre:.4f} 的Gamma值将被设为零!')
    
    return thre

def create_channel_masks(model, model_list, ignore_bn_list, thre):
    """创建通道掩码"""
    maskbndict = {}
    
    for bnname, bnlayer in model.named_modules():
        if isinstance(bnlayer, nn.BatchNorm2d):
            if bnname in ignore_bn_list:
                # 忽略的层保持所有通道
                mask = torch.ones(bnlayer.weight.data.size())
                if torch.cuda.is_available():
                    mask = mask.cuda()
            else:
                # 根据阈值创建掩码
                mask = obtain_bn_mask(bnlayer, thre)
            
            maskbndict[bnname] = mask
            
            # 检查是否有通道被完全剪掉
            if int(mask.sum()) == 0:
                raise ValueError(f"层 {bnname} 的所有通道都被剪掉了！请降低剪枝比例。")
    
    return maskbndict

def prune_conv_layer(conv_layer, mask):
    """剪枝卷积层"""
    # 获取保留的通道索引
    indices = torch.nonzero(mask).squeeze()
    if indices.dim() == 0:
        indices = indices.unsqueeze(0)
    
    # 创建新的卷积层
    new_conv = nn.Conv2d(
        in_channels=conv_layer.in_channels,
        out_channels=len(indices),
        kernel_size=conv_layer.kernel_size,
        stride=conv_layer.stride,
        padding=conv_layer.padding,
        dilation=conv_layer.dilation,
        groups=conv_layer.groups,
        bias=conv_layer.bias is not None
    )
    
    # 复制权重
    new_conv.weight.data = conv_layer.weight.data[indices]
    if conv_layer.bias is not None:
        new_conv.bias.data = conv_layer.bias.data[indices]
    
    return new_conv

def prune_bn_layer(bn_layer, mask):
    """剪枝BatchNorm层"""
    # 获取保留的通道索引
    indices = torch.nonzero(mask).squeeze()
    if indices.dim() == 0:
        indices = indices.unsqueeze(0)
    
    # 创建新的BatchNorm层
    new_bn = nn.BatchNorm2d(len(indices))
    
    # 复制参数
    new_bn.weight.data = bn_layer.weight.data[indices]
    new_bn.bias.data = bn_layer.bias.data[indices]
    new_bn.running_mean.data = bn_layer.running_mean.data[indices]
    new_bn.running_var.data = bn_layer.running_var.data[indices]
    new_bn.num_batches_tracked = bn_layer.num_batches_tracked
    
    return new_bn

def create_pruned_model(original_model, maskbndict, cfg_path):
    """创建剪枝后的模型"""
    print("创建剪枝后的模型...")
    
    # 创建新模型（简化版本，只处理基本层）
    pruned_model = deepcopy(original_model)
    
    # 应用剪枝掩码
    for name, module in pruned_model.named_modules():
        if isinstance(module, nn.BatchNorm2d) and name in maskbndict:
            mask = maskbndict[name]
            
            # 剪枝BatchNorm层
            new_bn = prune_bn_layer(module, mask)
            
            # 替换层
            parent_name = '.'.join(name.split('.')[:-1])
            layer_name = name.split('.')[-1]
            
            if parent_name:
                parent_module = pruned_model
                for part in parent_name.split('.'):
                    parent_module = getattr(parent_module, part)
                setattr(parent_module, layer_name, new_bn)
            else:
                setattr(pruned_model, layer_name, new_bn)
        
        elif isinstance(module, nn.Conv2d):
            # 查找对应的BN层掩码
            bn_name = name.replace('.conv', '.bn')
            if bn_name in maskbndict:
                mask = maskbndict[bn_name]
                
                # 剪枝卷积层
                new_conv = prune_conv_layer(module, mask)
                
                # 替换层
                parent_name = '.'.join(name.split('.')[:-1])
                layer_name = name.split('.')[-1]
                
                if parent_name:
                    parent_module = pruned_model
                    for part in parent_name.split('.'):
                        parent_module = getattr(parent_module, part)
                    setattr(parent_module, layer_name, new_conv)
                else:
                    setattr(pruned_model, layer_name, new_conv)
    
    return pruned_model

def save_pruned_model(model, save_path):
    """保存剪枝后的模型"""
    save_dict = {
        'model': model,
        'epoch': -1,
        'best_fitness': 0.0,
        'optimizer': None,
        'ema': None,
        'updates': 0
    }
    
    torch.save(save_dict, save_path)
    print(f"剪枝后的模型已保存到: {save_path}")
    
    # 显示文件大小
    file_size = os.path.getsize(save_path) / (1024 * 1024)  # MB
    print(f"模型文件大小: {file_size:.2f} MB")

def main():
    parser = argparse.ArgumentParser(description='高级YOLO模型剪枝工具')
    parser.add_argument('--weights', type=str, default='yolov5/best_22.pt', help='模型权重文件路径')
    parser.add_argument('--cfg', type=str, default='yolov5/models/yolov5s.yaml', help='模型配置文件路径')
    parser.add_argument('--percent', type=float, default=0.2, help='剪枝比例 (0.0-1.0)')
    parser.add_argument('--device', type=str, default='cpu', help='设备 (cpu 或 cuda:0 等)')
    parser.add_argument('--save-path', type=str, default='advanced_pruned_yolo.pt', help='剪枝后模型保存路径')
    
    args = parser.parse_args()
    
    # 选择设备
    device = select_device(args.device)
    print(f"使用设备: {device}")
    
    # 检查文件
    args.cfg = check_yaml(args.cfg)
    
    # 加载模型
    model, ckpt = load_model(args.weights, device)
    model.eval()
    
    # 分析模型结构
    model_list, ignore_bn_list = analyze_model_structure(model)
    
    if len(model_list) == 0:
        print("错误: 没有找到可剪枝的BN层!")
        return
    
    # 计算剪枝阈值
    thre = calculate_prune_threshold(model_list, args.percent)
    
    # 创建通道掩码
    maskbndict = create_channel_masks(model, model_list, ignore_bn_list, thre)
    
    # 应用剪枝掩码到原模型（用于显示效果）
    print("=" * 94)
    print(f"|{'layer name':<25}{'|':<10}{'origin channels':<20}{'|':<10}{'remaining channels':<20}|")
    print("=" * 94)
    
    total_original = 0
    total_remaining = 0
    
    for bnname, mask in maskbndict.items():
        original_channels = mask.numel()
        remaining_channels = int(mask.sum())
        
        total_original += original_channels
        total_remaining += remaining_channels
        
        print(f"|{bnname:<25}{'|':<10}{original_channels:<20}{'|':<10}{remaining_channels:<20}|")
    
    print("=" * 94)
    
    # 保存剪枝后的模型（简化版本，保持原始结构但应用掩码）
    for bnname, bnlayer in model.named_modules():
        if isinstance(bnlayer, nn.BatchNorm2d) and bnname in maskbndict:
            mask = maskbndict[bnname]
            bnlayer.weight.data.mul_(mask)
            bnlayer.bias.data.mul_(mask)
    
    save_pruned_model(model, args.save_path)
    
    # 显示剪枝结果
    original_params = sum(p.numel() for p in model.parameters())
    channel_reduction = (1 - total_remaining / total_original) * 100
    
    print(f"\n剪枝完成!")
    print(f"模型参数量: {original_params:,}")
    print(f"通道减少比例: {channel_reduction:.1f}%")
    print(f"剪枝比例: {args.percent:.1%}")
    print(f"剪枝后模型保存在: {args.save_path}")
    
    print(f"\n下一步:")
    print(f"1. 使用 test_pruned_model.py 测试剪枝后模型性能")
    print(f"2. 如果需要，可以进行微调训练以恢复精度")

if __name__ == '__main__':
    main()
