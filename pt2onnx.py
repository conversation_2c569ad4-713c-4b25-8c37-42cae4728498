# 限制高性能库使用的CPU线程数
import os
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["OPENBLAS_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"
os.environ["VECLIB_MAXIMUM_THREADS"] = "1"
os.environ["NUMEXPR_NUM_THREADS"] = "1"

import sys
import argparse
from pathlib import Path
import torch

# 将YOLOv5路径添加到系统路径
FILE = Path(__file__).resolve()
ROOT = FILE.parents[0] / 'yolov5'  # YOLOv5根目录
if str(ROOT) not in sys.path:
    sys.path.append(str(ROOT))  # 添加到系统路径

# 导入YOLOv5模型和工具
from yolov5.models.common import DetectMultiBackend
from yolov5.utils.torch_utils import select_device
from yolov5.utils.general import check_img_size

def export_onnx(yolo_model_path, output_path='yolov5_export.onnx', imgsz=(640, 640), device='cpu'):
    """
    导出YOLOv5模型为ONNX格式
    
    Args:
        yolo_model_path (str): .pt权重文件路径
        output_path (str): 导出的ONNX文件路径
        imgsz (tuple): 模型输入尺寸
        device (str): 运行设备
    """
    # 选择设备
    device = select_device(device)
    
    # 加载模型
    model = DetectMultiBackend(yolo_model_path, device=device, dnn=False)
    stride, names = model.stride, model.names
    imgsz = check_img_size(imgsz, s=stride)  # 检查图像尺寸
    
    # 配置模型
    model.eval()
    model.model.half() if device.type != 'cpu' else model.model.float()  # 半精度
    
    # 创建输入张量
    img = torch.zeros(1, 3, *imgsz).to(device).type_as(next(model.model.parameters()))  # 输入图像
    
    # 导出模型
    try:
        print(f'开始导出ONNX模型到 {output_path}...')
        torch.onnx.export(
            model, 
            img, 
            output_path, 
            verbose=False, 
            opset_version=12,  # ONNX版本
            do_constant_folding=True,
            input_names=['images'],
            output_names=['output'],
            dynamic_axes=None  # 静态尺寸，便于Netron可视化
        )
        print(f'ONNX导出成功: {output_path}')
        return output_path
    except Exception as e:
        print(f'ONNX导出失败: {e}')
        return None

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--yolo_model', type=str, default='yolov5/best_0429.pt', help='.pt模型路径')
    parser.add_argument('--output', type=str, default='yolov5_export.onnx', help='导出的ONNX文件路径')
    parser.add_argument('--imgsz', nargs='+', type=int, default=[640, 640], help='输入图像尺寸')
    parser.add_argument('--device', type=str, default='cpu', help='运行设备 (i.e. 0 or 0,1,2,3 or cpu)')
    opt = parser.parse_args()
    
    # 导出模型
    export_onnx(
        yolo_model_path=opt.yolo_model,
        output_path=opt.output,
        imgsz=opt.imgsz,
        device=opt.device
    )