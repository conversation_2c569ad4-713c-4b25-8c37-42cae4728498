#!/usr/bin/env python3
"""
性能统计模块
记录YOLO检测、DeepSORT跟踪等各个环节的时间消耗
生成Excel报告和可视化图表
"""

import time
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from datetime import datetime
import os
from collections import defaultdict, deque
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PerformanceTracker:
    """性能统计跟踪器"""
    
    def __init__(self, save_dir="performance_logs", window_size=100):
        """
        初始化性能跟踪器
        
        Args:
            save_dir: 保存目录
            window_size: 滑动窗口大小（用于实时统计）
        """
        self.save_dir = save_dir
        self.window_size = window_size
        
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        
        # 时间记录
        self.frame_data = []
        self.current_frame = {}
        self.frame_count = 0
        
        # 滑动窗口统计
        self.recent_times = {
            'yolo_time': deque(maxlen=window_size),
            'deepsort_time': deque(maxlen=window_size),
            'total_time': deque(maxlen=window_size),
            'other_time': deque(maxlen=window_size)
        }
        
        # 累计统计
        self.total_stats = {
            'total_yolo_time': 0,
            'total_deepsort_time': 0,
            'total_other_time': 0,
            'total_frames': 0
        }
        
        # 开始时间
        self.start_time = time.time()
        self.session_start = datetime.now()
        
        print(f"性能跟踪器初始化完成，保存目录: {save_dir}")
    
    def start_frame(self, frame_id=None):
        """开始新帧的计时"""
        self.frame_count += 1
        if frame_id is None:
            frame_id = self.frame_count
            
        self.current_frame = {
            'frame_id': frame_id,
            'frame_start_time': time.time(),
            'yolo_start': None,
            'yolo_end': None,
            'deepsort_start': None,
            'deepsort_end': None,
            'other_operations': []
        }
    
    def start_yolo(self):
        """开始YOLO检测计时"""
        self.current_frame['yolo_start'] = time.time()
    
    def end_yolo(self):
        """结束YOLO检测计时"""
        self.current_frame['yolo_end'] = time.time()
    
    def start_deepsort(self):
        """开始DeepSORT跟踪计时"""
        self.current_frame['deepsort_start'] = time.time()
    
    def end_deepsort(self):
        """结束DeepSORT跟踪计时"""
        self.current_frame['deepsort_end'] = time.time()
    
    def add_operation_time(self, operation_name, duration):
        """添加其他操作的时间"""
        self.current_frame['other_operations'].append({
            'name': operation_name,
            'duration': duration
        })
    
    def end_frame(self):
        """结束当前帧的计时并记录数据"""
        frame_end_time = time.time()
        
        # 计算各部分时间
        yolo_time = 0
        if self.current_frame['yolo_start'] and self.current_frame['yolo_end']:
            yolo_time = self.current_frame['yolo_end'] - self.current_frame['yolo_start']
        
        deepsort_time = 0
        if self.current_frame['deepsort_start'] and self.current_frame['deepsort_end']:
            deepsort_time = self.current_frame['deepsort_end'] - self.current_frame['deepsort_start']
        
        other_time = sum(op['duration'] for op in self.current_frame['other_operations'])
        total_time = frame_end_time - self.current_frame['frame_start_time']
        
        # 记录帧数据
        frame_data = {
            'frame_id': self.current_frame['frame_id'],
            'timestamp': datetime.now(),
            'yolo_time': yolo_time * 1000,  # 转换为毫秒
            'deepsort_time': deepsort_time * 1000,
            'other_time': other_time * 1000,
            'total_time': total_time * 1000,
            'yolo_percentage': (yolo_time / total_time * 100) if total_time > 0 else 0,
            'deepsort_percentage': (deepsort_time / total_time * 100) if total_time > 0 else 0,
            'other_percentage': (other_time / total_time * 100) if total_time > 0 else 0,
            'fps': 1.0 / total_time if total_time > 0 else 0
        }
        
        self.frame_data.append(frame_data)
        
        # 更新滑动窗口
        self.recent_times['yolo_time'].append(yolo_time * 1000)
        self.recent_times['deepsort_time'].append(deepsort_time * 1000)
        self.recent_times['total_time'].append(total_time * 1000)
        self.recent_times['other_time'].append(other_time * 1000)
        
        # 更新累计统计
        self.total_stats['total_yolo_time'] += yolo_time
        self.total_stats['total_deepsort_time'] += deepsort_time
        self.total_stats['total_other_time'] += other_time
        self.total_stats['total_frames'] += 1
        
        return frame_data
    
    def get_current_stats(self):
        """获取当前统计信息"""
        if not self.recent_times['total_time']:
            return None
        
        stats = {
            'recent_avg_yolo': np.mean(self.recent_times['yolo_time']),
            'recent_avg_deepsort': np.mean(self.recent_times['deepsort_time']),
            'recent_avg_total': np.mean(self.recent_times['total_time']),
            'recent_fps': 1000 / np.mean(self.recent_times['total_time']),
            'total_frames': self.total_stats['total_frames'],
            'session_duration': time.time() - self.start_time
        }
        
        return stats
    
    def save_to_excel(self, filename=None):
        """保存数据到Excel文件"""
        if not self.frame_data:
            print("没有数据可保存")
            return None
        
        if filename is None:
            timestamp = self.session_start.strftime("%Y%m%d_%H%M%S")
            filename = f"performance_report_{timestamp}.xlsx"
        
        filepath = os.path.join(self.save_dir, filename)
        
        # 创建DataFrame
        df = pd.DataFrame(self.frame_data)
        
        # 计算统计信息
        summary_stats = {
            '指标': [
                '总帧数', '总时长(秒)', '平均FPS',
                'YOLO平均时间(ms)', 'DeepSORT平均时间(ms)', '其他操作平均时间(ms)', '总平均时间(ms)',
                'YOLO时间占比(%)', 'DeepSORT时间占比(%)', '其他操作时间占比(%)',
                'YOLO最大时间(ms)', 'DeepSORT最大时间(ms)', '总最大时间(ms)',
                'YOLO最小时间(ms)', 'DeepSORT最小时间(ms)', '总最小时间(ms)'
            ],
            '数值': [
                len(self.frame_data),
                time.time() - self.start_time,
                df['fps'].mean(),
                df['yolo_time'].mean(),
                df['deepsort_time'].mean(),
                df['other_time'].mean(),
                df['total_time'].mean(),
                df['yolo_percentage'].mean(),
                df['deepsort_percentage'].mean(),
                df['other_percentage'].mean(),
                df['yolo_time'].max(),
                df['deepsort_time'].max(),
                df['total_time'].max(),
                df['yolo_time'].min(),
                df['deepsort_time'].min(),
                df['total_time'].min()
            ]
        }
        
        summary_df = pd.DataFrame(summary_stats)
        
        # 保存到Excel
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='详细数据', index=False)
            summary_df.to_excel(writer, sheet_name='统计摘要', index=False)
        
        print(f"性能报告已保存到: {filepath}")
        return filepath
    
    def plot_performance(self, save_plots=True):
        """绘制性能图表"""
        if not self.frame_data:
            print("没有数据可绘制")
            return
        
        df = pd.DataFrame(self.frame_data)
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        fig = plt.figure(figsize=(16, 12))
        
        # 1. 时间消耗趋势图
        plt.subplot(2, 3, 1)
        plt.plot(df['frame_id'], df['yolo_time'], label='YOLO检测', linewidth=1, alpha=0.7)
        plt.plot(df['frame_id'], df['deepsort_time'], label='DeepSORT跟踪', linewidth=1, alpha=0.7)
        plt.plot(df['frame_id'], df['total_time'], label='总时间', linewidth=2)
        plt.xlabel('帧数')
        plt.ylabel('时间 (ms)')
        plt.title('各模块时间消耗趋势')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 2. 时间占比饼图
        plt.subplot(2, 3, 2)
        avg_times = [df['yolo_time'].mean(), df['deepsort_time'].mean(), df['other_time'].mean()]
        labels = ['YOLO检测', 'DeepSORT跟踪', '其他操作']
        colors = ['#ff9999', '#66b3ff', '#99ff99']
        plt.pie(avg_times, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        plt.title('平均时间占比分布')
        
        # 3. FPS趋势图
        plt.subplot(2, 3, 3)
        plt.plot(df['frame_id'], df['fps'], color='green', linewidth=1)
        plt.axhline(y=df['fps'].mean(), color='red', linestyle='--', label=f'平均FPS: {df["fps"].mean():.1f}')
        plt.xlabel('帧数')
        plt.ylabel('FPS')
        plt.title('实时FPS变化')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 4. 时间分布直方图
        plt.subplot(2, 3, 4)
        plt.hist(df['yolo_time'], bins=30, alpha=0.7, label='YOLO', color='red')
        plt.hist(df['deepsort_time'], bins=30, alpha=0.7, label='DeepSORT', color='blue')
        plt.xlabel('时间 (ms)')
        plt.ylabel('频次')
        plt.title('时间分布直方图')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 5. 性能对比柱状图
        plt.subplot(2, 3, 5)
        categories = ['YOLO', 'DeepSORT', '其他', '总计']
        avg_times = [df['yolo_time'].mean(), df['deepsort_time'].mean(), 
                    df['other_time'].mean(), df['total_time'].mean()]
        bars = plt.bar(categories, avg_times, color=['red', 'blue', 'green', 'orange'])
        plt.ylabel('平均时间 (ms)')
        plt.title('各模块平均耗时对比')
        
        # 在柱状图上添加数值标签
        for bar, time_val in zip(bars, avg_times):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{time_val:.1f}ms', ha='center', va='bottom')
        
        # 6. 滑动平均趋势
        plt.subplot(2, 3, 6)
        window = min(20, len(df) // 4)  # 滑动窗口大小
        if window > 1:
            df['yolo_ma'] = df['yolo_time'].rolling(window=window).mean()
            df['deepsort_ma'] = df['deepsort_time'].rolling(window=window).mean()
            df['total_ma'] = df['total_time'].rolling(window=window).mean()
            
            plt.plot(df['frame_id'], df['yolo_ma'], label=f'YOLO({window}帧均值)', linewidth=2)
            plt.plot(df['frame_id'], df['deepsort_ma'], label=f'DeepSORT({window}帧均值)', linewidth=2)
            plt.plot(df['frame_id'], df['total_ma'], label=f'总时间({window}帧均值)', linewidth=2)
            plt.xlabel('帧数')
            plt.ylabel('时间 (ms)')
            plt.title('滑动平均趋势')
            plt.legend()
            plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plots:
            timestamp = self.session_start.strftime("%Y%m%d_%H%M%S")
            plot_filename = f"performance_plots_{timestamp}.png"
            plot_filepath = os.path.join(self.save_dir, plot_filename)
            plt.savefig(plot_filepath, dpi=300, bbox_inches='tight')
            print(f"性能图表已保存到: {plot_filepath}")
        
        plt.show()
    
    def print_summary(self):
        """打印性能摘要"""
        if not self.frame_data:
            print("没有数据可显示")
            return
        
        df = pd.DataFrame(self.frame_data)
        total_duration = time.time() - self.start_time
        
        print(f"\n{'='*60}")
        print("性能统计摘要")
        print(f"{'='*60}")
        print(f"测试时间: {self.session_start.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"总时长: {total_duration:.2f}秒")
        print(f"总帧数: {len(self.frame_data)}")
        print(f"平均FPS: {df['fps'].mean():.2f}")
        print(f"\n各模块平均耗时:")
        print(f"  YOLO检测: {df['yolo_time'].mean():.2f}ms ({df['yolo_percentage'].mean():.1f}%)")
        print(f"  DeepSORT跟踪: {df['deepsort_time'].mean():.2f}ms ({df['deepsort_percentage'].mean():.1f}%)")
        print(f"  其他操作: {df['other_time'].mean():.2f}ms ({df['other_percentage'].mean():.1f}%)")
        print(f"  总计: {df['total_time'].mean():.2f}ms")
        print(f"\n性能瓶颈分析:")
        if df['yolo_percentage'].mean() > df['deepsort_percentage'].mean():
            print("  主要瓶颈: YOLO检测")
        else:
            print("  主要瓶颈: DeepSORT跟踪")
        print(f"{'='*60}")

# 上下文管理器，方便使用
class TimingContext:
    """计时上下文管理器"""
    
    def __init__(self, tracker, operation_name):
        self.tracker = tracker
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        self.tracker.add_operation_time(self.operation_name, duration)
