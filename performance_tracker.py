#!/usr/bin/env python3
"""
性能统计模块
记录YOLO检测、DeepSORT跟踪等各个环节的时间消耗
生成Excel报告和可视化图表
"""

import time
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from datetime import datetime
import os
from collections import defaultdict, deque
import seaborn as sns

# 设置字体为英文，避免中文字体问题
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False

class PerformanceTracker:
    """性能统计跟踪器"""

    def __init__(self, save_dir="performance_logs", window_size=100):
        """
        初始化性能跟踪器

        Args:
            save_dir: 保存目录
            window_size: 滑动窗口大小（用于实时统计）
        """
        self.save_dir = save_dir
        self.window_size = window_size

        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)

        # 时间记录
        self.frame_data = []
        self.current_frame = {}
        self.frame_count = 0

        # 滑动窗口统计
        self.recent_times = {
            'yolo_time': deque(maxlen=window_size),
            'deepsort_time': deque(maxlen=window_size),
            'total_time': deque(maxlen=window_size),
            'other_time': deque(maxlen=window_size)
        }

        # 累计统计
        self.total_stats = {
            'total_yolo_time': 0,
            'total_deepsort_time': 0,
            'total_other_time': 0,
            'total_frames': 0
        }

        # 开始时间
        self.start_time = time.time()
        self.session_start = datetime.now()

        print(f"Performance tracker initialized, save directory: {save_dir}")

    def start_frame(self, frame_id=None):
        """开始新帧的计时"""
        self.frame_count += 1
        if frame_id is None:
            frame_id = self.frame_count

        self.current_frame = {
            'frame_id': frame_id,
            'frame_start_time': time.time(),
            'yolo_start': None,
            'yolo_end': None,
            'deepsort_start': None,
            'deepsort_end': None,
            'other_operations': []
        }

    def start_yolo(self):
        """开始YOLO检测计时"""
        self.current_frame['yolo_start'] = time.time()

    def end_yolo(self):
        """结束YOLO检测计时"""
        self.current_frame['yolo_end'] = time.time()

    def start_deepsort(self):
        """开始DeepSORT跟踪计时"""
        self.current_frame['deepsort_start'] = time.time()

    def end_deepsort(self):
        """结束DeepSORT跟踪计时"""
        self.current_frame['deepsort_end'] = time.time()

    def add_operation_time(self, operation_name, duration):
        """添加其他操作的时间"""
        self.current_frame['other_operations'].append({
            'name': operation_name,
            'duration': duration
        })

    def end_frame(self):
        """结束当前帧的计时并记录数据"""
        frame_end_time = time.time()

        # 计算各部分时间
        yolo_time = 0
        if self.current_frame['yolo_start'] and self.current_frame['yolo_end']:
            yolo_time = self.current_frame['yolo_end'] - self.current_frame['yolo_start']

        deepsort_time = 0
        if self.current_frame['deepsort_start'] and self.current_frame['deepsort_end']:
            deepsort_time = self.current_frame['deepsort_end'] - self.current_frame['deepsort_start']

        other_time = sum(op['duration'] for op in self.current_frame['other_operations'])
        total_time = frame_end_time - self.current_frame['frame_start_time']

        # 记录帧数据
        frame_data = {
            'frame_id': self.current_frame['frame_id'],
            'timestamp': datetime.now(),
            'yolo_time': yolo_time * 1000,  # 转换为毫秒
            'deepsort_time': deepsort_time * 1000,
            'other_time': other_time * 1000,
            'total_time': total_time * 1000,
            'yolo_percentage': (yolo_time / total_time * 100) if total_time > 0 else 0,
            'deepsort_percentage': (deepsort_time / total_time * 100) if total_time > 0 else 0,
            'other_percentage': (other_time / total_time * 100) if total_time > 0 else 0,
            'fps': 1.0 / total_time if total_time > 0 else 0
        }

        self.frame_data.append(frame_data)

        # 更新滑动窗口
        self.recent_times['yolo_time'].append(yolo_time * 1000)
        self.recent_times['deepsort_time'].append(deepsort_time * 1000)
        self.recent_times['total_time'].append(total_time * 1000)
        self.recent_times['other_time'].append(other_time * 1000)

        # 更新累计统计
        self.total_stats['total_yolo_time'] += yolo_time
        self.total_stats['total_deepsort_time'] += deepsort_time
        self.total_stats['total_other_time'] += other_time
        self.total_stats['total_frames'] += 1

        return frame_data

    def get_current_stats(self):
        """获取当前统计信息"""
        if not self.recent_times['total_time']:
            return None

        stats = {
            'recent_avg_yolo': np.mean(self.recent_times['yolo_time']),
            'recent_avg_deepsort': np.mean(self.recent_times['deepsort_time']),
            'recent_avg_total': np.mean(self.recent_times['total_time']),
            'recent_fps': 1000 / np.mean(self.recent_times['total_time']),
            'total_frames': self.total_stats['total_frames'],
            'session_duration': time.time() - self.start_time
        }

        return stats

    def save_to_excel(self, filename=None):
        """保存数据到Excel文件"""
        if not self.frame_data:
            print("没有数据可保存")
            return None

        if filename is None:
            timestamp = self.session_start.strftime("%Y%m%d_%H%M%S")
            filename = f"performance_report_{timestamp}.xlsx"

        filepath = os.path.join(self.save_dir, filename)

        # 创建DataFrame
        df = pd.DataFrame(self.frame_data)

        # Calculate statistics
        summary_stats = {
            'Metric': [
                'Total Frames', 'Total Duration (s)', 'Average FPS',
                'YOLO Avg Time (ms)', 'DeepSORT Avg Time (ms)', 'Other Avg Time (ms)', 'Total Avg Time (ms)',
                'YOLO Time Ratio (%)', 'DeepSORT Time Ratio (%)', 'Other Time Ratio (%)',
                'YOLO Max Time (ms)', 'DeepSORT Max Time (ms)', 'Total Max Time (ms)',
                'YOLO Min Time (ms)', 'DeepSORT Min Time (ms)', 'Total Min Time (ms)'
            ],
            'Value': [
                len(self.frame_data),
                time.time() - self.start_time,
                df['fps'].mean(),
                df['yolo_time'].mean(),
                df['deepsort_time'].mean(),
                df['other_time'].mean(),
                df['total_time'].mean(),
                df['yolo_percentage'].mean(),
                df['deepsort_percentage'].mean(),
                df['other_percentage'].mean(),
                df['yolo_time'].max(),
                df['deepsort_time'].max(),
                df['total_time'].max(),
                df['yolo_time'].min(),
                df['deepsort_time'].min(),
                df['total_time'].min()
            ]
        }

        summary_df = pd.DataFrame(summary_stats)

        # Save to Excel
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Detailed Data', index=False)
            summary_df.to_excel(writer, sheet_name='Summary Statistics', index=False)

        print(f"Performance report saved to: {filepath}")
        return filepath

    def plot_performance(self, save_plots=True):
        """Plot performance charts"""
        if not self.frame_data:
            print("No data to plot")
            return

        df = pd.DataFrame(self.frame_data)

        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        fig = plt.figure(figsize=(16, 12))

        # 1. Time consumption trend
        plt.subplot(2, 3, 1)
        plt.plot(df['frame_id'], df['yolo_time'], label='YOLO Detection', linewidth=1, alpha=0.7)
        plt.plot(df['frame_id'], df['deepsort_time'], label='DeepSORT Tracking', linewidth=1, alpha=0.7)
        plt.plot(df['frame_id'], df['total_time'], label='Total Time', linewidth=2)
        plt.xlabel('Frame Number')
        plt.ylabel('Time (ms)')
        plt.title('Time Consumption Trend by Module')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 2. Time distribution pie chart
        plt.subplot(2, 3, 2)
        avg_times = [df['yolo_time'].mean(), df['deepsort_time'].mean(), df['other_time'].mean()]
        labels = ['YOLO Detection', 'DeepSORT Tracking', 'Other Operations']
        colors = ['#ff9999', '#66b3ff', '#99ff99']
        plt.pie(avg_times, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        plt.title('Average Time Distribution')

        # 3. FPS trend chart
        plt.subplot(2, 3, 3)
        plt.plot(df['frame_id'], df['fps'], color='green', linewidth=1)
        plt.axhline(y=df['fps'].mean(), color='red', linestyle='--', label=f'Average FPS: {df["fps"].mean():.1f}')
        plt.xlabel('Frame Number')
        plt.ylabel('FPS')
        plt.title('Real-time FPS Changes')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 4. Time distribution histogram
        plt.subplot(2, 3, 4)
        plt.hist(df['yolo_time'], bins=30, alpha=0.7, label='YOLO', color='red')
        plt.hist(df['deepsort_time'], bins=30, alpha=0.7, label='DeepSORT', color='blue')
        plt.xlabel('Time (ms)')
        plt.ylabel('Frequency')
        plt.title('Time Distribution Histogram')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 5. Performance comparison bar chart
        plt.subplot(2, 3, 5)
        categories = ['YOLO', 'DeepSORT', 'Others', 'Total']
        avg_times = [df['yolo_time'].mean(), df['deepsort_time'].mean(),
                    df['other_time'].mean(), df['total_time'].mean()]
        bars = plt.bar(categories, avg_times, color=['red', 'blue', 'green', 'orange'])
        plt.ylabel('Average Time (ms)')
        plt.title('Average Time Comparison by Module')

        # Add value labels on bars
        for bar, time_val in zip(bars, avg_times):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                    f'{time_val:.1f}ms', ha='center', va='bottom')

        # 6. Moving average trend
        plt.subplot(2, 3, 6)
        window = min(20, len(df) // 4)  # Moving window size
        if window > 1:
            df['yolo_ma'] = df['yolo_time'].rolling(window=window).mean()
            df['deepsort_ma'] = df['deepsort_time'].rolling(window=window).mean()
            df['total_ma'] = df['total_time'].rolling(window=window).mean()

            plt.plot(df['frame_id'], df['yolo_ma'], label=f'YOLO({window}-frame avg)', linewidth=2)
            plt.plot(df['frame_id'], df['deepsort_ma'], label=f'DeepSORT({window}-frame avg)', linewidth=2)
            plt.plot(df['frame_id'], df['total_ma'], label=f'Total({window}-frame avg)', linewidth=2)
            plt.xlabel('Frame Number')
            plt.ylabel('Time (ms)')
            plt.title('Moving Average Trend')
            plt.legend()
            plt.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_plots:
            timestamp = self.session_start.strftime("%Y%m%d_%H%M%S")
            plot_filename = f"performance_plots_{timestamp}.png"
            plot_filepath = os.path.join(self.save_dir, plot_filename)
            plt.savefig(plot_filepath, dpi=300, bbox_inches='tight')
            print(f"Performance charts saved to: {plot_filepath}")

        plt.show()

    def print_summary(self):
        """Print performance summary"""
        if not self.frame_data:
            print("No data to display")
            return

        df = pd.DataFrame(self.frame_data)
        total_duration = time.time() - self.start_time

        print(f"\n{'='*60}")
        print("Performance Statistics Summary")
        print(f"{'='*60}")
        print(f"Test Time: {self.session_start.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Total Duration: {total_duration:.2f}s")
        print(f"Total Frames: {len(self.frame_data)}")
        print(f"Average FPS: {df['fps'].mean():.2f}")
        print(f"\nAverage Time by Module:")
        print(f"  YOLO Detection: {df['yolo_time'].mean():.2f}ms ({df['yolo_percentage'].mean():.1f}%)")
        print(f"  DeepSORT Tracking: {df['deepsort_time'].mean():.2f}ms ({df['deepsort_percentage'].mean():.1f}%)")
        print(f"  Other Operations: {df['other_time'].mean():.2f}ms ({df['other_percentage'].mean():.1f}%)")
        print(f"  Total: {df['total_time'].mean():.2f}ms")
        print(f"\nPerformance Bottleneck Analysis:")
        if df['yolo_percentage'].mean() > df['deepsort_percentage'].mean():
            print("  Main Bottleneck: YOLO Detection")
        else:
            print("  Main Bottleneck: DeepSORT Tracking")
        print(f"{'='*60}")

# 上下文管理器，方便使用
class TimingContext:
    """计时上下文管理器"""

    def __init__(self, tracker, operation_name):
        self.tracker = tracker
        self.operation_name = operation_name
        self.start_time = None

    def __enter__(self):
        self.start_time = time.time()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        self.tracker.add_operation_time(self.operation_name, duration)
