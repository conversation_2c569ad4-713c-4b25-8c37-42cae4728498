#!/usr/bin/env python3
"""
模型剪枝记录保存系统
记录剪枝过程、参数、性能对比等详细信息
"""

import json
import os
import time
import datetime
import torch
import pandas as pd
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns

class PruningLogger:
    def __init__(self, log_dir="pruning_logs"):
        """
        初始化剪枝记录器

        Args:
            log_dir: 日志保存目录
        """
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)

        # 创建子目录
        (self.log_dir / "experiments").mkdir(exist_ok=True)
        (self.log_dir / "models").mkdir(exist_ok=True)
        (self.log_dir / "reports").mkdir(exist_ok=True)
        (self.log_dir / "plots").mkdir(exist_ok=True)

        # 当前实验ID
        self.experiment_id = self._generate_experiment_id()
        self.experiment_dir = self.log_dir / "experiments" / self.experiment_id
        self.experiment_dir.mkdir(exist_ok=True)

        # 实验记录
        self.experiment_log = {
            "experiment_id": self.experiment_id,
            "start_time": datetime.datetime.now().isoformat(),
            "original_model": None,
            "pruning_method": None,
            "pruning_params": {},
            "pruned_model": None,
            "performance_before": {},
            "performance_after": {},
            "pruning_results": {},
            "notes": ""
        }

        print(f"剪枝实验开始 - ID: {self.experiment_id}")
        print(f"日志目录: {self.experiment_dir}")

    def _generate_experiment_id(self):
        """生成实验ID"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"pruning_exp_{timestamp}"

    def log_original_model(self, model_path, model_info=None):
        """记录原始模型信息"""
        self.experiment_log["original_model"] = {
            "path": str(model_path),
            "file_size_mb": os.path.getsize(model_path) / (1024 * 1024)
        }

        # 如果提供了模型对象，记录参数信息
        if isinstance(model_info, dict) and "model_object" in model_info:
            model = model_info["model_object"]
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

            self.experiment_log["original_model"]["parameters"] = {
                "total": int(total_params),
                "trainable": int(trainable_params),
                "non_trainable": int(total_params - trainable_params)
            }

            # 记录模型结构信息（不包含模型对象本身）
            if hasattr(model, 'model') and hasattr(model.model, '__len__'):
                self.experiment_log["original_model"]["layers"] = len(model.model)

        print(f"原始模型记录: {model_path}")

    def log_pruning_config(self, method, params):
        """记录剪枝配置"""
        self.experiment_log["pruning_method"] = method
        self.experiment_log["pruning_params"] = params

        print(f"剪枝方法: {method}")
        print(f"剪枝参数: {params}")

    def log_performance_before(self, performance_data):
        """记录剪枝前性能"""
        self.experiment_log["performance_before"] = performance_data
        print(f"剪枝前性能: {performance_data}")

    def log_performance_after(self, performance_data):
        """记录剪枝后性能"""
        self.experiment_log["performance_after"] = performance_data
        print(f"剪枝后性能: {performance_data}")

    def log_pruning_results(self, results):
        """记录剪枝结果"""
        self.experiment_log["pruning_results"] = results
        print(f"剪枝结果: {results}")

    def log_pruned_model(self, model_path, model_info=None):
        """记录剪枝后模型信息"""
        self.experiment_log["pruned_model"] = {
            "path": str(model_path),
            "file_size_mb": os.path.getsize(model_path) / (1024 * 1024)
        }

        # 复制模型到日志目录
        import shutil
        model_name = Path(model_path).name
        saved_model_path = self.experiment_dir / model_name
        shutil.copy2(model_path, saved_model_path)
        self.experiment_log["pruned_model"]["saved_path"] = str(saved_model_path)

        print(f"剪枝后模型记录: {model_path}")
        print(f"模型已保存到: {saved_model_path}")

    def add_note(self, note):
        """添加备注"""
        if self.experiment_log["notes"]:
            self.experiment_log["notes"] += "\n" + note
        else:
            self.experiment_log["notes"] = note

    def calculate_improvements(self):
        """计算性能提升"""
        before = self.experiment_log["performance_before"]
        after = self.experiment_log["performance_after"]

        improvements = {}

        # 计算速度提升
        if "fps" in before and "fps" in after:
            fps_improvement = (after["fps"] / before["fps"] - 1) * 100
            improvements["fps_improvement_percent"] = fps_improvement

        # 计算时间减少
        if "inference_time_ms" in before and "inference_time_ms" in after:
            time_reduction = (1 - after["inference_time_ms"] / before["inference_time_ms"]) * 100
            improvements["time_reduction_percent"] = time_reduction

        # 计算模型大小减少
        if self.experiment_log["original_model"] and self.experiment_log["pruned_model"]:
            original_size = self.experiment_log["original_model"]["file_size_mb"]
            pruned_size = self.experiment_log["pruned_model"]["file_size_mb"]
            size_reduction = (1 - pruned_size / original_size) * 100
            improvements["size_reduction_percent"] = size_reduction

        self.experiment_log["improvements"] = improvements
        return improvements

    def generate_performance_plot(self):
        """生成性能对比图表"""
        before = self.experiment_log["performance_before"]
        after = self.experiment_log["performance_after"]

        if not before or not after:
            print("性能数据不完整，无法生成图表")
            return

        # 创建对比图表
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle(f'模型剪枝性能对比 - {self.experiment_id}', fontsize=16)

        # FPS对比
        if "fps" in before and "fps" in after:
            axes[0, 0].bar(['剪枝前', '剪枝后'], [before["fps"], after["fps"]],
                          color=['lightblue', 'lightgreen'])
            axes[0, 0].set_title('推理速度 (FPS)')
            axes[0, 0].set_ylabel('FPS')

            # 添加数值标签
            for i, v in enumerate([before["fps"], after["fps"]]):
                axes[0, 0].text(i, v + 0.5, f'{v:.2f}', ha='center')

        # 推理时间对比
        if "inference_time_ms" in before and "inference_time_ms" in after:
            axes[0, 1].bar(['剪枝前', '剪枝后'],
                          [before["inference_time_ms"], after["inference_time_ms"]],
                          color=['lightcoral', 'lightgreen'])
            axes[0, 1].set_title('推理时间 (ms)')
            axes[0, 1].set_ylabel('毫秒')

            # 添加数值标签
            for i, v in enumerate([before["inference_time_ms"], after["inference_time_ms"]]):
                axes[0, 1].text(i, v + 1, f'{v:.2f}', ha='center')

        # 模型大小对比
        if self.experiment_log["original_model"] and self.experiment_log["pruned_model"]:
            original_size = self.experiment_log["original_model"]["file_size_mb"]
            pruned_size = self.experiment_log["pruned_model"]["file_size_mb"]

            axes[1, 0].bar(['剪枝前', '剪枝后'], [original_size, pruned_size],
                          color=['orange', 'lightgreen'])
            axes[1, 0].set_title('模型文件大小 (MB)')
            axes[1, 0].set_ylabel('MB')

            # 添加数值标签
            for i, v in enumerate([original_size, pruned_size]):
                axes[1, 0].text(i, v + 0.5, f'{v:.2f}', ha='center')

        # 性能提升汇总
        improvements = self.calculate_improvements()
        if improvements:
            metrics = []
            values = []

            if "fps_improvement_percent" in improvements:
                metrics.append('FPS提升')
                values.append(improvements["fps_improvement_percent"])

            if "time_reduction_percent" in improvements:
                metrics.append('时间减少')
                values.append(improvements["time_reduction_percent"])

            if "size_reduction_percent" in improvements:
                metrics.append('大小减少')
                values.append(improvements["size_reduction_percent"])

            if metrics:
                colors = ['green' if v > 0 else 'red' for v in values]
                bars = axes[1, 1].bar(metrics, values, color=colors, alpha=0.7)
                axes[1, 1].set_title('性能提升百分比 (%)')
                axes[1, 1].set_ylabel('百分比 (%)')
                axes[1, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)

                # 添加数值标签
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + (1 if height > 0 else -3),
                                    f'{value:.1f}%', ha='center', va='bottom' if height > 0 else 'top')

        plt.tight_layout()

        # 保存图表
        plot_path = self.log_dir / "plots" / f"{self.experiment_id}_performance.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"性能对比图表已保存: {plot_path}")
        return plot_path

    def save_experiment(self):
        """保存实验记录"""
        self.experiment_log["end_time"] = datetime.datetime.now().isoformat()

        # 计算性能提升
        self.calculate_improvements()

        # 保存JSON记录
        json_path = self.experiment_dir / "experiment_log.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(self.experiment_log, f, indent=2, ensure_ascii=False)

        # 生成性能图表
        plot_path = self.generate_performance_plot()

        # 生成HTML报告
        html_path = self.generate_html_report()

        # 更新总体记录
        self.update_master_log()

        print(f"\n实验记录已保存:")
        print(f"  JSON记录: {json_path}")
        print(f"  HTML报告: {html_path}")
        print(f"  性能图表: {plot_path}")

        return {
            "json_path": json_path,
            "html_path": html_path,
            "plot_path": plot_path
        }

    def generate_html_report(self):
        """生成HTML报告"""
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型剪枝实验报告 - {self.experiment_id}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background: #f4f4f4; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
        .section {{ margin-bottom: 30px; }}
        .metrics {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }}
        .metric-card {{ background: #f9f9f9; padding: 15px; border-radius: 5px; text-align: center; }}
        .improvement {{ color: green; font-weight: bold; }}
        .degradation {{ color: red; font-weight: bold; }}
        table {{ width: 100%; border-collapse: collapse; margin-top: 10px; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .plot {{ text-align: center; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 模型剪枝实验报告</h1>
        <p><strong>实验ID:</strong> {self.experiment_id}</p>
        <p><strong>开始时间:</strong> {self.experiment_log.get('start_time', 'N/A')}</p>
        <p><strong>结束时间:</strong> {self.experiment_log.get('end_time', 'N/A')}</p>
    </div>

    <div class="section">
        <h2>📊 性能提升概览</h2>
        <div class="metrics">
        """

        improvements = self.experiment_log.get("improvements", {})

        if "fps_improvement_percent" in improvements:
            fps_imp = improvements["fps_improvement_percent"]
            css_class = "improvement" if fps_imp > 0 else "degradation"
            html_content += f"""
            <div class="metric-card">
                <h3>推理速度提升</h3>
                <p class="{css_class}">{fps_imp:+.1f}%</p>
            </div>
            """

        if "time_reduction_percent" in improvements:
            time_red = improvements["time_reduction_percent"]
            css_class = "improvement" if time_red > 0 else "degradation"
            html_content += f"""
            <div class="metric-card">
                <h3>推理时间减少</h3>
                <p class="{css_class}">{time_red:+.1f}%</p>
            </div>
            """

        if "size_reduction_percent" in improvements:
            size_red = improvements["size_reduction_percent"]
            css_class = "improvement" if size_red > 0 else "degradation"
            html_content += f"""
            <div class="metric-card">
                <h3>模型大小减少</h3>
                <p class="{css_class}">{size_red:+.1f}%</p>
            </div>
            """

        html_content += """
        </div>
    </div>

    <div class="section">
        <h2>🔧 剪枝配置</h2>
        <table>
            <tr><th>参数</th><th>值</th></tr>
        """

        html_content += f"""
            <tr><td>剪枝方法</td><td>{self.experiment_log.get('pruning_method', 'N/A')}</td></tr>
        """

        for key, value in self.experiment_log.get("pruning_params", {}).items():
            html_content += f"<tr><td>{key}</td><td>{value}</td></tr>"

        html_content += """
        </table>
    </div>

    <div class="section">
        <h2>📈 性能对比</h2>
        <table>
            <tr><th>指标</th><th>剪枝前</th><th>剪枝后</th><th>变化</th></tr>
        """

        before = self.experiment_log.get("performance_before", {})
        after = self.experiment_log.get("performance_after", {})

        if "fps" in before and "fps" in after:
            change = after["fps"] - before["fps"]
            change_str = f"+{change:.2f}" if change > 0 else f"{change:.2f}"
            html_content += f"""
            <tr>
                <td>推理速度 (FPS)</td>
                <td>{before["fps"]:.2f}</td>
                <td>{after["fps"]:.2f}</td>
                <td>{change_str}</td>
            </tr>
            """

        if "inference_time_ms" in before and "inference_time_ms" in after:
            change = after["inference_time_ms"] - before["inference_time_ms"]
            change_str = f"+{change:.2f}" if change > 0 else f"{change:.2f}"
            html_content += f"""
            <tr>
                <td>推理时间 (ms)</td>
                <td>{before["inference_time_ms"]:.2f}</td>
                <td>{after["inference_time_ms"]:.2f}</td>
                <td>{change_str}</td>
            </tr>
            """

        html_content += """
        </table>
    </div>

    <div class="plot">
        <h2>📊 性能对比图表</h2>
        <img src="../plots/{}_performance.png" alt="性能对比图表" style="max-width: 100%; height: auto;">
    </div>

    <div class="section">
        <h2>📝 备注</h2>
        <p>{}</p>
    </div>

</body>
</html>
        """.format(self.experiment_id, self.experiment_log.get("notes", "无"))

        # 保存HTML文件
        html_path = self.experiment_dir / "report.html"
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        return html_path

    def update_master_log(self):
        """更新总体记录"""
        master_log_path = self.log_dir / "master_log.json"

        # 读取现有记录
        if master_log_path.exists():
            with open(master_log_path, 'r', encoding='utf-8') as f:
                master_log = json.load(f)
        else:
            master_log = {"experiments": []}

        # 添加当前实验的摘要
        experiment_summary = {
            "experiment_id": self.experiment_id,
            "start_time": self.experiment_log["start_time"],
            "end_time": self.experiment_log["end_time"],
            "pruning_method": self.experiment_log["pruning_method"],
            "improvements": self.experiment_log.get("improvements", {}),
            "experiment_dir": str(self.experiment_dir)
        }

        master_log["experiments"].append(experiment_summary)

        # 保存更新后的记录
        with open(master_log_path, 'w', encoding='utf-8') as f:
            json.dump(master_log, f, indent=2, ensure_ascii=False)

        print(f"总体记录已更新: {master_log_path}")

def load_experiment_log(experiment_id, log_dir="pruning_logs"):
    """加载指定实验的记录"""
    log_path = Path(log_dir) / "experiments" / experiment_id / "experiment_log.json"

    if not log_path.exists():
        raise FileNotFoundError(f"实验记录不存在: {log_path}")

    with open(log_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def list_all_experiments(log_dir="pruning_logs"):
    """列出所有实验记录"""
    master_log_path = Path(log_dir) / "master_log.json"

    if not master_log_path.exists():
        print("没有找到实验记录")
        return []

    with open(master_log_path, 'r', encoding='utf-8') as f:
        master_log = json.load(f)

    return master_log.get("experiments", [])

if __name__ == "__main__":
    # 示例用法
    logger = PruningLogger()

    # 记录原始模型
    logger.log_original_model("yolov5/best_22.pt")

    # 记录剪枝配置
    logger.log_pruning_config("sparse_pruning", {"percent": 0.15, "strategy": "balanced"})

    # 记录性能数据
    logger.log_performance_before({"fps": 11.89, "inference_time_ms": 84.11})
    logger.log_performance_after({"fps": 19.69, "inference_time_ms": 50.79})

    # 记录剪枝后模型
    logger.log_pruned_model("optimized_sparse_yolo.pt")

    # 添加备注
    logger.add_note("使用15%稀疏剪枝，效果显著")

    # 保存实验记录
    logger.save_experiment()
