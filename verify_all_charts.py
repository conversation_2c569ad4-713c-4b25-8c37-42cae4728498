#!/usr/bin/env python3
"""
验证所有图表生成功能是否正常
"""

import os
import inspect
from worker_analysis import WorkerAnalyzer

def verify_all_functionality():
    """验证所有功能是否正确整合"""
    print("=" * 60)
    print("验证所有图表生成功能")
    print("=" * 60)
    
    # 检查所有方法是否存在
    required_methods = [
        '_generate_worker_count_charts',
        '_generate_per_second_chart',
        '_generate_per_frame_chart',
        '_generate_worker_activity_heatmap',
        '_generate_worker_statistics_chart',
        '_generate_worker_timeline_chart',
        '_fix_worker_stats_timing'
    ]
    
    print("检查必需的方法:")
    all_methods_exist = True
    for method_name in required_methods:
        if hasattr(WorkerAnalyzer, method_name):
            method = getattr(WorkerAnalyzer, method_name)
            if callable(method):
                print(f"  ✅ {method_name} - 存在且可调用")
            else:
                print(f"  ❌ {method_name} - 存在但不可调用")
                all_methods_exist = False
        else:
            print(f"  ❌ {method_name} - 不存在")
            all_methods_exist = False
    
    # 检查_analyze_activity_patterns方法的调用
    print("\n检查_analyze_activity_patterns方法调用:")
    if hasattr(WorkerAnalyzer, '_analyze_activity_patterns'):
        method = getattr(WorkerAnalyzer, '_analyze_activity_patterns')
        source = inspect.getsource(method)
        
        expected_calls = [
            '_generate_worker_count_charts',
            '_generate_worker_activity_heatmap',
            '_generate_worker_statistics_chart',
            '_generate_worker_timeline_chart'
        ]
        
        all_calls_present = True
        for call in expected_calls:
            if call in source:
                print(f"  ✅ 包含调用: {call}")
            else:
                print(f"  ❌ 缺少调用: {call}")
                all_calls_present = False
        
        # 显示方法内容
        print(f"\n_analyze_activity_patterns方法内容:")
        lines = source.split('\n')
        for i, line in enumerate(lines, 1):
            print(f"    {i:2d}: {line}")
    else:
        print("  ❌ _analyze_activity_patterns方法不存在")
        all_calls_present = False
    
    return all_methods_exist and all_calls_present

def check_generated_files():
    """检查生成的文件"""
    print("\n" + "=" * 60)
    print("检查生成的文件")
    print("=" * 60)
    
    test_dir = "test_moving_workers_output"
    
    expected_files = [
        "worker_count_per_second.png",      # 每秒工人计数
        "worker_count_per_frame.png",       # 每帧工人计数
        "worker_activity_heatmap.png",      # 工人活动热力图
        "worker_statistics_summary.png",    # 工人统计汇总
        "worker_timeline.png",              # 工人时间线
        "worker_heatmap_plt.png",           # 基础热力图
        "worker_stats.json",                # 统计数据
        "worker_report.html"                # HTML报告
    ]
    
    if os.path.exists(test_dir):
        print(f"检查目录: {test_dir}")
        all_files_exist = True
        
        for file_name in expected_files:
            file_path = os.path.join(test_dir, file_name)
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                if file_name.endswith('.png'):
                    print(f"  🎯 {file_name} - {size:,} bytes")
                else:
                    print(f"  📄 {file_name} - {size:,} bytes")
            else:
                print(f"  ❌ {file_name} - 不存在")
                all_files_exist = False
        
        # 列出所有实际生成的文件
        print(f"\n实际生成的所有文件:")
        actual_files = os.listdir(test_dir)
        for file in sorted(actual_files):
            file_path = os.path.join(test_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  📁 {file} - {size:,} bytes")
        
        return all_files_exist
    else:
        print(f"❌ 测试目录不存在: {test_dir}")
        return False

def check_duration_fix():
    """检查duration修复是否生效"""
    print("\n" + "=" * 60)
    print("检查Duration和Avg Speed修复")
    print("=" * 60)
    
    stats_file = "test_moving_workers_output/worker_stats.json"
    
    if os.path.exists(stats_file):
        import json
        with open(stats_file, 'r', encoding='utf-8') as f:
            stats = json.load(f)
        
        print("检查统计数据:")
        duration_fixed = True
        speed_fixed = True
        
        for worker_id, data in stats.items():
            duration = data.get('duration', 0)
            speed = data.get('avg_speed', 0)
            distance = data.get('total_distance', 0)
            
            print(f"  工人 {worker_id}:")
            print(f"    Duration: {duration:.2f}秒", end="")
            if duration > 0:
                print(" ✅")
            else:
                print(" ❌")
                duration_fixed = False
            
            print(f"    Avg Speed: {speed:.1f}px/s", end="")
            if distance > 0 and speed > 0:
                print(" ✅")
            elif distance == 0 and speed == 0:
                print(" ✅ (无移动)")
            else:
                print(" ❌")
                speed_fixed = False
        
        return duration_fixed and speed_fixed
    else:
        print(f"❌ 统计文件不存在: {stats_file}")
        return False

def main():
    print("🔍 开始验证所有图表生成功能")
    
    try:
        # 验证方法
        methods_ok = verify_all_functionality()
        
        # 检查文件
        files_ok = check_generated_files()
        
        # 检查修复
        fix_ok = check_duration_fix()
        
        print("\n" + "=" * 60)
        print("验证结果汇总")
        print("=" * 60)
        
        print(f"方法完整性: {'✅ 通过' if methods_ok else '❌ 失败'}")
        print(f"文件生成: {'✅ 通过' if files_ok else '❌ 失败'}")
        print(f"Duration/Speed修复: {'✅ 通过' if fix_ok else '❌ 失败'}")
        
        if methods_ok and files_ok and fix_ok:
            print("\n🎉 所有功能验证通过！")
            print("\n现在工人分析功能包含:")
            print("  🎯 每秒工人计数图表")
            print("  🎯 每帧工人计数图表")
            print("  🎯 工人活动热力图")
            print("  🎯 工人统计汇总图表")
            print("  🎯 工人时间线图表")
            print("  🎯 修复的Duration和Avg Speed计算")
        else:
            print("\n❌ 部分功能验证失败，请检查上述问题")
        
    except Exception as e:
        print(f"\n❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
