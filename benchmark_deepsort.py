#!/usr/bin/env python3
"""
DeepSORT性能测试脚本
比较不同优化方案的推理速度和准确性
"""

import torch
import numpy as np
import time
import cv2
import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append('.')
from deep_sort.deep_sort import DeepSort
from deep_sort_optimized import OptimizedDeepSort, create_optimized_tracker

class DeepSORTBenchmark:
    """DeepSORT性能测试类"""
    
    def __init__(self, device='cuda'):
        self.device = device
        self.test_data = None
        
    def generate_test_data(self, num_frames=100, num_objects=6):
        """生成测试数据"""
        print(f"生成测试数据: {num_frames}帧, {num_objects}个目标")
        
        # 创建模拟的检测数据
        frames = []
        for frame_idx in range(num_frames):
            detections = []
            confidences = []
            classes = []
            
            for obj_id in range(num_objects):
                # 模拟目标移动
                x = 100 + obj_id * 150 + frame_idx * 2
                y = 100 + obj_id * 80 + np.sin(frame_idx * 0.1) * 20
                w, h = 80, 160
                
                # 确保在图像范围内
                x = max(w//2, min(x, 1920 - w//2))
                y = max(h//2, min(y, 1080 - h//2))
                
                detections.append([x, y, w, h])
                confidences.append(0.8 + np.random.random() * 0.2)
                classes.append(0)  # 人类
            
            # 创建模拟图像
            frame = np.random.randint(0, 255, (1080, 1920, 3), dtype=np.uint8)
            
            frames.append({
                'frame': frame,
                'detections': np.array(detections),
                'confidences': np.array(confidences),
                'classes': np.array(classes)
            })
        
        self.test_data = frames
        print("测试数据生成完成")
    
    def benchmark_tracker(self, tracker, tracker_name, warmup_frames=10):
        """测试跟踪器性能"""
        if self.test_data is None:
            raise ValueError("请先生成测试数据")
        
        print(f"\n测试 {tracker_name}...")
        
        # 预热
        for i in range(min(warmup_frames, len(self.test_data))):
            data = self.test_data[i]
            _ = tracker.update(
                data['detections'], 
                data['confidences'], 
                data['classes'], 
                data['frame']
            )
        
        # 正式测试
        times = []
        total_detections = 0
        
        torch.cuda.synchronize() if self.device == 'cuda' else None
        start_time = time.time()
        
        for i, data in enumerate(self.test_data):
            frame_start = time.time()
            
            outputs = tracker.update(
                data['detections'], 
                data['confidences'], 
                data['classes'], 
                data['frame']
            )
            
            frame_end = time.time()
            times.append(frame_end - frame_start)
            total_detections += len(data['detections'])
        
        torch.cuda.synchronize() if self.device == 'cuda' else None
        total_time = time.time() - start_time
        
        # 计算统计信息
        avg_time = np.mean(times) * 1000  # ms
        std_time = np.std(times) * 1000   # ms
        fps = 1000 / avg_time
        total_fps = len(self.test_data) / total_time
        
        results = {
            'tracker_name': tracker_name,
            'avg_time_per_frame': avg_time,
            'std_time': std_time,
            'fps': fps,
            'total_fps': total_fps,
            'total_time': total_time,
            'total_frames': len(self.test_data),
            'total_detections': total_detections
        }
        
        return results
    
    def compare_trackers(self):
        """比较不同的跟踪器"""
        if self.test_data is None:
            self.generate_test_data()
        
        results = []
        
        # 测试原始DeepSORT
        try:
            print("初始化原始DeepSORT...")
            original_tracker = DeepSort(
                "osnet_x0_25",
                self.device,
                max_dist=0.2,
                max_iou_distance=0.7,
                max_age=70,
                n_init=3,
                nn_budget=100
            )
            result = self.benchmark_tracker(original_tracker, "原始DeepSORT")
            results.append(result)
        except Exception as e:
            print(f"原始DeepSORT测试失败: {e}")
        
        # 测试优化配置的DeepSORT
        try:
            print("初始化优化配置DeepSORT...")
            fast_tracker = DeepSort(
                "osnet_x0_25",
                self.device,
                max_dist=0.4,
                max_iou_distance=0.8,
                max_age=20,
                n_init=2,
                nn_budget=50
            )
            result = self.benchmark_tracker(fast_tracker, "优化配置DeepSORT")
            results.append(result)
        except Exception as e:
            print(f"优化配置DeepSORT测试失败: {e}")
        
        # 测试完全优化的DeepSORT
        try:
            print("初始化完全优化DeepSORT...")
            optimized_tracker = create_optimized_tracker(device=self.device)
            result = self.benchmark_tracker(optimized_tracker, "完全优化DeepSORT")
            results.append(result)
        except Exception as e:
            print(f"完全优化DeepSORT测试失败: {e}")
        
        return results
    
    def print_results(self, results):
        """打印测试结果"""
        print(f"\n{'='*80}")
        print("DeepSORT性能测试结果")
        print(f"{'='*80}")
        
        print(f"{'跟踪器':<20} {'平均耗时(ms)':<15} {'标准差(ms)':<15} {'FPS':<10} {'总FPS':<10}")
        print("-" * 80)
        
        baseline_fps = None
        for result in results:
            name = result['tracker_name']
            avg_time = result['avg_time_per_frame']
            std_time = result['std_time']
            fps = result['fps']
            total_fps = result['total_fps']
            
            if baseline_fps is None:
                baseline_fps = fps
                speedup = "基准"
            else:
                speedup = f"{fps/baseline_fps:.2f}x"
            
            print(f"{name:<20} {avg_time:<15.2f} {std_time:<15.2f} {fps:<10.1f} {total_fps:<10.1f}")
        
        print("-" * 80)
        
        # 详细分析
        print("\n详细分析:")
        for i, result in enumerate(results):
            print(f"\n{i+1}. {result['tracker_name']}:")
            print(f"   - 总处理时间: {result['total_time']:.2f}s")
            print(f"   - 总帧数: {result['total_frames']}")
            print(f"   - 总检测数: {result['total_detections']}")
            print(f"   - 平均每帧耗时: {result['avg_time_per_frame']:.2f}ms")
            print(f"   - 理论FPS: {result['fps']:.1f}")
            print(f"   - 实际FPS: {result['total_fps']:.1f}")
            
            if i > 0:
                speedup = result['fps'] / results[0]['fps']
                print(f"   - 相对加速: {speedup:.2f}x")
    
    def memory_usage_test(self):
        """内存使用测试"""
        print(f"\n{'='*50}")
        print("内存使用测试")
        print(f"{'='*50}")
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            initial_memory = torch.cuda.memory_allocated() / 1024 / 1024  # MB
            
            # 测试不同配置的内存使用
            configs = [
                ("小特征库(50)", {"nn_budget": 50}),
                ("中特征库(100)", {"nn_budget": 100}),
                ("大特征库(200)", {"nn_budget": 200}),
            ]
            
            for name, config in configs:
                torch.cuda.empty_cache()
                start_memory = torch.cuda.memory_allocated() / 1024 / 1024
                
                try:
                    tracker = DeepSort("osnet_x0_25", self.device, **config)
                    
                    # 运行一些帧
                    for i in range(20):
                        if i < len(self.test_data):
                            data = self.test_data[i]
                            _ = tracker.update(
                                data['detections'], 
                                data['confidences'], 
                                data['classes'], 
                                data['frame']
                            )
                    
                    end_memory = torch.cuda.memory_allocated() / 1024 / 1024
                    memory_used = end_memory - start_memory
                    
                    print(f"{name}: {memory_used:.1f} MB")
                    
                except Exception as e:
                    print(f"{name}: 测试失败 - {e}")

def main():
    """主函数"""
    print("DeepSORT性能测试工具")
    print("=" * 50)
    
    # 检查设备
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"使用设备: {device}")
    
    # 创建测试器
    benchmark = DeepSORTBenchmark(device=device)
    
    try:
        # 生成测试数据
        benchmark.generate_test_data(num_frames=50, num_objects=6)
        
        # 比较跟踪器
        results = benchmark.compare_trackers()
        
        # 打印结果
        benchmark.print_results(results)
        
        # 内存使用测试
        benchmark.memory_usage_test()
        
        # 推荐配置
        print(f"\n{'='*50}")
        print("推荐配置")
        print(f"{'='*50}")
        
        if len(results) >= 2:
            best_result = max(results[1:], key=lambda x: x['fps'])
            print(f"推荐使用: {best_result['tracker_name']}")
            print(f"性能提升: {best_result['fps']/results[0]['fps']:.2f}x")
            print(f"配置文件: deep_sort/configs/deep_sort_fast.yaml")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
