#!/usr/bin/env python3
"""
查看所有剪枝实验记录
"""

import json
import os
from pathlib import Path
from datetime import datetime

def load_master_log(log_dir="pruning_logs"):
    """加载总体实验记录"""
    master_log_path = Path(log_dir) / "master_log.json"
    
    if not master_log_path.exists():
        print("没有找到实验记录")
        return []
    
    with open(master_log_path, 'r', encoding='utf-8') as f:
        master_log = json.load(f)
    
    return master_log.get("experiments", [])

def format_datetime(iso_string):
    """格式化日期时间"""
    try:
        dt = datetime.fromisoformat(iso_string)
        return dt.strftime("%Y-%m-%d %H:%M:%S")
    except:
        return iso_string

def format_percentage(value):
    """格式化百分比"""
    if value > 0:
        return f"+{value:.1f}%"
    else:
        return f"{value:.1f}%"

def get_experiment_details(experiment_id, log_dir="pruning_logs"):
    """获取实验详细信息"""
    exp_path = Path(log_dir) / "experiments" / experiment_id / "experiment_log.json"
    
    if not exp_path.exists():
        return None
    
    with open(exp_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def main():
    print("=" * 80)
    print("🔧 YOLO模型剪枝实验记录查看器")
    print("=" * 80)
    
    # 加载所有实验
    experiments = load_master_log()
    
    if not experiments:
        print("没有找到任何实验记录")
        return
    
    print(f"\n找到 {len(experiments)} 个剪枝实验:")
    print("-" * 80)
    
    for i, exp in enumerate(experiments, 1):
        exp_id = exp["experiment_id"]
        start_time = format_datetime(exp["start_time"])
        end_time = format_datetime(exp["end_time"])
        method = exp["pruning_method"]
        improvements = exp.get("improvements", {})
        
        print(f"\n📊 实验 {i}: {exp_id}")
        print(f"   时间: {start_time} - {end_time}")
        print(f"   方法: {method}")
        
        # 显示性能变化
        if improvements:
            fps_change = improvements.get("fps_improvement_percent", 0)
            time_change = improvements.get("time_reduction_percent", 0)
            size_change = improvements.get("size_reduction_percent", 0)
            
            print(f"   性能变化:")
            print(f"     FPS: {format_percentage(fps_change)}")
            print(f"     推理时间: {format_percentage(time_change)}")
            print(f"     模型大小: {format_percentage(size_change)}")
        
        # 获取详细信息
        details = get_experiment_details(exp_id)
        if details:
            original_model = details.get("original_model", {})
            pruning_params = details.get("pruning_params", {})
            pruning_results = details.get("pruning_results", {})
            
            print(f"   原始模型: {original_model.get('file_size_mb', 'N/A'):.1f} MB")
            
            if "percent" in pruning_params:
                print(f"   剪枝比例: {pruning_params['percent']:.1%}")
            
            if "actual_prune_ratio" in pruning_results:
                actual_ratio = pruning_results["actual_prune_ratio"]
                print(f"   实际剪枝: {actual_ratio:.1%}")
                
            if "pruned_channels" in pruning_results and "total_channels" in pruning_results:
                pruned = pruning_results["pruned_channels"]
                total = pruning_results["total_channels"]
                print(f"   剪枝通道: {pruned:,} / {total:,}")
        
        print(f"   实验目录: {exp['experiment_dir']}")
    
    print("\n" + "=" * 80)
    print("📁 实验文件结构:")
    print("=" * 80)
    
    # 显示文件结构
    log_dir = Path("pruning_logs")
    if log_dir.exists():
        print(f"\n📂 {log_dir}/")
        
        # 显示实验目录
        exp_dir = log_dir / "experiments"
        if exp_dir.exists():
            print(f"  📂 experiments/")
            for exp_folder in sorted(exp_dir.iterdir()):
                if exp_folder.is_dir():
                    print(f"    📂 {exp_folder.name}/")
                    for file in sorted(exp_folder.iterdir()):
                        if file.is_file():
                            size = file.stat().st_size
                            if size > 1024*1024:
                                size_str = f"{size/(1024*1024):.1f} MB"
                            elif size > 1024:
                                size_str = f"{size/1024:.1f} KB"
                            else:
                                size_str = f"{size} B"
                            
                            if file.suffix == '.pt':
                                icon = "🤖"
                            elif file.suffix == '.json':
                                icon = "📄"
                            elif file.suffix == '.html':
                                icon = "🌐"
                            else:
                                icon = "📄"
                            
                            print(f"      {icon} {file.name} ({size_str})")
        
        # 显示图表目录
        plots_dir = log_dir / "plots"
        if plots_dir.exists() and any(plots_dir.iterdir()):
            print(f"  📂 plots/")
            for plot_file in sorted(plots_dir.iterdir()):
                if plot_file.is_file():
                    size = plot_file.stat().st_size / 1024
                    print(f"    📊 {plot_file.name} ({size:.1f} KB)")
        
        # 显示总体记录
        master_file = log_dir / "master_log.json"
        if master_file.exists():
            size = master_file.stat().st_size
            print(f"  📋 master_log.json ({size} B)")
    
    print("\n" + "=" * 80)
    print("💡 使用建议:")
    print("=" * 80)
    print("1. 查看HTML报告: 打开 experiments/[实验ID]/report.html")
    print("2. 查看性能图表: 打开 plots/[实验ID]_performance.png")
    print("3. 使用剪枝模型: 加载 experiments/[实验ID]/sparse_pruned_yolo_*.pt")
    print("4. 查看详细数据: 打开 experiments/[实验ID]/experiment_log.json")

if __name__ == "__main__":
    main()
