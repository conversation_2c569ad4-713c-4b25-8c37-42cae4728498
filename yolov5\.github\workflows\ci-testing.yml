# YOLOv5 🚀 by Ultralytics, GPL-3.0 license

name: CI CPU testing

on: # https://help.github.com/en/actions/reference/events-that-trigger-workflows
  push:
    branches: [ master ]
  pull_request:
    # The branches below must be a subset of the branches above
    branches: [ master ]
  schedule:
    - cron: '0 0 * * *'  # Runs at 00:00 UTC every day

jobs:
  cpu-tests:

    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest, macos-latest, windows-latest ]
        python-version: [ 3.9 ]
        model: [ 'yolov5n' ]  # models to test

    # Timeout: https://stackoverflow.com/a/59076067/4521646
    timeout-minutes: 60
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v3
        with:
          python-version: ${{ matrix.python-version }}

      # Note: This uses an internal pip API and may not always work
      # https://github.com/actions/cache/blob/master/examples.md#multiple-oss-in-a-workflow
      - name: Get pip cache
        id: pip-cache
        run: |
          python -c "from pip._internal.locations import USER_CACHE_DIR; print('::set-output name=dir::' + USER_CACHE_DIR)"

      - name: Cache pip
        uses: actions/cache@v3
        with:
          path: ${{ steps.pip-cache.outputs.dir }}
          key: ${{ runner.os }}-${{ matrix.python-version }}-pip-${{ hashFiles('requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-${{ matrix.python-version }}-pip-

      # Known Keras 2.7.0 issue: https://github.com/ultralytics/yolov5/pull/5486
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -qr requirements.txt -f https://download.pytorch.org/whl/cpu/torch_stable.html \
            onnx tensorflow-cpu  # wandb
          python --version
          pip --version
          pip list
        shell: bash

      # - name: W&B login
      #   run: wandb login ****************************************

      # - name: Download data
      #   run: |
      #     curl -L -o tmp.zip https://github.com/ultralytics/yolov5/releases/download/v1.0/coco128.zip
      #     unzip -q tmp.zip -d ../datasets

      - name: Tests workflow
        run: |
          # export PYTHONPATH="$PWD"  # to run '$ python *.py' files in subdirectories
          d=cpu  # device
          weights=runs/train/exp/weights/best.pt

          # Train
          python train.py --img 64 --batch 32 --weights ${{ matrix.model }}.pt --cfg ${{ matrix.model }}.yaml --epochs 1 --device $d
          # Val
          python val.py --img 64 --batch 32 --weights ${{ matrix.model }}.pt --device $d
          python val.py --img 64 --batch 32 --weights $weights --device $d
          # Detect
          python detect.py --weights ${{ matrix.model }}.pt --device $d
          python detect.py --weights $weights --device $d
          python hubconf.py  # hub
          # Export
          python models/yolo.py --cfg ${{ matrix.model }}.yaml  # build PyTorch model
          python models/tf.py --weights ${{ matrix.model }}.pt  # build TensorFlow model
          python export.py --weights ${{ matrix.model }}.pt --img 64 --include torchscript onnx  # export
          # Python
          python - <<EOF
          import torch
          # model = torch.hub.load('ultralytics/yolov5', 'custom', path=$weights)
          EOF

        shell: bash
