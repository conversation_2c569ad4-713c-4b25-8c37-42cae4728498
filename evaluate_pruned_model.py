import os
import sys
import argparse
import time
import torch
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import cv2

# 添加YOLOv5路径
sys.path.insert(0, './yolov5')

from yolov5.models.common import DetectMultiBackend
from yolov5.utils.general import (check_img_size, non_max_suppression, scale_boxes)
from yolov5.utils.torch_utils import select_device, time_sync
from yolov5.utils.metrics import box_iou
from yolov5.utils.dataloaders import create_dataloader

def load_model(weights, device, data_yaml, img_size=640, half=False):
    """
    加载YOLOv5模型
    
    参数:
        weights (str): 模型权重文件路径
        device (str): 设备 ('cpu' 或 'cuda:0' 等)
        data_yaml (str): 数据集配置文件
        img_size (int): 图像大小
        half (bool): 是否使用半精度
    
    返回:
        model: 加载的模型
    """
    print(f"加载模型: {weights}")
    model = DetectMultiBackend(weights, device=device, dnn=False, data=data_yaml, fp16=half)
    stride, names, pt = model.stride, model.names, model.pt
    img_size = check_img_size(img_size, s=stride)  # 检查图像大小
    
    # 预热模型
    model.warmup(imgsz=(1, 3, img_size, img_size))
    
    return model, stride, names, pt, img_size

def count_parameters(model):
    """
    计算模型参数数量
    
    参数:
        model: PyTorch模型
    
    返回:
        total_params (int): 总参数数量
        trainable_params (int): 可训练参数数量
    """
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    return total_params, trainable_params

def calculate_flops(model, img_size=640):
    """
    计算模型FLOPs
    
    参数:
        model: PyTorch模型
        img_size (int): 图像大小
    
    返回:
        flops (float): 浮点运算次数
    """
    try:
        from thop import profile
        input_tensor = torch.randn(1, 3, img_size, img_size).to(next(model.parameters()).device)
        flops, _ = profile(model, inputs=(input_tensor,))
        return flops
    except ImportError:
        print("请安装thop库以计算FLOPs: pip install thop")
        return None
    except Exception as e:
        print(f"计算FLOPs时出错: {e}")
        return None

def benchmark_inference_speed(model, img_size=640, iterations=100):
    """
    基准测试模型推理速度
    
    参数:
        model: PyTorch模型
        img_size (int): 图像大小
        iterations (int): 迭代次数
    
    返回:
        fps (float): 每秒帧数
        latency (float): 平均延迟（毫秒）
    """
    device = next(model.parameters()).device
    dtype = next(model.parameters()).dtype
    dummy_input = torch.randn(1, 3, img_size, img_size, dtype=dtype).to(device)
    model.eval()
    
    # 预热
    with torch.no_grad():
        for _ in range(10):
            _ = model(dummy_input)
    
    # 计时
    latencies = []
    with torch.no_grad():
        for _ in range(iterations):
            start_time = time_sync()
            _ = model(dummy_input)
            end_time = time_sync()
            latencies.append((end_time - start_time) * 1000)  # 转换为毫秒
    
    # 计算统计数据
    latency = np.mean(latencies)
    fps = 1000 / latency
    
    return fps, latency, np.std(latencies), np.min(latencies), np.max(latencies)

def evaluate_model(model, dataloader, conf_thres=0.25, iou_thres=0.45, max_det=1000):
    """
    评估模型在数据集上的性能
    
    参数:
        model: PyTorch模型
        dataloader: 数据加载器
        conf_thres (float): 置信度阈值
        iou_thres (float): IoU阈值
        max_det (int): 每张图像的最大检测数量
    
    返回:
        metrics (dict): 评估指标
    """
    model.eval()
    device = next(model.parameters()).device
    
    stats = []
    for batch_i, (img, targets, paths, shapes) in enumerate(dataloader):
        img = img.to(device, non_blocking=True)
        img = img.float()  # uint8 to fp16/32
        img /= 255.0  # 0 - 255 to 0.0 - 1.0
        targets = targets.to(device)
        
        # 推理
        with torch.no_grad():
            pred = model(img)
            pred = non_max_suppression(pred, conf_thres, iou_thres, classes=None, agnostic=False, max_det=max_det)
        
        # 计算指标
        for si, pred_i in enumerate(pred):
            labels = targets[targets[:, 0] == si, 1:]
            nl, npr = labels.shape[0], pred_i.shape[0]
            
            if npr == 0:
                if nl:
                    stats.append((torch.zeros(0, 1), torch.Tensor(), torch.Tensor(), torch.zeros(nl, 1)))
                continue
            
            # 将预测结果缩放到原始图像大小
            predn = pred_i.clone()
            scale_boxes(img[si].shape[1:], predn[:, :4], shapes[si][0], shapes[si][1])
            
            # 计算IoU
            correct = torch.zeros(pred_i.shape[0], 1).to(device)
            if nl:
                detected = []
                tcls_tensor = labels[:, 0]
                
                # 目标框
                tbox = labels[:, 1:5]
                scale_boxes(img[si].shape[1:], tbox, shapes[si][0], shapes[si][1])
                
                # 每个预测框与所有目标框的IoU
                for j, pred_j in enumerate(predn):
                    iou, j_idx = box_iou(pred_j[0:4].unsqueeze(0), tbox).max(1)
                    if iou >= iou_thres and j_idx not in detected:
                        correct[j] = 1
                        detected.append(j_idx)
            
            stats.append((correct, pred_i[:, 4], pred_i[:, 5], tcls_tensor))
    
    # 计算mAP
    stats = [np.concatenate(x, 0) for x in zip(*stats)]
    if len(stats) and stats[0].any():
        tp, fp, p, r, f1, ap, ap_class = ap_per_class(*stats)
        ap50, ap = ap[:, 0], ap.mean(1)  # AP@0.5, AP@0.5:0.95
        mp, mr, map50, map = p.mean(), r.mean(), ap50.mean(), ap.mean()
    else:
        mp, mr, map50, map = 0.0, 0.0, 0.0, 0.0
    
    return {
        'precision': float(mp),
        'recall': float(mr),
        'mAP@0.5': float(map50),
        'mAP@0.5:0.95': float(map),
    }

def compare_models(original_model_path, pruned_model_path, data_yaml, img_size=640, batch_size=16, device=''):
    """
    比较原始模型和剪枝后模型的性能
    
    参数:
        original_model_path (str): 原始模型路径
        pruned_model_path (str): 剪枝后模型路径
        data_yaml (str): 数据集配置文件
        img_size (int): 图像大小
        batch_size (int): 批次大小
        device (str): 设备
    """
    # 选择设备
    device = select_device(device)
    
    # 加载模型
    original_model, stride, names, pt, img_size = load_model(original_model_path, device, data_yaml, img_size)
    pruned_model, _, _, _, _ = load_model(pruned_model_path, device, data_yaml, img_size)
    
    # 1. 模型大小比较
    orig_size = os.path.getsize(original_model_path) / (1024 * 1024)  # MB
    pruned_size = os.path.getsize(pruned_model_path) / (1024 * 1024)  # MB
    
    orig_params, orig_trainable = count_parameters(original_model)
    pruned_params, pruned_trainable = count_parameters(pruned_model)
    
    # 2. 计算FLOPs
    try:
        import thop
        orig_flops = calculate_flops(original_model, img_size)
        pruned_flops = calculate_flops(pruned_model, img_size)
    except:
        orig_flops = None
        pruned_flops = None
    
    # 3. 推理速度比较
    orig_fps, orig_latency, orig_std, orig_min, orig_max = benchmark_inference_speed(original_model, img_size)
    pruned_fps, pruned_latency, pruned_std, pruned_min, pruned_max = benchmark_inference_speed(pruned_model, img_size)
    
    # 4. 准确率比较
    # 创建验证数据加载器
    dataloader = create_dataloader(
        Path(data_yaml).parent / Path(data_yaml).stem / 'val',
        img_size,
        batch_size,
        stride,
        single_cls=False,
        pad=0.5,
        rect=True,
        workers=8
    )[0]
    
    print("评估原始模型...")
    orig_metrics = evaluate_model(original_model, dataloader)
    
    print("评估剪枝后模型...")
    pruned_metrics = evaluate_model(pruned_model, dataloader)
    
    # 5. 打印比较结果
    print("\n" + "="*50)
    print("模型比较结果:")
    print("="*50)
    
    print(f"\n1. 模型大小比较:")
    print(f"   原始模型: {orig_size:.2f} MB")
    print(f"   剪枝模型: {pruned_size:.2f} MB")
    print(f"   减少比例: {(1 - pruned_size/orig_size)*100:.2f}%")
    
    print(f"\n2. 参数数量比较:")
    print(f"   原始模型: {orig_params:,} 参数")
    print(f"   剪枝模型: {pruned_params:,} 参数")
    print(f"   减少比例: {(1 - pruned_params/orig_params)*100:.2f}%")
    
    if orig_flops and pruned_flops:
        print(f"\n3. 计算量比较 (FLOPs):")
        print(f"   原始模型: {orig_flops/1e9:.2f} GFLOPs")
        print(f"   剪枝模型: {pruned_flops/1e9:.2f} GFLOPs")
        print(f"   减少比例: {(1 - pruned_flops/orig_flops)*100:.2f}%")
    
    print(f"\n4. 推理速度比较:")
    print(f"   原始模型: {orig_fps:.2f} FPS (延迟: {orig_latency:.2f} ms ± {orig_std:.2f})")
    print(f"   剪枝模型: {pruned_fps:.2f} FPS (延迟: {pruned_latency:.2f} ms ± {pruned_std:.2f})")
    print(f"   速度提升: {(pruned_fps/orig_fps - 1)*100:.2f}%")
    
    print(f"\n5. 准确率比较:")
    print(f"   原始模型: mAP@0.5={orig_metrics['mAP@0.5']:.4f}, mAP@0.5:0.95={orig_metrics['mAP@0.5:0.95']:.4f}")
    print(f"   剪枝模型: mAP@0.5={pruned_metrics['mAP@0.5']:.4f}, mAP@0.5:0.95={pruned_metrics['mAP@0.5:0.95']:.4f}")
    print(f"   精度变化: mAP@0.5 {(pruned_metrics['mAP@0.5']/orig_metrics['mAP@0.5'] - 1)*100:.2f}%, mAP@0.5:0.95 {(pruned_metrics['mAP@0.5:0.95']/orig_metrics['mAP@0.5:0.95'] - 1)*100:.2f}%")
    
    # 6. 生成比较图表
    labels = ['原始模型', '剪枝模型']
    
    # 模型大小比较
    plt.figure(figsize=(15, 10))
    plt.subplot(2, 2, 1)
    plt.bar(labels, [orig_size, pruned_size])
    plt.title('模型大小 (MB)')
    plt.ylabel('MB')
    
    # 参数数量比较
    plt.subplot(2, 2, 2)
    plt.bar(labels, [orig_params, pruned_params])
    plt.title('参数数量')
    plt.ylabel('参数数量')
    
    # 推理速度比较
    plt.subplot(2, 2, 3)
    plt.bar(labels, [orig_fps, pruned_fps])
    plt.title('推理速度 (FPS)')
    plt.ylabel('FPS')
    
    # 准确率比较
    plt.subplot(2, 2, 4)
    plt.bar(labels, [orig_metrics['mAP@0.5'], pruned_metrics['mAP@0.5']])
    plt.title('准确率 (mAP@0.5)')
    plt.ylabel('mAP')
    
    plt.tight_layout()
    plt.savefig('model_comparison.png')
    print(f"\n比较图表已保存到 'model_comparison.png'")

def main():
    parser = argparse.ArgumentParser(description='YOLOv5模型性能比较工具')
    parser.add_argument('--original', type=str, default='yolov5/best_22.pt', help='原始模型路径')
    parser.add_argument('--pruned', type=str, default='pruned_model.pt', help='剪枝后模型路径')
    parser.add_argument('--data', type=str, default='yolov5/data/my_worker.yaml', help='数据集配置文件')
    parser.add_argument('--img-size', type=int, default=640, help='图像大小')
    parser.add_argument('--batch-size', type=int, default=16, help='批次大小')
    parser.add_argument('--device', type=str, default='', help='设备 (cpu 或 cuda:0 等)')
    
    args = parser.parse_args()
    
    compare_models(
        args.original,
        args.pruned,
        args.data,
        args.img_size,
        args.batch_size,
        args.device
    )

if __name__ == '__main__':
    main()
