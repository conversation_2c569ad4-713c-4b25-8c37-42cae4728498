#!/usr/bin/env python3
"""
优化的DeepSORT实现
包含多种加速技术：模型剪枝、半精度推理、批处理优化等
"""

import numpy as np
import torch
import sys
import time
from os.path import exists as file_exists, join
from collections import deque

# 导入原始DeepSORT组件
sys.path.append('deep_sort')
from deep_sort.sort.nn_matching import NearestNeighborDistanceMetric
from deep_sort.sort.detection import Detection
from deep_sort.sort.tracker import Tracker

sys.path.append('deep_sort/deep/reid')
from torchreid.utils import FeatureExtractor

class OptimizedFeatureExtractor:
    """优化的特征提取器"""
    
    def __init__(self, model_name, model_path, device, 
                 enable_half_precision=False, batch_size=8, 
                 feature_extract_interval=1):
        """
        初始化优化的特征提取器
        
        Args:
            model_name: 模型名称
            model_path: 模型路径
            device: 设备
            enable_half_precision: 是否启用半精度
            batch_size: 批处理大小
            feature_extract_interval: 特征提取间隔
        """
        self.device = device
        self.enable_half_precision = enable_half_precision
        self.batch_size = batch_size
        self.feature_extract_interval = feature_extract_interval
        self.frame_count = 0
        self.feature_cache = {}
        
        # 加载模型
        self.extractor = FeatureExtractor(
            model_name=model_name,
            model_path=model_path,
            device=str(device)
        )
        
        # 半精度优化
        if enable_half_precision and device == 'cuda':
            self.extractor.model = self.extractor.model.half()
            print("启用半精度推理")
        
        # 预热模型
        self._warmup()
    
    def _warmup(self):
        """预热模型"""
        dummy_input = np.random.randint(0, 255, (128, 256, 3), dtype=np.uint8)
        try:
            _ = self.extractor([dummy_input])
            print("模型预热完成")
        except Exception as e:
            print(f"模型预热失败: {e}")
    
    def extract_features(self, im_crops, track_ids=None):
        """
        批量提取特征
        
        Args:
            im_crops: 图像裁剪列表
            track_ids: 轨迹ID列表（用于缓存）
        
        Returns:
            features: 特征向量数组
        """
        if not im_crops:
            return np.array([])
        
        self.frame_count += 1
        
        # 检查是否需要跳帧
        if self.frame_count % self.feature_extract_interval != 0:
            # 使用缓存的特征或返回零特征
            if track_ids is not None:
                features = []
                for tid in track_ids:
                    if tid in self.feature_cache:
                        features.append(self.feature_cache[tid])
                    else:
                        # 创建零特征
                        features.append(np.zeros(512))  # OSNet特征维度
                return np.array(features) if features else np.array([])
        
        # 批处理提取特征
        features = []
        for i in range(0, len(im_crops), self.batch_size):
            batch = im_crops[i:i + self.batch_size]
            batch_features = self._extract_batch(batch)
            features.extend(batch_features)
        
        # 更新缓存
        if track_ids is not None and len(track_ids) == len(features):
            for tid, feat in zip(track_ids, features):
                self.feature_cache[tid] = feat
        
        return np.array(features)
    
    def _extract_batch(self, im_crops):
        """提取单个批次的特征"""
        try:
            # 转换数据类型
            if self.enable_half_precision and self.device == 'cuda':
                # 半精度推理需要特殊处理
                features = self.extractor(im_crops)
                return features.numpy() if hasattr(features, 'numpy') else features
            else:
                features = self.extractor(im_crops)
                return features.numpy() if hasattr(features, 'numpy') else features
        except Exception as e:
            print(f"特征提取错误: {e}")
            # 返回零特征
            return [np.zeros(512) for _ in im_crops]
    
    def clear_cache(self):
        """清理特征缓存"""
        self.feature_cache.clear()

class OptimizedDeepSort:
    """优化的DeepSORT跟踪器"""
    
    def __init__(self, model_path, device='cuda', 
                 max_dist=0.4, max_iou_distance=0.8, max_age=20, 
                 n_init=2, nn_budget=50, enable_half_precision=False,
                 batch_size=8, feature_extract_interval=1,
                 cache_clear_interval=100):
        """
        初始化优化的DeepSORT
        
        Args:
            model_path: 模型路径
            device: 设备
            max_dist: 最大特征距离
            max_iou_distance: 最大IOU距离
            max_age: 轨迹最大年龄
            n_init: 初始化帧数
            nn_budget: 特征库大小
            enable_half_precision: 启用半精度
            batch_size: 批处理大小
            feature_extract_interval: 特征提取间隔
            cache_clear_interval: 缓存清理间隔
        """
        self.device = device
        self.cache_clear_interval = cache_clear_interval
        self.frame_count = 0
        
        # 确定模型名称
        if 'osnet' in model_path.lower():
            if 'x0_25' in model_path:
                model_name = 'osnet_x0_25'
            elif 'x0_5' in model_path:
                model_name = 'osnet_x0_5'
            elif 'x0_75' in model_path:
                model_name = 'osnet_x0_75'
            else:
                model_name = 'osnet_x1_0'
        else:
            model_name = 'osnet_x0_25'  # 默认使用最轻量级模型
        
        # 初始化优化的特征提取器
        self.extractor = OptimizedFeatureExtractor(
            model_name=model_name,
            model_path=model_path,
            device=device,
            enable_half_precision=enable_half_precision,
            batch_size=batch_size,
            feature_extract_interval=feature_extract_interval
        )
        
        # 初始化跟踪器
        metric = NearestNeighborDistanceMetric("euclidean", max_dist, nn_budget)
        self.tracker = Tracker(metric, max_iou_distance=max_iou_distance, 
                              max_age=max_age, n_init=n_init)
        
        print(f"优化DeepSORT初始化完成:")
        print(f"  - 模型: {model_name}")
        print(f"  - 设备: {device}")
        print(f"  - 半精度: {enable_half_precision}")
        print(f"  - 批处理大小: {batch_size}")
        print(f"  - 特征提取间隔: {feature_extract_interval}")
    
    def update(self, bbox_xywh, confidences, classes, ori_img, use_yolo_preds=False):
        """
        更新跟踪器
        
        Args:
            bbox_xywh: 边界框 (x_center, y_center, width, height)
            confidences: 置信度
            classes: 类别
            ori_img: 原始图像
            use_yolo_preds: 是否使用YOLO预测
        
        Returns:
            outputs: 跟踪结果
        """
        self.frame_count += 1
        self.height, self.width = ori_img.shape[:2]
        
        # 提取特征
        start_time = time.time()
        features = self._get_features(bbox_xywh, ori_img)
        feature_time = time.time() - start_time
        
        # 创建检测
        bbox_tlwh = self._xywh_to_tlwh(bbox_xywh)
        detections = [Detection(bbox_tlwh[i], conf, features[i]) 
                     for i, conf in enumerate(confidences)]
        
        # 更新跟踪器
        start_time = time.time()
        self.tracker.predict()
        self.tracker.update(detections, classes)
        track_time = time.time() - start_time
        
        # 定期清理缓存
        if self.frame_count % self.cache_clear_interval == 0:
            self.extractor.clear_cache()
        
        # 输出结果
        outputs = []
        for track in self.tracker.tracks:
            if not track.is_confirmed() or track.time_since_update > 1:
                continue
            
            if use_yolo_preds:
                det = track.get_yolo_pred()
                x1, y1, x2, y2 = self._tlwh_to_xyxy(det.tlwh)
            else:
                box = track.to_tlwh()
                x1, y1, x2, y2 = self._tlwh_to_xyxy(box)
            
            track_id = track.track_id
            class_id = track.class_id
            outputs.append(np.array([x1, y1, x2, y2, track_id, class_id], dtype=np.int64))
        
        if len(outputs) > 0:
            outputs = np.stack(outputs, axis=0)
        
        return outputs
    
    def _get_features(self, bbox_xywh, ori_img):
        """获取特征"""
        im_crops = []
        for box in bbox_xywh:
            x1, y1, x2, y2 = self._xywh_to_xyxy(box)
            im = ori_img[y1:y2, x1:x2]
            im_crops.append(im)
        
        if im_crops:
            features = self.extractor.extract_features(im_crops)
        else:
            features = np.array([])
        
        return features
    
    def _xywh_to_xyxy(self, bbox_xywh):
        """转换边界框格式"""
        x, y, w, h = bbox_xywh
        x1 = max(int(x - w / 2.), 0)
        x2 = min(int(x + w / 2.), self.width - 1)
        y1 = max(int(y - h / 2.), 0)
        y2 = min(int(y + h / 2.), self.height - 1)
        return x1, y1, x2, y2
    
    def _xywh_to_tlwh(self, bbox_xywh):
        """转换为tlwh格式"""
        ret = np.asarray(bbox_xywh).copy()
        ret[:2] -= ret[2:] / 2
        return ret
    
    def _tlwh_to_xyxy(self, bbox_tlwh):
        """转换为xyxy格式"""
        x, y, w, h = bbox_tlwh
        x1 = max(int(x), 0)
        x2 = min(int(x + w), self.width - 1)
        y1 = max(int(y), 0)
        y2 = min(int(y + h), self.height - 1)
        return x1, y1, x2, y2

# 便捷函数
def create_optimized_tracker(config_path="deep_sort/configs/deep_sort_fast.yaml", 
                           model_path=None, device='cuda'):
    """
    创建优化的跟踪器
    
    Args:
        config_path: 配置文件路径
        model_path: 模型路径
        device: 设备
    
    Returns:
        tracker: 优化的DeepSORT跟踪器
    """
    if model_path is None:
        model_path = "deep_sort/deep/checkpoint/osnet_x0_25.pth"
    
    # 使用优化参数
    tracker = OptimizedDeepSort(
        model_path=model_path,
        device=device,
        max_dist=0.4,
        max_iou_distance=0.8,
        max_age=20,
        n_init=2,
        nn_budget=50,
        enable_half_precision=True,
        batch_size=8,
        feature_extract_interval=2,  # 每2帧提取一次特征
        cache_clear_interval=100
    )
    
    return tracker
