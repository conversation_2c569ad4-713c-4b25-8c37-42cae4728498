#!/usr/bin/env python3
"""
测试每秒和每帧的工人计数图表生成
"""

import os
import sys
import json
import pandas as pd
from worker_analysis import WorkerAnalyzer

def create_test_tracks_data():
    """创建测试用的tracks数据"""
    print("创建测试tracks数据...")
    
    # 模拟25秒视频，30 FPS，总共750帧
    tracks_data = []
    
    # 工人1: 全程活跃 (1-750帧)
    for frame in range(1, 751, 3):  # 每3帧记录一次
        tracks_data.append([frame, 1, 100, 200, 50, 100, 0.8, 0, 1])
    
    # 工人2: 中途出现 (150-600帧)
    for frame in range(150, 601, 3):
        tracks_data.append([frame, 2, 200, 300, 50, 100, 0.9, 0, 1])
    
    # 工人3: 早期出现 (1-300帧)
    for frame in range(1, 301, 3):
        tracks_data.append([frame, 3, 300, 400, 50, 100, 0.7, 0, 1])
    
    # 工人4: 后期出现 (400-750帧)
    for frame in range(400, 751, 3):
        tracks_data.append([frame, 4, 400, 500, 50, 100, 0.6, 0, 1])
    
    # 工人5: 短暂出现 (200-250帧)
    for frame in range(200, 251, 3):
        tracks_data.append([frame, 5, 500, 600, 50, 100, 0.5, 0, 1])
    
    # 保存到文件
    tracks_file = "test_tracks_per_second_frame.txt"
    with open(tracks_file, 'w') as f:
        for track in tracks_data:
            f.write(' '.join(map(str, track)) + '\n')
    
    print(f"测试tracks文件已创建: {tracks_file}")
    print(f"包含 {len(tracks_data)} 条跟踪记录")
    print(f"模拟视频: 25秒, 30 FPS, 750帧")
    
    return tracks_file

def test_per_second_frame_charts():
    """测试每秒和每帧图表生成"""
    print("=" * 60)
    print("测试每秒和每帧工人计数图表生成")
    print("=" * 60)
    
    # 创建测试数据
    tracks_file = create_test_tracks_data()
    video_file = "image/测试基准视频.mp4"  # 使用真实视频文件获取FPS信息
    output_dir = "test_per_second_frame_output"
    
    try:
        # 创建分析器实例
        print("\n创建WorkerAnalyzer实例...")
        analyzer = WorkerAnalyzer(tracks_file, video_file, output_dir)
        
        # 手动设置视频信息（模拟25秒视频）
        analyzer.video_info = {
            'fps': 30.0,
            'frame_count': 750,
            'width': 1920,
            'height': 1080,
            'duration': 25.0
        }
        
        print("设置模拟视频信息: 25秒, 30 FPS, 750帧")
        
        # 运行分析
        print("\n开始分析...")
        analyzer.analyze()
        
        print("\n✅ 每秒和每帧图表生成测试成功！")
        
        # 检查生成的文件
        print("\n检查生成的文件:")
        expected_files = [
            "worker_count_per_second.png",
            "worker_count_per_frame.png",
            "worker_stats.json",
            "worker_report.html"
        ]
        
        if os.path.exists(output_dir):
            for file_name in expected_files:
                file_path = os.path.join(output_dir, file_name)
                if os.path.exists(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  ✅ {file_name} - {size} bytes")
                else:
                    print(f"  ❌ {file_name} - 不存在")
            
            # 列出所有生成的文件
            print(f"\n所有生成的文件:")
            all_files = os.listdir(output_dir)
            for file in sorted(all_files):
                file_path = os.path.join(output_dir, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  📄 {file} - {size} bytes")
        else:
            print(f"  ❌ 输出目录不存在: {output_dir}")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        if os.path.exists(tracks_file):
            os.remove(tracks_file)
            print(f"\n清理临时文件: {tracks_file}")

def test_with_existing_data():
    """使用现有数据测试每秒和每帧图表"""
    print("=" * 60)
    print("使用现有数据测试每秒和每帧图表")
    print("=" * 60)
    
    # 使用现有的分析结果
    existing_analysis_dir = "runs/track/yolov5/best_22_osnet_x0_2518/analysis"
    stats_file = os.path.join(existing_analysis_dir, "worker_stats_fixed.json")
    video_file = "image/测试基准视频.mp4"
    output_dir = "test_existing_per_second_frame"
    
    if not os.path.exists(stats_file):
        print(f"❌ 现有统计文件不存在: {stats_file}")
        return
    
    try:
        # 从现有统计数据重建tracks数据
        print("从现有统计数据重建tracks数据...")
        
        with open(stats_file, 'r', encoding='utf-8') as f:
            stats_data = json.load(f)
        
        # 创建模拟的tracks数据
        tracks_data = []
        for worker_id, stats in stats_data.items():
            first_frame = stats['first_frame']
            last_frame = stats['last_frame']
            track_points = stats['track_points']
            
            # 生成该工人的轨迹点
            step = max(1, (last_frame - first_frame) // max(1, track_points - 1)) if track_points > 1 else 1
            for frame in range(first_frame, last_frame + 1, step):
                x = 100 + int(worker_id) * 50
                y = 200 + frame // 10
                tracks_data.append([frame, int(worker_id), x, y, 50, 100, 0.8, 0, 1])
        
        # 保存tracks文件
        tracks_file = "existing_data_tracks.txt"
        with open(tracks_file, 'w') as f:
            for track in tracks_data:
                f.write(' '.join(map(str, track)) + '\n')
        
        print(f"重建的tracks文件已创建: {tracks_file}")
        print(f"包含 {len(tracks_data)} 条跟踪记录")
        
        # 运行分析
        print("\n创建WorkerAnalyzer实例...")
        analyzer = WorkerAnalyzer(tracks_file, video_file, output_dir)
        
        print("\n开始分析...")
        analyzer.analyze()
        
        print("\n✅ 使用现有数据的每秒和每帧图表测试成功！")
        
        # 检查生成的文件
        print("\n检查生成的文件:")
        expected_files = [
            "worker_count_per_second.png",
            "worker_count_per_frame.png"
        ]
        
        if os.path.exists(output_dir):
            for file_name in expected_files:
                file_path = os.path.join(output_dir, file_name)
                if os.path.exists(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  ✅ {file_name} - {size} bytes")
                else:
                    print(f"  ❌ {file_name} - 不存在")
        
        # 清理临时文件
        if os.path.exists(tracks_file):
            os.remove(tracks_file)
            print(f"\n清理临时文件: {tracks_file}")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("🚀 开始测试每秒和每帧工人计数图表功能")
    
    # 测试1: 使用模拟数据
    test_per_second_frame_charts()
    
    print("\n" + "="*60)
    
    # 测试2: 使用现有数据
    test_with_existing_data()
    
    print("\n🎉 所有测试完成！")

if __name__ == '__main__':
    main()
