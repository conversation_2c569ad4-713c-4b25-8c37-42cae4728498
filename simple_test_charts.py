#!/usr/bin/env python3
"""
简单测试每秒和每帧图表生成
"""

import os
import json
from worker_analysis import WorkerAnalyzer

def simple_test():
    """简单测试"""
    print("=" * 60)
    print("简单测试每秒和每帧图表生成")
    print("=" * 60)
    
    # 使用现有的统计数据
    existing_stats = "runs/track/yolov5/best_22_osnet_x0_2518/analysis/worker_stats_fixed.json"
    video_file = "image/测试基准视频.mp4"
    output_dir = "simple_test_output"
    
    if not os.path.exists(existing_stats):
        print(f"❌ 统计文件不存在: {existing_stats}")
        return
    
    try:
        # 从现有统计数据重建简单的tracks数据
        print("从现有统计数据重建tracks数据...")
        
        with open(existing_stats, 'r', encoding='utf-8') as f:
            stats_data = json.load(f)
        
        # 创建简单的tracks数据
        tracks_data = []
        for worker_id, stats in stats_data.items():
            first_frame = stats['first_frame']
            last_frame = stats['last_frame']
            
            # 每10帧生成一个轨迹点
            for frame in range(first_frame, last_frame + 1, 10):
                x = 100 + int(worker_id) * 50
                y = 200
                tracks_data.append([frame, int(worker_id), x, y, 50, 100, 0.8, 0, 1])
        
        # 保存tracks文件
        tracks_file = "simple_test_tracks.txt"
        with open(tracks_file, 'w') as f:
            for track in tracks_data:
                f.write(' '.join(map(str, track)) + '\n')
        
        print(f"简单tracks文件已创建: {tracks_file}")
        print(f"包含 {len(tracks_data)} 条跟踪记录")
        
        # 创建分析器并运行
        print("\n创建WorkerAnalyzer实例...")
        analyzer = WorkerAnalyzer(tracks_file, video_file, output_dir)
        
        print("\n开始分析...")
        analyzer.analyze()
        
        print("\n✅ 简单测试成功！")
        
        # 检查生成的文件
        print("\n检查生成的文件:")
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            for file in sorted(files):
                file_path = os.path.join(output_dir, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    if 'per_second' in file or 'per_frame' in file:
                        print(f"  🎯 {file} - {size} bytes")
                    else:
                        print(f"  📄 {file} - {size} bytes")
        
        # 清理临时文件
        if os.path.exists(tracks_file):
            os.remove(tracks_file)
            print(f"\n清理临时文件: {tracks_file}")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    simple_test()
