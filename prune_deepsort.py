#!/usr/bin/env python3
"""
DeepSORT模型剪枝脚本
用于对ReID特征提取网络进行剪枝以提升推理速度
"""

import torch
import torch.nn as nn
import numpy as np
import time
import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.append('deep_sort/deep/reid')
from torchreid.utils import FeatureExtractor
import torch_pruning as tp

class DeepSORTPruner:
    def __init__(self, model_name="osnet_x0_25", device="cuda"):
        """
        初始化DeepSORT剪枝器
        
        Args:
            model_name: ReID模型名称
            device: 设备类型
        """
        self.model_name = model_name
        self.device = device
        self.original_model = None
        self.pruned_model = None
        
    def load_model(self, model_path=None):
        """加载原始模型"""
        print(f"加载模型: {self.model_name}")
        
        if model_path is None:
            # 使用默认路径
            model_path = f"deep_sort/deep/checkpoint/{self.model_name}.pth"
        
        self.extractor = FeatureExtractor(
            model_name=self.model_name.rsplit('_', 1)[:-1][0] if '_' in self.model_name else self.model_name,
            model_path=model_path,
            device=str(self.device)
        )
        
        self.original_model = self.extractor.model
        print(f"模型加载完成，设备: {self.device}")
        
    def analyze_model(self):
        """分析模型结构和参数量"""
        if self.original_model is None:
            raise ValueError("请先加载模型")
            
        total_params = sum(p.numel() for p in self.original_model.parameters())
        trainable_params = sum(p.numel() for p in self.original_model.parameters() if p.requires_grad)
        
        print(f"\n=== 模型分析 ===")
        print(f"模型名称: {self.model_name}")
        print(f"总参数量: {total_params:,}")
        print(f"可训练参数: {trainable_params:,}")
        print(f"模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
        
        return total_params, trainable_params
    
    def benchmark_model(self, model, num_iterations=100):
        """测试模型推理速度"""
        model.eval()
        
        # 创建测试输入 (batch_size=1, channels=3, height=256, width=128)
        test_input = torch.randn(1, 3, 256, 128).to(self.device)
        
        # 预热
        with torch.no_grad():
            for _ in range(10):
                _ = model(test_input)
        
        # 计时
        torch.cuda.synchronize() if self.device == "cuda" else None
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(num_iterations):
                _ = model(test_input)
                
        torch.cuda.synchronize() if self.device == "cuda" else None
        end_time = time.time()
        
        avg_time = (end_time - start_time) / num_iterations * 1000  # ms
        fps = 1000 / avg_time
        
        return avg_time, fps
    
    def prune_model(self, pruning_ratio=0.3, pruning_method="magnitude"):
        """
        剪枝模型
        
        Args:
            pruning_ratio: 剪枝比例 (0.0-1.0)
            pruning_method: 剪枝方法 ("magnitude", "random", "structured")
        """
        if self.original_model is None:
            raise ValueError("请先加载模型")
            
        print(f"\n=== 开始剪枝 ===")
        print(f"剪枝方法: {pruning_method}")
        print(f"剪枝比例: {pruning_ratio:.1%}")
        
        # 复制模型
        self.pruned_model = torch.nn.utils.deepcopy(self.original_model)
        
        if pruning_method == "magnitude":
            self._magnitude_pruning(pruning_ratio)
        elif pruning_method == "structured":
            self._structured_pruning(pruning_ratio)
        else:
            raise ValueError(f"不支持的剪枝方法: {pruning_method}")
            
        print("剪枝完成")
        
    def _magnitude_pruning(self, pruning_ratio):
        """基于权重大小的非结构化剪枝"""
        for name, module in self.pruned_model.named_modules():
            if isinstance(module, (nn.Conv2d, nn.Linear)):
                # 对卷积层和全连接层进行剪枝
                torch.nn.utils.prune.l1_unstructured(module, name='weight', amount=pruning_ratio)
                # 移除剪枝掩码，使剪枝永久化
                torch.nn.utils.prune.remove(module, 'weight')
                
    def _structured_pruning(self, pruning_ratio):
        """结构化剪枝（通道剪枝）"""
        try:
            # 创建示例输入
            example_inputs = torch.randn(1, 3, 256, 128).to(self.device)
            
            # 创建重要性评估器
            imp = tp.importance.MagnitudeImportance(p=1)
            
            # 忽略某些层
            ignored_layers = []
            for m in self.pruned_model.modules():
                if isinstance(m, (nn.BatchNorm2d, nn.BatchNorm1d)):
                    ignored_layers.append(m)
            
            # 创建剪枝器
            pruner = tp.pruner.MagnitudePruner(
                self.pruned_model,
                example_inputs,
                importance=imp,
                pruning_ratio=pruning_ratio,
                ignored_layers=ignored_layers,
            )
            
            # 执行剪枝
            pruner.step()
            
        except Exception as e:
            print(f"结构化剪枝失败: {e}")
            print("回退到非结构化剪枝")
            self._magnitude_pruning(pruning_ratio)
    
    def compare_models(self):
        """比较原始模型和剪枝模型的性能"""
        if self.original_model is None or self.pruned_model is None:
            raise ValueError("请先加载和剪枝模型")
            
        print(f"\n=== 性能比较 ===")
        
        # 分析参数量
        orig_params = sum(p.numel() for p in self.original_model.parameters())
        pruned_params = sum(p.numel() for p in self.pruned_model.parameters())
        
        # 计算非零参数（对于非结构化剪枝）
        orig_nonzero = sum((p != 0).sum().item() for p in self.original_model.parameters())
        pruned_nonzero = sum((p != 0).sum().item() for p in self.pruned_model.parameters())
        
        print(f"原始模型参数量: {orig_params:,}")
        print(f"剪枝模型参数量: {pruned_params:,}")
        print(f"原始模型非零参数: {orig_nonzero:,}")
        print(f"剪枝模型非零参数: {pruned_nonzero:,}")
        print(f"参数压缩比: {pruned_nonzero/orig_nonzero:.1%}")
        
        # 测试推理速度
        print("\n测试推理速度...")
        orig_time, orig_fps = self.benchmark_model(self.original_model)
        pruned_time, pruned_fps = self.benchmark_model(self.pruned_model)
        
        print(f"原始模型: {orig_time:.2f}ms ({orig_fps:.1f} FPS)")
        print(f"剪枝模型: {pruned_time:.2f}ms ({pruned_fps:.1f} FPS)")
        print(f"速度提升: {pruned_fps/orig_fps:.2f}x")
        
        return {
            'original': {'params': orig_params, 'nonzero': orig_nonzero, 'time': orig_time, 'fps': orig_fps},
            'pruned': {'params': pruned_params, 'nonzero': pruned_nonzero, 'time': pruned_time, 'fps': pruned_fps},
            'compression_ratio': pruned_nonzero/orig_nonzero,
            'speedup': pruned_fps/orig_fps
        }
    
    def save_pruned_model(self, save_path):
        """保存剪枝后的模型"""
        if self.pruned_model is None:
            raise ValueError("没有剪枝模型可保存")
            
        torch.save(self.pruned_model.state_dict(), save_path)
        print(f"剪枝模型已保存到: {save_path}")

def main():
    """主函数"""
    print("DeepSORT模型剪枝工具")
    print("=" * 50)
    
    # 检查设备
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"使用设备: {device}")
    
    # 创建剪枝器
    pruner = DeepSORTPruner(model_name="osnet_x0_25", device=device)
    
    try:
        # 加载模型
        pruner.load_model()
        
        # 分析原始模型
        pruner.analyze_model()
        
        # 测试不同剪枝比例
        pruning_ratios = [0.2, 0.3, 0.5]
        results = []
        
        for ratio in pruning_ratios:
            print(f"\n{'='*20} 剪枝比例: {ratio:.1%} {'='*20}")
            
            # 剪枝模型
            pruner.prune_model(pruning_ratio=ratio, pruning_method="magnitude")
            
            # 比较性能
            result = pruner.compare_models()
            result['pruning_ratio'] = ratio
            results.append(result)
            
            # 保存剪枝模型
            save_path = f"deep_sort/deep/checkpoint/osnet_x0_25_pruned_{ratio:.1f}.pth"
            pruner.save_pruned_model(save_path)
        
        # 总结结果
        print(f"\n{'='*20} 剪枝结果总结 {'='*20}")
        print(f"{'剪枝比例':<10} {'压缩比':<10} {'速度提升':<10} {'推荐':<10}")
        print("-" * 50)
        
        best_ratio = 0.3  # 默认推荐
        best_score = 0
        
        for result in results:
            ratio = result['pruning_ratio']
            compression = result['compression_ratio']
            speedup = result['speedup']
            
            # 计算综合得分（速度提升 * 压缩比）
            score = speedup * (1 - compression)
            if score > best_score:
                best_score = score
                best_ratio = ratio
            
            recommend = "★★★" if ratio == best_ratio else ""
            print(f"{ratio:<10.1%} {compression:<10.1%} {speedup:<10.2f}x {recommend:<10}")
        
        print(f"\n推荐使用剪枝比例: {best_ratio:.1%}")
        print(f"推荐模型路径: deep_sort/deep/checkpoint/osnet_x0_25_pruned_{best_ratio:.1f}.pth")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
