#!/usr/bin/env python3
"""
修复工人数量时间图表生成问题
诊断并重新生成有效的worker_count_over_time.png图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import os
import cv2
import argparse
from pathlib import Path

# 设置matplotlib中文字体支持
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial', 'DejaVu Sans', 'Liberation Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def load_tracks_data(tracks_file):
    """加载跟踪数据"""
    print(f"加载跟踪数据: {tracks_file}")
    
    try:
        # 尝试读取MOT格式的跟踪数据
        tracks_data = pd.read_csv(tracks_file, header=None, 
                                 names=['frame', 'id', 'x', 'y', 'w', 'h', 'conf', 'class', 'visibility'])
        print(f"成功加载 {len(tracks_data)} 条跟踪记录")
        print(f"帧范围: {tracks_data['frame'].min()} - {tracks_data['frame'].max()}")
        print(f"工人ID数量: {tracks_data['id'].nunique()}")
        return tracks_data
    except Exception as e:
        print(f"加载跟踪数据失败: {e}")
        return None

def get_video_info(video_file):
    """获取视频信息"""
    if not video_file or not os.path.exists(video_file):
        print("视频文件不存在，将使用默认参数")
        return None
    
    try:
        cap = cv2.VideoCapture(video_file)
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        cap.release()
        
        video_info = {
            'fps': fps,
            'frame_count': frame_count,
            'width': width,
            'height': height,
            'duration': duration
        }
        
        print(f"视频信息: {width}x{height}, {fps:.2f} FPS, {frame_count} 帧, {duration:.2f} 秒")
        return video_info
    except Exception as e:
        print(f"获取视频信息失败: {e}")
        return None

def generate_worker_count_chart(tracks_data, video_info, output_path, time_interval_seconds=60):
    """生成工人数量随时间变化的图表"""
    print("生成工人数量时间图表...")
    
    if tracks_data is None or len(tracks_data) == 0:
        print("没有跟踪数据，生成空图表")
        plt.figure(figsize=(12, 6))
        plt.plot([0, 1], [0, 0], marker='o', linestyle='-', linewidth=2, markersize=8)
        plt.title('Worker Count Over Time (No Data)', fontsize=16)
        plt.xlabel('Time (minutes)', fontsize=12)
        plt.ylabel('Worker Count', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"空图表已保存到: {output_path}")
        return
    
    # 获取基本信息
    total_frames = tracks_data['frame'].max()
    min_frame = tracks_data['frame'].min()
    
    # 如果有视频信息，使用视频的FPS；否则假设30 FPS
    if video_info and video_info['fps'] > 0:
        fps = video_info['fps']
        total_frames = max(total_frames, video_info['frame_count'])
    else:
        fps = 30.0  # 默认FPS
        print(f"使用默认FPS: {fps}")
    
    print(f"帧范围: {min_frame} - {total_frames}")
    print(f"FPS: {fps}")
    
    # 计算时间间隔（帧数）
    time_interval_frames = int(fps * time_interval_seconds)
    print(f"时间间隔: {time_interval_seconds}秒 = {time_interval_frames}帧")
    
    # 计算时间段数量
    num_intervals = int(np.ceil((total_frames - min_frame + 1) / time_interval_frames))
    print(f"时间段数量: {num_intervals}")
    
    if num_intervals <= 0:
        print("时间段数量无效，生成默认图表")
        plt.figure(figsize=(12, 6))
        plt.plot([0], [0], marker='o', linestyle='-', linewidth=2, markersize=8)
        plt.title('Worker Count Over Time (Invalid Data)', fontsize=16)
        plt.xlabel('Time (minutes)', fontsize=12)
        plt.ylabel('Worker Count', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        return
    
    # 初始化工人数量数组
    worker_counts = np.zeros(num_intervals)
    time_labels = []
    
    # 计算每个时间段的工人数量
    for interval in range(num_intervals):
        start_frame = min_frame + interval * time_interval_frames
        end_frame = min(min_frame + (interval + 1) * time_interval_frames, total_frames + 1)
        
        # 获取该时间段内的跟踪数据
        interval_data = tracks_data[
            (tracks_data['frame'] >= start_frame) & 
            (tracks_data['frame'] < end_frame)
        ]
        
        # 统计不同工人ID的数量
        unique_workers = interval_data['id'].nunique()
        worker_counts[interval] = unique_workers
        
        # 生成时间标签（分钟）
        time_minutes = (start_frame - min_frame) / fps / 60
        time_labels.append(time_minutes)
        
        print(f"时间段 {interval}: 帧 {start_frame}-{end_frame}, 工人数量: {unique_workers}")
    
    print(f"工人数量统计: {worker_counts}")
    print(f"时间标签: {time_labels}")
    
    # 创建图表
    plt.figure(figsize=(12, 6))
    
    # 绘制线图
    if len(time_labels) > 1:
        plt.plot(time_labels, worker_counts, marker='o', linestyle='-', 
                linewidth=2, markersize=8, color='#2E86AB', markerfacecolor='#A23B72')
    else:
        plt.scatter(time_labels, worker_counts, s=100, color='#2E86AB')
    
    # 设置图表属性
    plt.title('Worker Count Over Time', fontsize=16, fontweight='bold')
    plt.xlabel('Time (minutes)', fontsize=12)
    plt.ylabel('Worker Count', fontsize=12)
    plt.grid(True, alpha=0.3)
    
    # 设置Y轴范围
    max_count = max(worker_counts) if len(worker_counts) > 0 else 1
    plt.ylim(-0.5, max_count + 0.5)
    
    # 设置X轴范围
    if len(time_labels) > 1:
        plt.xlim(min(time_labels) - 0.5, max(time_labels) + 0.5)
    
    # 添加数值标签
    for i, (x, y) in enumerate(zip(time_labels, worker_counts)):
        plt.annotate(f'{int(y)}', (x, y), textcoords="offset points", 
                    xytext=(0,10), ha='center', fontsize=10)
    
    # 美化图表
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"工人数量时间图表已保存到: {output_path}")
    
    # 显示统计信息
    print(f"\n图表统计信息:")
    print(f"  总时间段: {num_intervals}")
    print(f"  最大工人数: {int(max(worker_counts))}")
    print(f"  最小工人数: {int(min(worker_counts))}")
    print(f"  平均工人数: {np.mean(worker_counts):.2f}")

def generate_enhanced_chart(tracks_data, video_info, output_path):
    """生成增强版的工人数量图表，包含多种时间粒度"""
    print("生成增强版工人数量图表...")
    
    if tracks_data is None or len(tracks_data) == 0:
        print("没有数据，跳过增强图表生成")
        return
    
    # 创建子图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Worker Count Analysis - Multiple Time Scales', fontsize=16, fontweight='bold')
    
    time_intervals = [30, 60, 120, 300]  # 30秒, 1分钟, 2分钟, 5分钟
    titles = ['30 Seconds', '1 Minute', '2 Minutes', '5 Minutes']
    
    for idx, (interval, title) in enumerate(zip(time_intervals, titles)):
        row = idx // 2
        col = idx % 2
        ax = axes[row, col]
        
        # 生成该时间间隔的数据
        worker_counts, time_labels = calculate_worker_counts(tracks_data, video_info, interval)
        
        if len(time_labels) > 0:
            ax.plot(time_labels, worker_counts, marker='o', linestyle='-', 
                   linewidth=2, markersize=6)
            ax.set_title(f'Worker Count Over Time ({title})', fontweight='bold')
            ax.set_xlabel('Time (minutes)')
            ax.set_ylabel('Worker Count')
            ax.grid(True, alpha=0.3)
            
            # 添加数值标签
            for x, y in zip(time_labels, worker_counts):
                ax.annotate(f'{int(y)}', (x, y), textcoords="offset points", 
                           xytext=(0,5), ha='center', fontsize=8)
    
    plt.tight_layout()
    
    # 保存增强图表
    enhanced_path = output_path.replace('.png', '_enhanced.png')
    plt.savefig(enhanced_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"增强版图表已保存到: {enhanced_path}")

def calculate_worker_counts(tracks_data, video_info, time_interval_seconds):
    """计算指定时间间隔的工人数量"""
    if tracks_data is None or len(tracks_data) == 0:
        return [], []
    
    total_frames = tracks_data['frame'].max()
    min_frame = tracks_data['frame'].min()
    
    if video_info and video_info['fps'] > 0:
        fps = video_info['fps']
    else:
        fps = 30.0
    
    time_interval_frames = int(fps * time_interval_seconds)
    num_intervals = int(np.ceil((total_frames - min_frame + 1) / time_interval_frames))
    
    worker_counts = []
    time_labels = []
    
    for interval in range(num_intervals):
        start_frame = min_frame + interval * time_interval_frames
        end_frame = min(min_frame + (interval + 1) * time_interval_frames, total_frames + 1)
        
        interval_data = tracks_data[
            (tracks_data['frame'] >= start_frame) & 
            (tracks_data['frame'] < end_frame)
        ]
        
        unique_workers = interval_data['id'].nunique()
        worker_counts.append(unique_workers)
        
        time_minutes = (start_frame - min_frame) / fps / 60
        time_labels.append(time_minutes)
    
    return worker_counts, time_labels

def main():
    parser = argparse.ArgumentParser(description='修复工人数量时间图表')
    parser.add_argument('--tracks', type=str, required=True, help='跟踪数据文件路径')
    parser.add_argument('--video', type=str, help='视频文件路径（可选）')
    parser.add_argument('--output', type=str, help='输出图片路径')
    parser.add_argument('--interval', type=int, default=60, help='时间间隔（秒）')
    parser.add_argument('--enhanced', action='store_true', help='生成增强版图表')
    
    args = parser.parse_args()
    
    # 设置默认输出路径
    if not args.output:
        tracks_dir = os.path.dirname(args.tracks)
        args.output = os.path.join(tracks_dir, 'worker_count_over_time_fixed.png')
    
    print("=" * 60)
    print("工人数量时间图表修复工具")
    print("=" * 60)
    
    # 加载数据
    tracks_data = load_tracks_data(args.tracks)
    video_info = get_video_info(args.video)
    
    # 生成图表
    generate_worker_count_chart(tracks_data, video_info, args.output, args.interval)
    
    # 生成增强版图表
    if args.enhanced:
        generate_enhanced_chart(tracks_data, video_info, args.output)
    
    print("=" * 60)
    print("图表生成完成！")
    print("=" * 60)

if __name__ == '__main__':
    main()
