#!/usr/bin/env python3
"""
从视频中提取测试帧用于模型验证
"""

import cv2
import os
import argparse

def extract_frames_from_video(video_path, output_dir, num_frames=10, start_frame=0):
    """
    从视频中提取指定数量的帧
    
    Args:
        video_path: 视频文件路径
        output_dir: 输出目录
        num_frames: 提取的帧数
        start_frame: 开始帧号
    """
    print(f"📹 处理视频: {video_path}")
    
    # 打开视频
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ 无法打开视频: {video_path}")
        return []
    
    # 获取视频信息
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    
    print(f"   总帧数: {total_frames}")
    print(f"   FPS: {fps:.1f}")
    
    # 计算提取间隔
    if total_frames <= num_frames:
        frame_interval = 1
        actual_num_frames = total_frames
    else:
        frame_interval = max(1, (total_frames - start_frame) // num_frames)
        actual_num_frames = num_frames
    
    print(f"   提取间隔: 每 {frame_interval} 帧提取一帧")
    print(f"   实际提取: {actual_num_frames} 帧")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 提取帧
    extracted_files = []
    video_name = os.path.splitext(os.path.basename(video_path))[0]
    
    frame_count = 0
    extracted_count = 0
    
    while cap.isOpened() and extracted_count < actual_num_frames:
        ret, frame = cap.read()
        if not ret:
            break
        
        # 检查是否需要提取这一帧
        if frame_count >= start_frame and (frame_count - start_frame) % frame_interval == 0:
            # 保存帧
            frame_filename = f"{video_name}_frame_{frame_count:06d}.jpg"
            frame_path = os.path.join(output_dir, frame_filename)
            
            cv2.imwrite(frame_path, frame)
            extracted_files.append(frame_path)
            extracted_count += 1
            
            print(f"   ✅ 提取帧 {frame_count}: {frame_filename}")
        
        frame_count += 1
    
    cap.release()
    print(f"   📁 提取完成: {len(extracted_files)} 个文件")
    
    return extracted_files

def main():
    parser = argparse.ArgumentParser(description='从视频中提取测试帧')
    parser.add_argument('--video_dir', type=str, default='image',
                       help='视频目录')
    parser.add_argument('--output_dir', type=str, default='test_frames',
                       help='输出目录')
    parser.add_argument('--num_frames', type=int, default=10,
                       help='每个视频提取的帧数')
    parser.add_argument('--start_frame', type=int, default=30,
                       help='开始提取的帧号')
    
    args = parser.parse_args()
    
    print("🎬 视频帧提取工具")
    print("=" * 50)
    
    # 查找视频文件
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv']
    video_files = []
    
    if os.path.exists(args.video_dir):
        for file in os.listdir(args.video_dir):
            if any(file.lower().endswith(ext) for ext in video_extensions):
                video_files.append(os.path.join(args.video_dir, file))
    
    if not video_files:
        print(f"❌ 在 {args.video_dir} 中没有找到视频文件")
        return
    
    print(f"📹 找到 {len(video_files)} 个视频文件:")
    for video_file in video_files:
        print(f"   - {video_file}")
    
    # 提取帧
    all_extracted_files = []
    
    for video_file in video_files:
        try:
            extracted_files = extract_frames_from_video(
                video_file, 
                args.output_dir, 
                args.num_frames, 
                args.start_frame
            )
            all_extracted_files.extend(extracted_files)
        except Exception as e:
            print(f"❌ 处理视频失败 {video_file}: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎉 提取完成!")
    print(f"总共提取: {len(all_extracted_files)} 个测试帧")
    print(f"保存目录: {args.output_dir}")
    
    if all_extracted_files:
        print(f"\n💡 现在可以运行模型验证:")
        print(f"python validate_pruned_models.py --images {args.output_dir}")

if __name__ == '__main__':
    main()
